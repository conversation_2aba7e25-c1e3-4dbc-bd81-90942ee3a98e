<?php

namespace App\Livewire;

use App\Models\Bank;
use App\Models\VietQrStore;
use App\Services\VietQR;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Auth;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;

class QrGenerate extends Component
{
    use WithPagination;

    public $banks;
    public $bank_bin = 970436;
    public $account_no;
    public $account_name;
    public $frame_id = 0;
    public $is_mask;
    public $amount;
    public $memo;
    public $base64Result = null;
    public $quickLink = '';

    public function messages()
    {
        return [
            'bank_bin.required' => 'Trường bắt buộc chọn.',
            'account_no.required' => 'Trường bắt buộc nhập, chỉ nhập số, tiếng Việt không dấu, <PERSON>hông chứa các ký tự đặc biệt, tối đa 19 ký tự.',
            'account_no.regex' => 'Trường bắt buộc nhập, chỉ nhập số, tiếng Việt không dấu, Không chứa các ký tự đặc biệt, tối đa 19 ký tự.',
            'account_no.max' => 'Trường bắt buộc nhập, chỉ nhập số, tiếng Việt không dấu, Không chứa các ký tự đặc biệt, tối đa 19 ký tự.',
            'account_name.required' => 'Trường bắt buộc nhập, nhập tiếng Việt không dấu, viết hoa. Không chứa các ký tự đặc biệt, tối đa 50 ký tự.',
            'account_name.max' => 'Trường bắt buộc nhập, nhập tiếng Việt không dấu, viết hoa. Không chứa các ký tự đặc biệt, tối đa 50 ký tự.',
            'account_name.regex' => 'Trường bắt buộc nhập, nhập tiếng Việt không dấu, viết hoa. Không chứa các ký tự đặc biệt, tối đa 50 ký tự.',
            'memo.max' => 'Nhập tiếng Việt không dấu, viết hoa. Không chứa các ký tự đặc biệt, tối đa 25 ký tự.',
        ];
    }

    public function generate()
    {
        $validated = $this->validate([
            'bank_bin' => 'required',
            'account_no' => ['required', 'max:19', 'regex:/^[A-Z0-9]*$/'],
            'account_name' => ['required', 'max:50', 'regex:/^[A-Z ]*$/'],
            'frame_id' => 'nullable|in:0',
            'is_mask' => 'nullable|boolean',
            'memo' => 'nullable|string|max:25',
            'amount' => 'nullable|numeric',
        ]);
        $rs = VietQR::get_string($validated);

        $qrCode = QrCode::create($rs)
            ->setEncoding(new Encoding('UTF-8'))
            ->setSize(450)
            ->setMargin(10);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);

        $bank = Bank::where('bin', $this->bank_bin)->first();

        if ($this->frame_id != '') {
            $manager = new ImageManager(
                new Driver()
            );
            $qr = $manager->read($result->getDataUri());

            $file_logo = $manager->read(public_path('assets/media/logo_banks/' . $bank->code . '.png'))
                ->scale(290);
            $allpay_logo = $manager->read(public_path('assets/media/logos/logo-green2.png'))
                ->scale(270);
            $str_bank = $this->account_no;
            if (isset($this->is_mask)) {
                $str_bank = $this->stringToSecret($str_bank);
            }

            $frame = @imagecreatefrompng(public_path('assets/media/frames/bg-' . $this->frame_id . '.png'));
            $img = $manager
                ->read($frame)
                ->place($file_logo, 'center', 0, 290)
                ->place($allpay_logo, 'top-left', 160, 100)
                ->place($qr, 'center', 0, -45)
                ->text('Chủ tài khoản: ' . strtoupper($this->account_name), 424, 930, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM Avo.ttf'));
                    $font->size(30);
                    $font->color('#1e427e');
                    $font->align('center');
                })
                ->text('Số tài khoản: ' . $str_bank, 424, 975, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM AvoBold.ttf'));
                    $font->size(30);
                    $font->color('#1e427e');
                    $font->align('center');
                })
                ->text($bank->name, 424, 1020, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM Avo.ttf'));
                    $font->size(25);
                    $font->color('#1e427e');
                    $font->align('center');
                });
            $this->base64Result = $img->toPng()->toDataUri();
            $this->quickLink = route('quicklink', ['bank_code' => $bank->code, 'account_no' => $this->account_no, 'account_name' => $this->account_name, 'amount' => $this->amount, 'memo' => $this->memo, 'is_mask' => $this->is_mask, 'bg' => $this->frame_id]);
        } else {
            $this->base64Result = $result->getDataUri();
            $this->quickLink = route('quicklink', ['bank_code' => $bank->code, 'account_no' => $this->account_no, 'account_name' => $this->account_name]);
        }
    }

    public function saveQR()
    {
        $validated = $this->validate([
            'bank_bin' => 'required',
            'account_no' => ['required', 'max:19', 'regex:/^[A-Z0-9]*$/'],
            'account_name' => ['required', 'max:50', 'regex:/^[A-Z ]*$/'],
            'frame_id' => 'nullable|in:0',
            'is_mask' => 'nullable|boolean',
            'memo' => 'nullable|string|max:25',
            'amount' => 'nullable|numeric',
        ]);
        do {
            $slug = Str::random(10);
            $exists = VietQrStore::where('slug', $slug)->first();
        } while ($exists);

        $validated['slug'] = Str::random(10);
        $validated['user_id'] = Auth::user()->id;

        VietQrStore::create($validated);

        $this->dispatch('notifySuccess', ['msg' => 'Lưu mã QR thành công!']);
    }

    private function stringToSecret(string $string = NULL)
    {
        if (!$string) {
            return NULL;
        }
        $length = strlen($string);
        $visibleCount = (int) round($length / 4);
        $hiddenCount = $length - ($visibleCount * 2);
        return substr($string, 0, $visibleCount) . str_repeat('*', $hiddenCount) . substr($string, ($visibleCount * -1), $visibleCount);
    }

    public function render()
    {
        $this->banks = Bank::all();
        $qrStores = VietQrStore::where('user_id', Auth::user()->id)->paginate(10);
        return view('livewire.qr-generate', compact('qrStores'));
    }

    public function destroy($id)
    {
        $vietQrStore = VietQrStore::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if ($vietQrStore) {
            $vietQrStore->delete();
        }
    }
}
