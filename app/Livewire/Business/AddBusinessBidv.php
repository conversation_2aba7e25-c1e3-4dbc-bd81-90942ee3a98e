<?php

namespace App\Livewire\Business;

use App\Models\BankAccount;
use App\Models\BIDVBusiness;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AddBusinessBidv extends Component
{
    public $account_no;
    public $account_holder_name;
    public $first_va_number;
    public $hint_name;

    public function save()
    {
        $validated = $this->validate([
            'account_no' => 'required|max:250',
            'account_holder_name' => 'required|max:250',
            'first_va_number' => 'required|max:250',
            'hint_name' => 'nullable|max:250',
        ]);
        $validated['bank_code'] = 'bidv';
        $validated['is_business'] = true;
        $validated['open_api'] = true;
        $validated['user_id'] = Auth::user()->id;

        BankAccount::create($validated);

        $this->redirectRoute('bank.accounts.business.bidv');
    }

    public function render()
    {
        return view('livewire.business.add-business-bidv');
    }
}
