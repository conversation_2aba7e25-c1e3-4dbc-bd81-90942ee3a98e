<?php

namespace App\Livewire\Business;

use App\Models\BankAccount;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ListBusinessBidv extends Component
{
    public $accounts;
    public function mount()
    {
        $this->accounts = BankAccount::where('user_id', Auth::user()->id)
            ->business()->bidv()->get();
    }
    public function render()
    {
        return view('livewire.business.list-business-bidv');
    }
}
