<?php

namespace App\Livewire;

use App\Models\BankAccount;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ListAcb extends Component
{
    public $accounts;

    public function destroy($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->acb()->where('id', $id)->first();
        if ($bank) {
            $bank->transactions()->delete();
            $bank->hooks()->delete();
            $bank->delete();
        }
    }

    public function toggle($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if ($bank) {
            $bank->status = !$bank->status;
            $bank->save();
        }
    }

    public function generateToken($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->acb()->where('id', $id)->first();
        if ($bank) {
            $bank->api_token = md5($bank->username . time() . rand(1, 9999));
            $bank->save();
        }
    }

    public function render()
    {
        $this->accounts = BankAccount::where('user_id', Auth::user()->id)->acb()->get();

        return view('livewire.list-acb');
    }
}
