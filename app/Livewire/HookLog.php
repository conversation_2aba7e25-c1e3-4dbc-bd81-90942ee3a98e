<?php

namespace App\Livewire;

use App\Models\HookLog as ModelsHookLog;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class HookLog extends Component
{
    use WithPagination;
    public function render()
    {
        $logs = ModelsHookLog::where('user_id', Auth::user()->id)->orderBy('created_at', 'desc')->paginate(10);
        return view('livewire.hook-log', compact('logs'));
    }
}
