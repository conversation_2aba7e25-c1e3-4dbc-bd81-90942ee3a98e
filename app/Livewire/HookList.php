<?php

namespace App\Livewire;

use App\Models\Hook;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class HookList extends Component
{
    public function toggle($id)
    {
        $hook = Hook::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if ($hook) {
            $hook->status = !$hook->status;
            $hook->save();
        }
    }

    public function destroy($id)
    {
        $hook = Hook::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if ($hook) {
            $hook->logs()->delete();
            $hook->delete();
        }
    }

    public function render()
    {
        $hooks = Hook::where('user_id', Auth::user()->id)->paginate(10);

        return view('livewire.hook-list', compact('hooks'));
    }
}
