<?php

namespace App\Livewire;

use App\Models\BankAccount;
use App\Services\MBOpenAPIService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AddMBBank extends Component
{
    public $username = '';
    public $password = '';
    public $account_no = '';
    public $otp = '';
    public $step = 1;
    public $progress = 50;
    public $is_loading = false;
    public $api_type = 1;

    public $open_api_step = 1;
    public $open_api_progress = 33;
    public $accountName = '';
    public $accountNumber = '';
    public $transType = 'DC';
    public $nationalId = '';
    public $phoneNumber = '';
    public $requestId;

    protected $mbOpenAPIService;

    public function __construct()
    {
        $this->mbOpenAPIService = new MBOpenAPIService();
    }


    public function updatedAccountNumber()
    {
        if ($this->accountNumber) {
            $result = $this->mbOpenAPIService->getAccountName($this->accountNumber);
            if ($result && isset($result['errorCode']) && $result['errorCode'] == '000') {
                $this->accountName = $result['data']['accountName'];
                $this->resetErrorBag('accountNumber');
            } elseif ($result['errorCode'] == '200') {
                $this->addError('accountNumber', 'Số tài khoản chưa được đăng ký trên hệ thống MBBank.');
            }
        }
    }

    public function openAPICheck()
    {
        $validated = $this->validate([
            'accountNumber' => ['required', 'unique:bank_accounts,account_no'],
            'accountName' => ['required'],
            'nationalId' => ['required'],
            'phoneNumber' => ['required'],
            'transType' => ['required', 'in:D,C,DC'],
        ]);
        $result = $this->mbOpenAPIService->bdsdSubscribeRequest($validated);
        if ($result && isset($result['data']['requestId'])) {
            $this->open_api_step = 2;
            $this->open_api_progress = 66;
            $this->requestId = $result['data']['requestId'];
            $this->dispatch('notifySuccess', ['msg' => 'Đã gửi OTP']);
        } elseif (isset($result['soaErrorCode'])) {
            if ($result['soaErrorCode'] == '1020') {
                $this->addError('accountNumber', 'Số tài khoản chưa được đăng ký trên hệ thống MBBank.');
            } elseif ($result['soaErrorCode'] == '293') {
                $this->addError('nationalId', 'Số CCCD/CMT chưa được đăng ký tài khoản trên tại MB.');
            } elseif ($result['soaErrorCode'] == '219') {
                $this->addError('phoneNumber', 'Số điện thoại chưa được đăng ký cho tài khoản trên tại MB.');
            } elseif ($result['soaErrorCode'] == '40600') {
                $this->addError('accountName', 'Tên chủ tài khoản không khớp.');
            } elseif ($result['soaErrorCode'] == '40504') {
                $this->dispatch('notifyError', ['msg' => 'Tài khoản này đã được liên kết.']);
            } elseif ($result['soaErrorCode'] == '410') {
                $this->addError('nationalId', 'Số CCCD/CMT chưa được đăng ký tài khoản trên tại MB.');
                $this->addError('phoneNumber', 'Số điện thoại chưa được đăng ký cho tài khoản trên tại MB.');
            } else {
                $this->dispatch('notifyError', ['msg' => $result['soaErrorDesc']]);
            }
            return;
        }
    }

    public function inputOTP()
    {
        $validated = $this->validate([
            'otp' => ['required'],
        ]);

        $result = $this->mbOpenAPIService->bdsdSubscribeConfirm($validated['otp'], $this->requestId);
        if ($result && isset($result['errorCode']) && $result['errorCode'] == '000') {
            BankAccount::create([
                'bank_code' => 'mb',
                'user_id' => Auth::user()->id,
                'account_no' => $this->accountNumber,
                'account_holder_name' => $this->accountName,
                'phone_number' => $this->phoneNumber,
                'open_api' => true,
            ]);
            $this->open_api_step = 3;
            $this->open_api_progress = 100;
        } elseif (isset($result['soaErrorCode'])) {
            if ($result['soaErrorCode'] == '40507') {
                $this->addError('otp', 'OTP không đúng hoặc đã hết hạn.');
            } elseif ($result['soaErrorCode'] == '40504') {
                $this->dispatch('notifyError', ['msg' => 'Tài khoản này đã được liên kết.']);
            } else {
                $this->dispatch('notifyError', ['msg' => $result['soaErrorDesc']]);
            }
            return;
        }
    }

    public function save()
    {
        $validated = $this->validate([
            'username' => ['required', Rule::unique('bank_accounts')->where(function ($query) {
                return $query->where('bank_code', 'mb');
            })],
            'password' => 'required',
            'account_no' => 'required',
        ]);
        $mbClient = mbank();
        $load_info = $mbClient->loadByUsername($this->username, $this->account_no);
        $result = $load_info->loginAuth($this->password);
        if ($result) {
            if (isset($result['success']) && $result['success']) {
                $this->step = 2;
                $this->progress = 100;
            } else {
                $this->dispatch('notifyError', ['msg' => $result['message']]);
            }
        }
    }

    public function render()
    {
        return view('livewire.add-m-b-bank');
    }
}
