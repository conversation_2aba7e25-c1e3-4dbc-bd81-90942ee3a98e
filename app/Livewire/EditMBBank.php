<?php

namespace App\Livewire;

use App\Models\BankAccount;
use Livewire\Component;

class EditMBBank extends Component
{
    public BankAccount $bank;
    public $username = '';

    public $password = '';

    public $account_no = '';

    public $otp = '';

    public $step = 1;

    public $progress = 50;

    public $is_loading = false;

    public function mount(BankAccount $bank)
    {
        $this->username = $bank->username;
        $this->account_no = $bank->account_no;
    }

    public function save()
    {
        $validated = $this->validate([
            'username' => 'required',
            'password' => 'required',
            'account_no' => 'required',
        ]);
        $mbClient = mbank();
        $load_info = $mbClient->loadByAccount($this->bank, $this->username, $this->password, $this->account_no);
        $result = $load_info->loginAuth();
        if ($result) {
            if (isset($result['success']) && $result['success']) {
                $this->step = 2;
                $this->progress = 100;
            } else {

                $this->dispatch('notifyError', ['msg' => $result['message']]);
            }
        }
    }

    public function render()
    {
        return view('livewire.edit-m-b-bank');
    }
}
