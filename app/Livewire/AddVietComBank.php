<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AddVietComBank extends Component
{

    public $username = '';

    public $password = '';

    public $account_no = '';

    public $otp = '';

    public $step = 1;

    public $progress = 33;

    public $is_loading = false;

    public $allow_login_browser = null;

    public function save()
    {
        $validated = $this->validate([
            'username' => ['required', Rule::unique('bank_accounts')->where(function ($query) {
                return $query->where('bank_code', 'vcb');
            })],
            'password' => 'required',
            'account_no' => 'required',
        ]);
        $result = vietcombank()->loadByPhone($validated['username'], $validated['account_no'])->loginAuthNew($validated['password']);
        if ($result) {
            if (!$result->success) {
                if (isset($result->allow_browser) && $result->allow_browser == false) {
                    $this->allow_login_browser = "Đăng nhập ứng dụng VCB Digibank >> Cài đặt >> Quản lý kênh đăng nhập: \n1. Bật “Cho phép đăng nhập VCB Digibank trên trình duyệt web”\n2.TẮT “Xác thực đăng nhập VCB Digibank trên trình duyệt web”.\nChúng tôi xin lỗi vì các bất tiện (nếu có) đối với Quý khách.";
                } else {
                    $this->dispatch('notifyError', ['msg' => $result->message]);
                }
            } else {
                if ($result->request_otp) {
                    $this->step = 2;
                    $this->progress = 66;
                    $this->dispatch(
                        'notifyError',
                        [
                            'msg' => 'Vui lòng kiểm tra tin nhắn từ Vietcombank sau đó nhập OTP vào ô bên dưới để tiến hành lưu trình duyệt'
                        ]
                    );
                } else {
                    $this->step = 3;
                    $this->progress = 100;
                }
            }
        }
    }

    public function inputOTP()
    {
        $validated = $this->validate([
            'otp' => 'required',
        ]);

        $loadUser = vietcombank()->loadByPhone($this->username, $this->account_no);
        $confirmOTP = $loadUser->submitOtpLogin($this->otp, 1);
        if ($confirmOTP->code == 00) {
            $dataSave = [
                "sessionId" => $confirmOTP->sessionId,
                "mobileId" => $confirmOTP->userInfo->mobileId,
                "clientId" => $confirmOTP->userInfo->clientId,
                "cif" => $confirmOTP->userInfo->cif
            ];
            $sv = $loadUser->saveBrowser($dataSave);
            if ($sv && $sv->code == 00) {
                $this->step = 3;
                $this->progress = 100;
            } else if ($sv && $sv->code == '030' && $sv->mid == '3009') {
                $this->dispatch('notifyError', ['msg' => 'Bạn đã lưu quá 5 trình duyệt cho phép, vui lòng liên hệ Hotline ********** của Vietcombank để xoá các trình duyệt đã lưu']);
            } else {
                $this->dispatch('notifyError', ['msg' => 'Lỗi lưu trình duyệt. Vui lòng liên hệ bộ phận kỹ thuật.']);
            }
        } else {
            $this->dispatch('notifyError', ['msg' => 'Đã hết thời gian hiệu lực của giao dịch. Quý khách vui lòng khởi tạo lại']);
        }
    }

    public function resendOTP()
    {
        $loadUser = vietcombank()->loadByPhone($this->username, $this->account_no);
        $resultInitLoginNewBrowser = $loadUser->initLoginNewBrowser();
        if (isset($resultInitLoginNewBrowser->transaction->tranId)) {
            $loadUser->setTranId($resultInitLoginNewBrowser->transaction->tranId);
            $requestOtp = $loadUser->chooseOtpType($resultInitLoginNewBrowser->transaction->tranId, 1);
            if ($requestOtp->code == '00') {
                $this->dispatch('notifySuccess', ['msg' => 'Đã gửi lại OTP']);
            } else {
                $this->dispatch('notifyError', ['msg' => 'Error chooseOtpType']);
            }
        } else {
            $this->dispatch('notifyError', ['msg' => 'Error initLoginNewBrowser']);
        }
    }

    public function render()
    {
        return view('livewire.add-viet-com-bank');
    }
}
