<?php

namespace App\Livewire;

use App\Models\BankAccount;
use App\Models\BankTransaction;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class TransactionReport extends Component
{
    use WithPagination;

    public $accounts;
    public $bank_account_id = '';
    public $search = '';

    public $sumAmountIn = 0;
    public $sumAmountOut = 0;


    public function render()
    {
        $this->accounts = BankAccount::where('user_id', Auth::user()->id)->get();
        $transactions = BankTransaction::with(['bankAccount'])
            ->where('user_id', Auth::user()->id)
            ->when($this->bank_account_id != '', function ($query) {
                $query->where('bank_account_id', $this->bank_account_id);
            })
            ->when($this->search != '', function ($query) {
                $query->where(function ($q) {
                    $q->orWhere('memo', 'like', '%' . $this->search . '%')
                        ->orWhere('transaction_id', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy('actual_at', 'desc')
            ->paginate(10);

        $this->sumAmountIn = BankTransaction::where('user_id', Auth::user()->id)
            ->whereMonth('actual_at', now())->where('type', 1)->get()->sum('amount');

        $this->sumAmountOut = BankTransaction::where('user_id', Auth::user()->id)
            ->whereMonth('actual_at', now())->where('type', 2)->get()->sum('amount');


        return view('livewire.transaction-report', compact('transactions'));
    }
}
