<?php

namespace App\Livewire;

use App\Models\DepositHistory as ModelsDepositHistory;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class DepositHistory extends Component
{
    public function render()
    {
        $transactions = ModelsDepositHistory::where('user_id', Auth::user()->id)
            // ->when($this->search != '', function ($query) {
            //     $query->where(function ($q) {
            //         $q->orWhere('memo', 'like', '%' . $this->search . '%')
            //             ->orWhere('transaction_id', 'like', '%' . $this->search . '%');
            //     });
            // })
            ->orderBy('created_at', 'desc')
            ->paginate(10);


        return view('livewire.deposit-history', compact('transactions'));
    }
}
