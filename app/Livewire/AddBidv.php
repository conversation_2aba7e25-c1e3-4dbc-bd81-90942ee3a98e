<?php

namespace App\Livewire;

use App\Bank\Bidv;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AddBidv extends Component
{
    public $username = '';

    public $password = '';

    public $account_no = '';

    public $otp = '';

    public $step = 1;

    public $progress = 33;

    public $is_loading = false;

    public $allow_login_browser = null;

    public function save()
    {
        $validated = $this->validate([
            'username' => ['required', Rule::unique('bank_accounts')->where(function ($query) {
                return $query->where('bank_code', 'bidv');
            })],
            'password' => 'required',
            'account_no' => 'required',
        ]);
        $bidvClient = new Bidv($this->username, $this->password, $this->account_no);
        $bidvClient = $bidvClient->loadData();
        $login = $bidvClient->doLogin();

        if ($login) {
            if (!$login->success) {
                $this->dispatch('notifyError', ['msg' => $login->message]);
            } else {
                if ($login->request_otp) {
                    $this->step = 2;
                    $this->progress = 66;
                    $this->dispatch(
                        'notifyError',
                        [
                            'msg' => 'Mã OTP đã được gửi đến số điện thoại của bạn'
                        ]
                    );
                } else {
                    $this->step = 3;
                    $this->progress = 100;
                }
            }
        }
    }

    public function inputOTP()
    {
        $validated = $this->validate([
            'otp' => 'required',
        ]);

        $bidvClient = new Bidv($this->username, $this->password, $this->account_no);
        $bidvClient = $bidvClient->loadData();
        $verifyOTP = $bidvClient->verifyOTP($this->otp);

        if ($verifyOTP['status']) {
            $this->step = 3;
            $this->progress = 100;
        } else {
            $this->dispatch('notifyError', ['msg' => $verifyOTP['message']]);
        }
    }

    public function resendOTP()
    {
        $bidvClient = new Bidv($this->username, $this->password, $this->account_no);
        $bidvClient = $bidvClient->loadData();
        $login = $bidvClient->doLogin();
        if ($login) {
            if (!$login->success) {
                $this->dispatch('notifyError', ['msg' => $login->message]);
            } else {
                if ($login->request_otp) {
                    $this->dispatch('notifySuccess', ['msg' => 'Đã gửi lại OTP']);
                } else {
                    $this->dispatch('notifyError', ['msg' => 'Có thể bạn đã xác thực OTP trước đó, vui lòng liên kết lại từ đầu']);
                }
            }
        }
    }

    public function render()
    {
        return view('livewire.add-bidv');
    }
}
