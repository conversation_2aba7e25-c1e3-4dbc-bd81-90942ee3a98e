<?php

namespace App\Livewire;

use App\Bank\Acb;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AddAcb extends Component
{

    public $username = '';

    public $password = '';

    public $account_no = '';

    public $otp = '';

    public $step = 1;

    public $progress = 50;

    public $is_loading = false;

    public function save()
    {
        $validated = $this->validate([
            'username' => ['required', Rule::unique('bank_accounts')->where(function ($query) {
                return $query->where('bank_code', 'acb');
            })],
            'password' => 'required',
            'account_no' => 'required',
        ]);
        $acb = new Acb();
        $load_info = $acb->loadByUsername($this->username, $this->password, $this->account_no);
        $result = $load_info->login();
        if ($result) {
            if (isset($result['status']) && $result['status']) {
                $this->step = 2;
                $this->progress = 100;
            } else {

                $this->dispatch('notifyError', ['msg' => $result['message']]);
            }
        }
    }

    public function render()
    {
        return view('livewire.add-acb');
    }
}
