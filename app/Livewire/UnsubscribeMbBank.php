<?php

namespace App\Livewire;

use App\Models\BankAccount;
use App\Services\MBOpenAPIService;
use Livewire\Component;

class UnsubscribeMbBank extends Component
{
    public BankAccount $bank;

    public $open_api_step = 1;
    public $open_api_progress = 50;
    public $otp;
    public $requestId;

    protected $mbOpenAPIService;

    public function __construct()
    {
        $this->mbOpenAPIService = new MBOpenAPIService();
    }

    public function sendOTP()
    {
        $result = $this->mbOpenAPIService->bdsdUnSubscribeRequest($this->bank->account_no);
        if ($result && isset($result['errorCode']) && $result['errorCode'] == '000') {
            $this->requestId = $result['data']['requestId'];
            $this->dispatch('notifySuccess', ['msg' => 'Đã gửi OTP']);
        } elseif (isset($result['soaErrorCode'])) {
            abort(404);
        }
    }

    public function unsubscribe()
    {
        $validated = $this->validate([
            'otp' => ['required'],
        ]);

        $result = $this->mbOpenAPIService->bdsdUnSubscribeConfirm($validated['otp'], $this->requestId);
        if ($result && isset($result['errorCode']) && $result['errorCode'] == '000') {
            $this->open_api_step = 2;
            $this->open_api_progress = 100;
            $this->bank->transactions()->delete();
            $this->bank->hooks()->delete();
            $this->bank->delete();
        } elseif (isset($result['soaErrorCode'])) {
            if ($result['soaErrorCode'] == '40507') {
                $this->addError('otp', 'OTP không đúng hoặc đã hết hạn.');
            } else {
                $this->dispatch('notifyError', ['msg' => $result['soaErrorDesc']]);
            }
            return;
        }
    }

    public function mount()
    {
        $this->sendOTP();
    }

    public function render()
    {
        return view('livewire.unsubscribe-mb-bank');
    }
}
