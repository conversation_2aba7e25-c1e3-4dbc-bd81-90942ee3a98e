<?php

namespace App\Livewire\VaNumber;

use App\Models\BIDVVaNumber;
use App\Services\BIDVOpenAPIService;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Illuminate\Support\Str;

class AddVaNumberBidv extends Component
{
    public $first_va_number;
    public $bank_id;
    public $service_id;
    public $va_number;
    public $va_name;

    public function mount($first_va_number, $bank_id, $service_id)
    {
        $this->first_va_number = $first_va_number;
        $this->bank_id = $bank_id;
        $this->service_id = $service_id;
    }

    public function generateVaNumber()
    {
        $this->va_number = strtoupper(Str::random(5));
    }

    public function save()
    {
        $validated = $this->validate([
            'va_number' => ['required', 'max:5', 'regex:/^[A-Z0-9]*$/'],
            'va_name' => ['required', 'max:50', 'regex:/^[A-Z0-9 ]*$/'],
        ]);
        $exists = BIDVVaNumber::where('va_number', $this->first_va_number . $this->va_number)->first();
        if ($exists) {
            $this->addError('va_number', ['msg' => 'Số VA đã tồn tại']);
            return;
        }

        $validated['bank_account_id'] = $this->bank_id;
        $validated['va_number'] = $this->first_va_number . $this->va_number;
        $bidvService = new BIDVOpenAPIService();
        $resultVa = $bidvService->genVietQR([
            'serviceId' => $this->service_id,
            'code' => $this->va_number,
            'name' => $this->va_name,
            'serviceCode' => $this->first_va_number,
        ]);
        Log::debug($resultVa);
        if (!$resultVa) {
            return;
        }
        if (isset($resultVa['errorCode']) && $resultVa['errorCode'] == '000' && isset($resultVa['vietQR'])) {
            BIDVVaNumber::create($validated);
            $this->redirectRoute('detail.business.bidv', ['id' => $this->bank_id]);
        } else {
            $this->dispatch('notifyError', ['msg' => $resultVa['errorDesc']]);
        }
    }

    public function render()
    {
        return view('livewire.va-number.add-va-number-bidv');
    }
}
