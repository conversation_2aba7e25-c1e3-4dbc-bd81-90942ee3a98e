<?php

namespace App\Livewire;

use App\Models\DepositHistory;
use App\Models\Package;
use App\Models\UserPackage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class UpgradePlan extends Component
{
    public $selectPackageId;
    public $package;
    public $cost;
    public $newStartAt;
    public $newEndAt;

    public function back()
    {
        $this->reset();
    }

    public function select($packageId)
    {
        $this->selectPackageId = $packageId;
        $this->package = Package::find($packageId);
        $user = Auth::user();
        $this->cost = calculationDiffPricePackage($user->currentPackage, $this->package);
        if ($this->cost == 0) {
            $this->dispatch(
                'notifyError',
                [
                    'msg' => 'Lỗi hệ thống'
                ]
            );
            $this->reset();
            return;
        }
        $this->newStartAt = $user->currentPackage->created_at;
        $this->newEndAt = $user->currentPackage->expire_at;
    }

    public function upgrade()
    {
        $package = $this->package;
        $user = Auth::user();

        abort_if(!$package, 404);
        if ($package->price <= $user->currentPackage->package->price || $this->cost == 0) {
            $this->dispatch(
                'notifyError',
                [
                    'msg' => 'Lỗi hệ thống'
                ]
            );
            return;
        }
        if ($user->coin < $this->cost) {
            $this->dispatch(
                'notifyError',
                [
                    'msg' => 'Số dư không đủ!'
                ]
            );
            return;
        } else {
            try {
                DB::beginTransaction();
                if ($user->currentPackage->package->price == 0) {
                    UserPackage::create([
                        'user_id' => $user->id,
                        'package_id' => $package->id,
                        'start_at' => now(),
                        'end_at' => now()->addMonth(),
                        'expire_at' => now()->addMonth(),
                    ]);
                } else {
                    UserPackage::create([
                        'user_id' => $user->id,
                        'package_id' => $package->id,
                        'start_at' => $user->currentPackage->start_at,
                        'end_at' => $user->currentPackage->end_at,
                        'expire_at' => $user->currentPackage->expire_at,
                    ]);
                }

                $user->currentPackage->delete();
                $user->coin -=  $this->cost;
                $user->save();
                DepositHistory::create([
                    'user_id' => $user->id,
                    'amount' => $this->cost,
                    'description' => 'Nâng cấp gói sử dụng',
                    'ref' => 'UP-' . now()->format('dmYHisu'),
                    'type' => 2,
                ]);
                DB::commit();
                $this->dispatch(
                    'notifySuccess',
                    [
                        'msg' => 'Nâng cấp gói thành công'
                    ]
                );
                $this->redirectRoute('dashboard');
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e);
                $this->dispatch(
                    'notifyError',
                    [
                        'msg' => 'Lỗi hệ thống'
                    ]
                );
            }
        }
    }

    public function render()
    {
        $plans = Package::orderBy('price', 'asc')->get();

        return view('livewire.upgrade-plan', compact('plans'));
    }
}
