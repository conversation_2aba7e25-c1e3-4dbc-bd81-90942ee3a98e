<?php

namespace App\Livewire;

use Livewire\Component;

class PayCodeStructure extends Component
{
    public $payCodes = [];
    public function mount()
    {
        $this->payCodes = auth()->user()->pay_codes;
    }

    public function addPayCode()
    {
        $this->payCodes[] = [
            'prefix' => '',
            'suffix_from' => 6,
            'suffix_to' => 8,
            'character_type' => 'NumberOnly',
        ];
    }

    public function deletePayCode($key)
    {
        unset($this->payCodes[$key]);
    }

    public function savePayCode()
    {
        $validated = $this->validate([
            'payCodes.*.prefix' => 'required|string|min:2|max:5',
            'payCodes.*.suffix_from' => 'required|integer|min:1|max:30',
            'payCodes.*.suffix_to' => 'required|integer|min:1|max:30',
            'payCodes.*.character_type' => 'required|in:NumberOnly,NumberAndLetter',
        ]);
        auth()->user()->pay_codes = $validated['payCodes'] ?? [];
        auth()->user()->save();
        session()->flash('notifySuccess', '<PERSON><PERSON><PERSON> cấu trúc mã thành công!');
        $this->redirectRoute('configuration');
    }


    public function render()
    {
        return view('livewire.pay-code-structure');
    }
}
