<?php

namespace App\Livewire;

use App\Models\BankAccount;
use App\Models\Hook;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Illuminate\Support\Str;

class HookAdd extends Component
{
    public $accounts;
    public $bank_account_id;
    public $hook_type = '';
    public $endpoint;
    public $token;
    public $syntax = '';
    public $event = 'all';
    public $method = 'post';
    public $skip_if_no_code = false;

    public function generateToken()
    {
        $this->token = Str::random(50);
    }

    public function save()
    {
        $validated = $this->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'hook_type' => 'required|in:webhook,slack,telegram,larksuite,haravan,sapo',
            'endpoint' => 'required_if:hook_type,webhook,slack,telegram,larksuite|max:250',
            'token' => 'required|max:250',
            'event' => 'required|in:in,out,all',
            'method' => 'required|in:post',
            'syntax' => 'required_if:hook_type,haravan,sapo',
            'skip_if_no_code' => 'nullable|boolean',
        ]);
        $validated['user_id'] = Auth::user()->id;

        $hook = Hook::create($validated);
        session()->flash('notifySuccess', 'Thêm webhook thành công!');
        if (in_array($hook->hook_type, ['haravan'])) {
            $this->redirectRoute('hook.embed', ['id' => $hook->id]);
        } else {
            $this->redirectRoute('hook.index');
        }
    }

    public function render()
    {
        $this->accounts = BankAccount::where('user_id', Auth::user()->id)->get();
        return view('livewire.hook-add');
    }
}
