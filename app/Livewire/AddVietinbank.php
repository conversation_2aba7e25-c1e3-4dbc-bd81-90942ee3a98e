<?php

namespace App\Livewire;

use App\Bank\Vietinbank;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AddVietinbank extends Component
{
    public $username = '';

    public $password = '';

    public $account_no = '';

    public $otp = '';

    public $step = 1;

    public $progress = 50;

    public $is_loading = false;

    public function save()
    {
        $validated = $this->validate([
            'username' => ['required', Rule::unique('bank_accounts')->where(function ($query) {
                return $query->where('bank_code', 'icb');
            })],
            'password' => 'required',
            'account_no' => 'required',
        ]);

        $vietinbank = new Vietinbank($this->username, $this->password, $this->account_no);
        $result = $vietinbank->doLogin();
        if ($result) {
            if (isset($result['success']) && $result['success']) {
                $this->step = 2;
                $this->progress = 100;
            } else {
                $this->dispatch('notifyError', ['msg' => $result['message']]);
            }
        }
    }

    public function render()
    {
        return view('livewire.add-vietinbank');
    }
}
