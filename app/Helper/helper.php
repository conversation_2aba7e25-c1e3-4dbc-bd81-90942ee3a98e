<?php

use App\Helper\Bank\VietComBank;
use App\Helper\Bank\MBank;
use App\Helper\Bank\Bidv;
use App\Helpers\Bank\ViettelPay;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Route;

if (!function_exists('vietcombank')) {
    function vietcombank()
    {
        return new VietComBank;
    }
}
if (!function_exists('mbbank')) {
    function mbank()
    {
        return new MBank;
    }
}
if (!function_exists('bidv')) {
    function bidv()
    {
        return new Bidv;
    }
}

if (!function_exists('viettelpay')) {
    function viettelpay()
    {
        return new ViettelPay;
    }
}

if (!function_exists('active_menu')) {
    function active_menu($routeName = [])
    {
        if (in_array(Route::currentRouteName(), $routeName)) {
            return 'active';
        }

        return '';
    }
}

if (!function_exists('open_menu')) {
    function open_menu($routeName = [])
    {
        if (in_array(Route::currentRouteName(), $routeName)) {
            return 'open';
        }

        return '';
    }
}

if (!function_exists('responseJson')) {
    function responseJson($data, $status = 200)
    {
        return response()->json($data, $status, [], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
}

if (!function_exists('format_money')) {
    function format_money(int $int)
    {
        return str_replace(',', '.', number_format($int));
    }
}

if (!function_exists('ping')) {
    function ping($ipv4, $port, $waitTimeoutInSeconds = 1)
    {
        if (@$fp = fsockopen($ipv4, $port, $errCode, $errStr, $waitTimeoutInSeconds)) {
            fclose($fp);
            return true;
        }
        return false;
    }
}

if (!function_exists('convert_phone')) {

    function convert_phone($phonenumber, $rev = 1)
    {
        $CELL = array(
            '016966' => '03966',
            '0169' => '039',
            '0168' => '038',
            '0167' => '037',
            '0166' => '036',
            '0165' => '035',
            '0164' => '034',
            '0163' => '033',
            '0162' => '032',
            '0120' => '070',
            '0121' => '079',
            '0122' => '077',
            '0126' => '076',
            '0128' => '078',
            '0123' => '083',
            '0124' => '084',
            '0125' => '085',
            '0127' => '081',
            '0129' => '082',
            '01992' => '059',
            '01993' => '059',
            '01998' => '059',
            '01999' => '059',
            '0186' => '056',
            '0188' => '058'
        );

        if ($rev === 0) {
            $array = array();
            foreach ($CELL as $keys => $item) {
                $array[$item] = $keys;
            }
            $CELL = $array;
        }

        $phonenumber = str_replace(' ', '', $phonenumber);
        //2. Xóa các dấu chấm phân cách
        $phonenumber = str_replace('.', '', $phonenumber);
        //3. Xóa các dấu gạch nối phân cách
        $phonenumber = str_replace('-', '', $phonenumber);
        //4. Xóa dấu mở ngoặc đơn
        $phonenumber = str_replace('(', '', $phonenumber);
        //5. Xóa dấu đóng ngoặc đơn
        $phonenumber = str_replace(')', '', $phonenumber);
        //6. Xóa dấu +
        $phonenumber = str_replace('+', '', $phonenumber);
        //7. Chuyển 84 đầu thành 0
        if (substr($phonenumber, 0, 2) == '84') {
            $phonenumber = '0' . substr($phonenumber, 2, strlen($phonenumber) - 2);
        }
        foreach ($CELL as $key => $value) {
            //$prefixlen=strlen($key);
            if (strpos($phonenumber, $key) === 0) {
                $prefix = $key;
                $prefixlen = strlen($key);
                $phone = substr($phonenumber, $prefixlen, strlen($phonenumber) - $prefixlen);
                $prefix = str_replace($key, $value, $prefix);
                $phonenumber = $prefix . $phone;
                //$phonenumber=str_replace($key,$value,$phonenumber);
                break;
            }
        }

        if (substr($phonenumber, 0, 1) == '0') {
            $phonenumber = '84' . substr($phonenumber, 1, strlen($phonenumber) - 1);
        }
        return $phonenumber;
    }
}

function vn_to_str($str)
{

    $unicode = array(
        'a' => 'á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ',
        'd' => 'đ',
        'e' => 'é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ',
        'i' => 'í|ì|ỉ|ĩ|ị',
        'o' => 'ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ',
        'u' => 'ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự',
        'y' => 'ý|ỳ|ỷ|ỹ|ỵ',
        'A' => 'Á|À|Ả|Ã|Ạ|Ă|Ắ|Ặ|Ằ|Ẳ|Ẵ|Â|Ấ|Ầ|Ẩ|Ẫ|Ậ',
        'D' => 'Đ',
        'E' => 'É|È|Ẻ|Ẽ|Ẹ|Ê|Ế|Ề|Ể|Ễ|Ệ',
        'I' => 'Í|Ì|Ỉ|Ĩ|Ị',
        'O' => 'Ó|Ò|Ỏ|Õ|Ọ|Ô|Ố|Ồ|Ổ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ở|Ỡ|Ợ',
        'U' => 'Ú|Ù|Ủ|Ũ|Ụ|Ư|Ứ|Ừ|Ử|Ữ|Ự',
        'Y' => 'Ý|Ỳ|Ỷ|Ỹ|Ỵ',
    );

    foreach ($unicode as $nonUnicode => $uni) {

        $str = preg_replace("/($uni)/i", $nonUnicode, $str);
    }

    return $str;
}

function calculationDiffPricePackage($originPackage, $destinationPackage)
{
    $currentPackagePrice = $originPackage->package->price;
    $newPackagePrice = $destinationPackage->price;
    if ($currentPackagePrice == 0) {
        return $newPackagePrice;
    }

    $monthNumbers = round(now()->parse($originPackage->created_at)->diffInMonths(now()->parse($originPackage->expire_at)));
    $totalDays = round(now()->parse($originPackage->created_at)->diffInDays(now()->parse($originPackage->expire_at)));

    // Số ngày sử dụng gói A trước khi nâng cấp
    $daysUsedA = round(now()->parse($originPackage->created_at)->diffInDays(now()));

    // Calculate remaining days in the month
    $remainingDays = $totalDays - $daysUsedA;

    // Calculate the daily cost of the current package
    $dailyCostCurrent = $currentPackagePrice * $monthNumbers / $totalDays;

    // Calculate the daily cost of the new package
    $dailyCostNew = $newPackagePrice * $monthNumbers  / $totalDays;

    // Calculate the prorated refund for the current package
    $proratedRefund = $dailyCostCurrent * $remainingDays;

    // Calculate the prorated cost for the new package
    $proratedCostNew = $dailyCostNew * $remainingDays;

    // Calculate the difference
    $costDifference = $proratedCostNew - $proratedRefund;

    return $costDifference;
}
