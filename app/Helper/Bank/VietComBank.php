<?php

namespace App\Helper\Bank;

use App\Helper\Helper;
use Crypt_RSA;
use GuzzleHttp\Client;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Auth;
use App\Helper\Anticaptcha;
use App\Helper\ImageToText;
use App\Models\BankAccount;
use App\Models\Proxy;

class VietComBank extends Helper

{
    private $account;

    private $serverPublic = "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAiEPragkOAc+PM2TqG1Xqh/+mqWP0dJge+VfJ/H75nwCchOMNG297SgRKx7M3\nrvwxUfTw602rZ1LiwLV+h16/tGj5BxuQCkfAj+QFp3P4A+Kar8spo1mW2i7MCshhtzF72SHJ\n9K1yH67RmrCZdHpYdezs5yb1FtccUkUUhpbTX9PBaKMhxmecJE1jORRiSCdRl+c54NHVAbxf\nGrDDMRFw3PFv9cCmLSvP8/7mI3ClmDz+e9PsxFDItaynaMogrJDOm3D4i3CF2YgVmGBNBWfy\na/0t88eCWfM34JJ87ufQuzi6Fs9n3XOeWXN8DNc02YD9/Ua7lKFxaFF9iQfZkB3ckwIDAQAB\n-----END RSA PUBLIC KEY-----";

    protected $defaultPublicKey = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAikqQrIzZJkUvHisjfu5ZCN+TLy//43CIc5hJE709TIK3HbcC9vuc2+PPEtI6peSUGqOnFoYOwl3i8rRdSaK17G2RZN01MIqRIJ/6ac9H4L11dtfQtR7KHqF7KD0fj6vU4kb5+0cwR3RumBvDeMlBOaYEpKwuEY9EGqy9bcb5EhNGbxxNfbUaogutVwG5C1eKYItzaYd6tao3gq7swNH7p6UdltrCpxSwFEvc7douE2sKrPDp807ZG2dFslKxxmR4WHDHWfH0OpzrB5KKWQNyzXxTBXelqrWZECLRypNq7P+1CyfgTSdQ35fdO7M1MniSBT1V33LdhXo73/9qD5e5VQIDAQAB\n-----END PUBLIC KEY-----";
    protected $clientPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeEk3hNBXhvUKOl62RX2lf9KE1SZ3SCWu5qOWZsCcIBvD6fpDRP1iuKCmK49lAfP3ntdNRFN8i8MMYnaokZu+Pux3dywIiNVVLVCXFr00UcTR45M6hdbnLct9cJ+XLJIoJQW2TGz9xINErTMnvlj4n2uIm6nDv2AbR6Ii9+kq+iQIDAQAB";
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    public function loadByPhone($username, $accountNo, $ipv4 = null)
    {
        $this->account = BankAccount::where([
            'username' => $username
        ])->where(
            'bank_code',
            'vcb'
        )->first();
        if (empty($this->account)) {
            $this->account = new BankAccount();
            $this->account->bank_code = 'vcb';
            $this->account->username = $username;
            $this->account->account_no = $accountNo;
            $this->account->status   = 0;
            $this->account->user_id  = Auth::user()->id;
            $this->generateKey();
        }
        if ($ipv4 != null) {
            if (!empty($this->account->proxy)) {
                $this->account->proxy = $ipv4;
            }
        }
        return $this;
    }

    public function loadByAccount($exitAccount, $username, $accountNo, $ipv4 = null)
    {
        $this->account = $exitAccount;
        $this->account->username = $username;
        $this->account->account_no = $accountNo;

        return $this;
    }


    public function loginAuthNew($password)
    {
        $captcha_id = $this->generateImei();
        $result     = $this->getCaptcha($captcha_id);
        $resolved_captcha = $this->getCaptchaTHANHTRUNGIT($result);

        if ($resolved_captcha !== false) {
            if ($resolved_captcha->status == true) {
                $captchaText = $resolved_captcha->captcha;

                $this->account->password = $password;
                $result = $this->authNew($captcha_id, $captchaText);
                if ($result !== false) {
                    if ($result->code == '00') {
                        $this->account->access_key    = $result->accessKey;
                        $this->account->sessionId    = $result->sessionId;
                        $this->account->accounts      = $result->userInfo;
                        $this->account->account_holder_name      = $result->userInfo->cusName ?? '';
                        $this->account->last_status      = 'Đăng nhập thành công';
                        $this->account->time_login    = time();
                        $this->account->status        = 1;
                        $this->account->save();
                        return (object) [
                            'success' => true,
                            'request_otp' => false,
                            'message' => 'Đăng nhập thành công'
                        ];
                    }
                    if ($result->code == 20231 && $result->mid == 6) {
                        $this->account->browserToken        = $result->browserToken;
                        $this->account->accounts      = [];
                        $this->account->save();
                        $resultInitLoginNewBrowser = $this->initLoginNewBrowser();
                        if (isset($resultInitLoginNewBrowser->transaction->tranId)) {
                            $this->account->tranId        = $resultInitLoginNewBrowser->transaction->tranId;
                            $this->account->status        = 0;
                            $this->account->last_status       = 'Đang đợi OTP lưu trình duyệt';
                            $this->account->save();
                            $requestOtp = $this->chooseOtpType($resultInitLoginNewBrowser->transaction->tranId, 1);
                            return (object) [
                                'success' => true,
                                'request_otp' => true,
                                'data' => $requestOtp
                            ];
                        }
                    }
                    if ($result->code == '019' && $result->mid == 6) {
                        return (object) [
                            'success' => false,
                            'allow_browser' => false,
                            'data' => $result
                        ];
                    }
                }

                if ($this->account->id) {
                    $this->account->last_status = 'Đăng nhập không thành công';
                    $this->account->save();
                }


                return (object) [
                    'success' => false,
                    'message' => 'Đăng nhập không thành công'
                ];
            }
        }
        if ($this->account->id) {
            $this->account->last_status = 'Đăng nhập không thành công';
            $this->account->save();
        }
        return (object) [
            'success' => false,
            'message' => $result->des ?? 'Đăng nhập không thành công'
        ];
    }

    public function initLoginNewBrowser()
    {
        $client = new Client();
        try {
            $payload = [
                "browserToken" => $this->account->browserToken,
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "lang" => "vi",
                "mid" => 3008,
                "clientPubKey" => $this->clientPublicKey,
                "E" => $this->getE() ?: "",
                "browserId" => md5($this->account->username),
                "cif" => "",
                "clientId" => "",
                "mobileId" => "",
                "sessionId" => "",
                "user" => $this->account->username
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/authen-service/v1/api-3008', [
                'proxy' => $this->getProxy(),
                'json' => $encodeData,
                'headers' => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="106", " Not A;Brand";v="99", "Google Chrome";v="106"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                )
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                return $this->decrypt($response, $this->clientPrivateKey);
            }
        } catch (\Throwable $th) {
            dd($th);
        }
        return false;
    }

    public function chooseOtpType($tranId, $type = 5)
    {
        $client = new Client();
        try {
            $payload = [
                "browserToken" => $this->account->browserToken,
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "lang" => "vi",
                "clientPubKey" => $this->clientPublicKey,
                "E" => $this->getE() ?: "",
                "browserId" => md5($this->account->username),
                "cif" => "",
                "clientId" => "",
                "mobileId" => "",
                "sessionId" => "",
                "mid" => 3010,
                "browserToken" => $this->account->browserToken,
                "tranId" => $tranId,
                "type" => $type, //1 la sms,5 la smart
                "user" => $this->account->username
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/authen-service/v1/api-3010', [
                'proxy' => $this->getProxy(),
                'json' => $encodeData,
                'headers' => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="106", " Not A;Brand";v="99", "Google Chrome";v="106"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                )
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                return $this->decrypt($response, $this->clientPrivateKey);
            }
        } catch (\Throwable $th) {
            dd($th);
        }
        return false;
    }

    public function submitOtpLogin($otp, $type = 5)
    {
        $client = new Client();
        try {
            $payload = [
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "lang" => "vi",
                "clientPubKey" => $this->clientPublicKey,
                "E" => $this->getE() ?: "",
                "browserId" => md5($this->account->username),
                "cif" => "",
                "clientId" => "",
                "mobileId" => "",
                "sessionId" => "",
                "browserToken" => $this->account->browserToken,
                "tranId" => $this->account->tranId,
                "type" => $type, //1 la sms,5 la smart
                "user" => $this->account->username,
                "mid" => 3011,
                "otp" => $otp,
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/authen-service/v1/api-3011', [
                'proxy' => $this->getProxy(),
                'json' => $encodeData,
                'headers' => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="106", " Not A;Brand";v="99", "Google Chrome";v="106"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                )
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                return $this->decrypt($response, $this->clientPrivateKey);
            }
        } catch (\Throwable $th) {
            dd($th);
        }
        return false;
    }

    public function saveBrowser($data)
    {
        $client = new Client();
        try {
            $payload = [
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "lang" => "vi",
                "clientPubKey" => $this->clientPublicKey,
                "E" => $this->getE() ?: "",
                "browserId" => md5($this->account->username),
                "user" => $this->account->username,
                "browserName" => "Chrome " . $this->account->username,
                "mid" => 3009,
                "cif" => $data['cif'],
                "clientId" => $data['clientId'],
                "mobileId" => $data['mobileId'],
                "sessionId" => $data['sessionId'],
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/authen-service/v1/api-3009', [
                'proxy' => $this->getProxy(),
                'json' => $encodeData,
                'headers' => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="106", " Not A;Brand";v="99", "Google Chrome";v="106"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                )
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                $result =  $this->decrypt($response, $this->clientPrivateKey);
                if ($result && $result->code == 00) {
                    $this->account->status        = 1;
                    $this->account->last_status = 'Đăng nhập thành công';
                    $this->account->save();
                }
                if ($result && $result->code == '030' && $result->mid == '3009') {
                    $this->account->status        = 0;
                    $this->account->last_status       = 'Lỗi lưu trình duyệt do lưu quá 5 trình duyệt';
                    $this->account->save();
                }
                return $result;
            }
        } catch (\Throwable $th) {
            dd($th);
        }
        return false;
    }

    public function saoketaikhoan($accountNo = null, $accountType = null, $begin = null, $end = null)
    {
        if ($accountNo == null || $accountType == null) {
            $result = $this->danhsachtaikhoan();
            if ($result->success) {
                $accountNo      = $result->results['0']->accountNo;
                $accountType    = $result->results['0']->accountType;
            }
        }
        $begin = $begin ?? date('d/m/Y');
        $end   = $end   ?? date('d/m/Y');
        $result = $this->laysaoketaikhoan($accountNo, $accountType, $begin, $end);
        if ($result != false) {
            if ($result->code == '00') {
                $result = json_decode($this->decryptAES($result->data));
                if ($result != false) {
                    if ($result->code == '00') {
                        return (object) [
                            'success' => true,
                            'message' => 'Thành công',
                            'results' => $result->transactions
                        ];
                    }
                }
            }
        }
        return (object) [
            'success' => false,
            'message' => $result->des ?? 'Thất bại'
        ];
    }

    public function saoketaikhoanNew($accountNo = null, $accountType = null, $begin = null, $end = null)
    {
        if ($accountNo == null || $accountType == null) {
            $accountNo      = $this->account->accounts->defaultAccount;
            $accountType    = $this->account->accounts->defaultAccountType;
        }
        $begin = $begin ?? date('d/m/Y'); //strtotime('-1 days')
        $end   = $end   ?? date('d/m/Y'); //strtotime('+1 days')
        $result = $this->laysaoketaikhoanNew($accountNo, $accountType, $begin, $end);
        if ($result != false) {
            if (isset($result->code) && $result->code == '00') {
                return (object) [
                    'success' => true,
                    'message' => 'Thành công',
                    'results' => $result->transactions
                ];
            }
        }
        return (object) [
            'success' => false,
            'message' => $result->des ?? 'Thất bại'
        ];
    }

    private function laysaoketaikhoanNew($accountNo, $accountType, $begin, $end)
    {
        $client = new Client();
        try {
            $payload = [
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "accountNo" => $accountNo,
                "accountType" => $accountType,
                "cif" => "********",
                "clientId" => "********",
                "fromDate" => $begin,
                "lang" => "vi",
                "lengthInPage" => 999999,
                "mid" => 14,
                "mobileId" => "********",
                "pageIndex" => 0,
                "sessionId" => $this->account->sessionId,
                "stmtDate" => "",
                "stmtType" => "",
                "toDate" => $end,
                "user" => $this->account->username,
                "clientPubKey" => $this->clientPublicKey,
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/bank-service/v1/transaction-history', [
                'proxy' => $this->getProxy(),
                'json'      => $encodeData,
                'headers'   => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                ),
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                return $this->decrypt($response, $this->clientPrivateKey);
            }
        } catch (\Throwable $th) {
        }
        return false;
    }

    public function danhsachtaikhoan()
    {
        $result = $this->laydanhsachtaikhoan();
        if ($result !== false) {
            if ($result->code == '00') {
                $result = json_decode($this->decryptAES($result->data));
                if ($result !== false) {
                    if ($result->code == '00') {
                        return (object) [
                            'success' => true,
                            'message' => 'Thành công',
                            'results' => $result->DDAccounts
                        ];
                    }
                }
            }
        }
        return (object) [
            'success' => false,
            'message' => $result->des ?? 'Lấy danh sách tài khoản thất bại'
        ];
    }

    private function laysaoketaikhoan($accountNo, $accountType, $begin, $end)
    {
        $client = new Client();
        $this->account->cookies = $this->account->cookies == NULL ? [] : $this->account->cookies;
        $cookieJar = CookieJar::fromArray($this->account->cookies, 'vcbdigibank.vietcombank.com.vn');
        try {
            $res = $client->request('POST', 'https://vcbdigibank.vietcombank.com.vn/w1/process-ib', [
                'proxy' => $this->getProxy(),
                'json'      => array(
                    'data' => $this->encryptAES(array(
                        'user_name' => $this->account->username,
                        'data'      => array(
                            'processCode' => 'laysaoketaikhoan',
                            'cif'         => $this->account->accounts->cif,
                            'sessionId'   => $this->account->accounts->session_id,
                            'accountNo'   => $accountNo,
                            'accountType' => $accountType,
                            'fromDate'    => $begin,
                            'toDate'      => $end,
                            'pageIndex'   => 0,
                            'lengthInPage' => 999999,
                            'stmtDate'    => '',
                            'stmtType'    => '',
                            'lang'        => 'vi',
                        ),
                        'client_key' => $this->account->publicKey
                    )),
                    'mid' => 'laysaoketaikhoan'
                ),
                'headers'   => array(
                    'Host'              => 'vcbdigibank.vietcombank.com.vn',
                    'Connection'        => 'keep-alive',
                    'sec-ch-ua'         => '" Not;A Brand";v="99", "Google Chrome";v="97", "Chromium";v="97"',
                    'DNT'               => '1',
                    'sec-ch-ua-mobile'  => '?1',
                    'sec-ch-ua-platform' => '"Android"',
                    'Accept-Language'   => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
                    'User-Agent'        => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36',
                    'Referer'           => 'https://vcbdigibank.vietcombank.com.vn/thongtintaikhoan/chitiettaikhoan',
                    'Accept'            => 'application/json',
                    'Authorization'     => 'Bearer ' . $this->account->authorization
                ),
                'cookies'   => $cookieJar
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    private function laydanhsachtaikhoan()
    {
        $client = new Client();
        $this->account->cookies = $this->account->cookies == NULL ? [] : $this->account->cookies;
        $cookieJar = CookieJar::fromArray($this->account->cookies, 'vcbdigibank.vietcombank.com.vn');
        try {
            $res = $client->request('POST', 'https://vcbdigibank.vietcombank.com.vn/w1/process-ib', [
                'proxy' => $this->getProxy(),
                'json'      => array(
                    'data' => $this->encryptAES(array(
                        'user_name' => $this->account->username,
                        'data'      => array(
                            'processCode' => 'laydanhsachtaikhoan',
                            'cif'         => $this->account->accounts->cif,
                            'sessionId'   => $this->account->accounts->session_id,
                            'type'        => 1,
                            'lang'        => 'vi',
                        ),
                        'client_key' => $this->account->publicKey
                    )),
                    'mid' => 'laydanhsachtaikhoan'
                ),
                'headers'   => array(
                    'Host'              => 'vcbdigibank.vietcombank.com.vn',
                    'Connection'        => 'keep-alive',
                    'sec-ch-ua'         => '" Not;A Brand";v="99", "Google Chrome";v="97", "Chromium";v="97"',
                    'DNT'               => '1',
                    'sec-ch-ua-mobile'  => '?1',
                    'sec-ch-ua-platform' => '"Android"',
                    'Accept-Language'   => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
                    'User-Agent'        => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36',
                    'Referer'           => 'https://vcbdigibank.vietcombank.com.vn/thongtintaikhoan/chitiettaikhoan',
                    'Accept'            => 'application/json',
                    'Authorization'     => 'Bearer ' . $this->account->authorization
                ),
                'cookies'   => $cookieJar
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    private function authNew($captcha_id, $captcha_text)
    {
        $client = new Client();
        try {
            $payload = [
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "captchaToken" => $captcha_id,
                "captchaValue" => $captcha_text,
                "browserId" => md5($this->account->username),
                "checkAcctPkg" => "1",
                "lang" => "vi",
                "mid" => 6,
                "clientPubKey" => $this->clientPublicKey,
                "password" => $this->account->password,
                "user" => $this->account->username,
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/authen-service/v1/login', [
                'proxy' => $this->getProxy(),
                'json' => $encodeData,
                'headers' => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                )
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                return $this->decrypt($response, $this->clientPrivateKey);
            }
        } catch (\Throwable $th) {
            dd($th);
        }
        return false;
    }

    private function getCaptchaTHANHTRUNGIT($base64)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://api.ecaptcha.vn/api/captcha/vcb', [
                'json' => array(
                    'api_key' => '34269d96a9369e2fdd64601ac52e1c3c',
                    'base64' => $base64
                )
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    private function getCaptcha($captcha_id)
    {
        $client = new Client();
        try {
            $res = $client->request('GET', 'https://digiapp.vietcombank.com.vn/utility-service/v1/captcha/' . $captcha_id, [
                'proxy' => $this->getProxy(),
                'headers' => array(
                    'Accept' =>  'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'Accept-Encoding' =>  'gzip, deflate, br',
                    'Accept-Language' =>  'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,ja;q=0.4,ru;q=0.3,zh-CN;q=0.2,zh;q=0.1',
                    'Cache-Control' =>  'max-age=0',
                    'Connection' =>  'keep-alive',
                    'Cookie' =>  '__cf_bm=SYisb5lWhP7wnmLF2IhD6PTz1VScCPtxAMBEpg7TD98-**********-0-ARJ5C5ox0u20jJzEG6BTiczEBEW8PCndhrCF8UHHspAnOhgqoxYuKfG3vLNka7vFZk34WZ8gEyjvLELhSptJdatZv3eoYYyIqJfChDR54Xw8; TS01388157=011fc56c761311f84d17ecb3b4027b21264fd5f2b1f5d1ec3c7cf9a12271573777cf5689962b64d51499c2af8cef3bcf8dfb559d05',
                    'Host' =>  'digiapp.vietcombank.com.vn',
                    'sec-ch-ua' =>  '"Not?A_Brand";v="8", "Chromium";v="108", "Google Chrome";v="108"',
                    'sec-ch-ua-mobile' =>  '?0',
                    'sec-ch-ua-platform' =>  "macOS",
                    'Sec-Fetch-Dest' =>  'document',
                    'Sec-Fetch-Mode' =>  'navigate',
                    'Sec-Fetch-Site' =>  'none',
                    'Sec-Fetch-User' =>  '?1',
                    'Upgrade-Insecure-Requests' =>  '1',
                    'User-Agent' =>  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                )
            ]);
            return base64_encode($res->getBody()->getContents());
        } catch (\Throwable $th) {
        }
        return false;
    }

    public function getAccountDeltail($account_number)
    {
        $client = new Client();
        try {
            $payload = [
                "DT" => "Mac OS X",
                "OV" => "10_15_7",
                "PM" => "Chrome *********",
                "browserId" => md5($this->account->username),
                "clientPubKey" => $this->clientPublicKey,
                "user" => $this->account->username,
                "E" => $this->getE() ?: "",
                "accountNo" => $account_number,
                "accountType" => "D",
                "mid" => 13,
                "cif" => $this->account->accounts->cif,
                "mobileId" => $this->account->accounts->mobileId,
                "clientId" => $this->account->accounts->clientId,
                "sessionId" => $this->account->accounts->sessionId
            ];
            $encodeData = $this->encrypt(json_encode($payload), $this->defaultPublicKey);
            $res = $client->request('POST', 'https://digiapp.vietcombank.com.vn/bank-service/v1/get-account-detail', [
                'proxy' => $this->getProxy(),
                'json' => $encodeData,
                'headers' => array(
                    "Accept" => "application/json",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi",
                    "Connection" => "keep-alive",
                    "Content-Length" => "219",
                    "Content-Type" => "application/json",
                    "Host" => "digiapp.vietcombank.com.vn",
                    "Origin" => "https =>//vcbdigibank.vietcombank.com.vn",
                    "Referer" => "https =>//vcbdigibank.vietcombank.com.vn/",
                    "sec-ch-ua" => '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
                    "sec-ch-ua-mobile" => '?0',
                    "sec-ch-ua-platform" => "macOS",
                    "Sec-Fetch-Dest" => 'empty',
                    "Sec-Fetch-Mode" => 'cors',
                    "Sec-Fetch-Site" => 'same-site',
                    "User-Agent" => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    "X-Channel" => 'Web',
                    "X-Request-ID" => ***************
                )
            ]);
            $response = json_decode($res->getBody());
            if ($response) {
                return $this->decrypt($response, $this->clientPrivateKey);
            }
        } catch (\Throwable $th) {
            dd($th);
        }
        return false;
    }

    private function generateKey(): void
    {
        $config = array(
            'config' => app_path('/Helper/Bank/openssl.cnf'),
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA
        );

        $keyPair = openssl_pkey_new($config);
        // print_r($keyPair);
        // die();
        openssl_pkey_export($keyPair, $privateKey, null, $config);
        $this->account->privateKey = $privateKey;
        $this->account->publicKey  = openssl_pkey_get_details($keyPair)['key'];
    }

    private function encryptAES($encrypt)
    {
        $random = random_bytes(24);
        $rsa = new Crypt_RSA();
        $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
        $rsa->loadKey($this->serverPublic);
        $encrypted_key = $rsa->encrypt(base64_encode($random));
        return base64_encode($encrypted_key) . "@@@@@@" . base64_encode(openssl_encrypt(json_encode($encrypt, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), "aes-192-ecb", $random, OPENSSL_RAW_DATA));
    }

    private function decryptAES($encrypted)
    {
        $rsa = new Crypt_RSA();
        $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
        $rsa->loadKey($this->account->privateKey);
        $decrypted = $rsa->decrypt(base64_decode(substr($encrypted, 0, 344)));
        return openssl_decrypt(base64_decode(substr($encrypted, 344)), 'aes-192-ecb', base64_decode($decrypted), OPENSSL_RAW_DATA);
    }

    private function cookies_to_array(array $cookies): void
    {
        $cookie = $this->account->cookies ?? [];
        foreach ($cookies as $item) {
            if (strstr($item, '=')) {
                $explode = explode('=', $item);
                $cookie[$explode[0]] = explode(';', substr($item, strlen($explode[0]) + 1))[0];
            }
        }
        $this->account->cookies = $cookie;
        // dd($cookies);
    }

    function generateRandomString($length = 10)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    function encrypt($str, $publicKey)
    {
        $key = $this->generateRandomString(32);
        $iv = $this->generateRandomString(16);
        $rsa = new Crypt_RSA();
        $rsa->loadKey($publicKey);
        $rsa->setEncryptionMode(2);
        $body = base64_encode($iv . openssl_encrypt($str, 'AES-256-CTR', $key, OPENSSL_RAW_DATA, $iv));
        $header = base64_encode($rsa->encrypt(base64_encode($key)));

        return [
            'd' => $body,
            'k' => $header,
        ];
    }

    function decrypt($cipher, $privateKey)
    {
        $header = $cipher->k;
        $body = base64_decode($cipher->d);

        $rsa = new Crypt_RSA();
        $rsa->loadKey($privateKey);
        $rsa->setEncryptionMode(2);
        $key = $rsa->decrypt(base64_decode($header));

        $iv = substr($body, 0, 16);
        $cipherText = substr($body, 16);
        $text = openssl_decrypt($cipherText, 'AES-256-CTR', base64_decode($key), OPENSSL_RAW_DATA, $iv);

        return json_decode($text);
    }

    protected function getE()
    {
        $ahash = md5($this->account->username);
        $imei = vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split($ahash, 4));
        return strtoupper($imei);
    }

    public function setTranId($tranId)
    {
        $this->account->tranId = $tranId;
        $this->account->save();
    }

    public function getProxy()
    {
        $proxy = Proxy::where('status', 1)->first();
        if ($proxy) {

            return $proxy->proxy;
        }

        return "";
    }
}
