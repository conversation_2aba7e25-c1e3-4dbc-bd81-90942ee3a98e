<?php

namespace App\Helper\Bank;

use App\Helper\Helper;
use GuzzleHttp\Client;
use App\Models\BankAccount;
use App\Models\Proxy;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MBank extends Helper

{
    private $account;

    private $appVer = 'ios_10.9_v452';

    public function loadByUsername($username, $accountNo)
    {
        $this->account = BankAccount::where(
            'username',
            $username
        )
            ->where(
                'bank_code',
                'mb'
            )
            ->first();
        if (!$this->account) {
            $this->account = new BankAccount();
            $this->account->bank_code = 'mb';
            $this->account->username = $username;
            $this->account->account_no = $accountNo;
            $this->account->ref_no    = $this->getTimeNow();
            $this->account->imei     = $this->generateImei();
            $this->account->status   = 0;
            $this->account->cookies  = [];
            $this->account->user_id  = Auth::user()->id;
        }
        return $this;
    }

    public function loadByAccount($exitAccount, $username, $password, $accountNo, $ipv4 = null)
    {
        $this->account = $exitAccount;
        $this->account->username = $username;
        $this->account->password = $password;
        $this->account->account_no = $accountNo;

        return $this;
    }

    public function loginAuth($password = null)
    {
        $this->account->password = $password ?? $this->account->password;
        if (empty($this->account->password)) {
            return array(
                'success' => false,
                'message' => 'Vui lòng nhập mật khẩu để đăng nhập'
            );
        }
        // $result = $this->doLogin();
        $result = $this->doLoginWebV2();
        if ($result != false) {
            if ($result->result->ok == true && $result->result->responseCode == "00") {
                $this->account->status      = 1;
                $this->account->account_no  = $result->cust->chrgAcctCd;
                $this->account->sessionId  = $result->sessionId;
                $this->account->accounts   = $result->cust;
                $this->account->account_holder_name = $result->cust->nm ?? '';
                $this->account->last_status      = 'Đăng nhập thành công';
                $this->account->save();

                return [
                    'success' => true,
                    'message' => 'Đăng nhập thành công'
                ];
            }
        }
        return [
            'success' => false,
            'message' => 'Đăng nhập thất bại vui lòng kiểm tra lại thông tin'
        ];
    }

    public function balance()
    {
        $result = $this->getBalance();
        if ($result !== false) {
            if ($result->result->ok == true) {
                return (object) [
                    'success' => true,
                    'message' => 'Thành công',
                    'results' => $result->acct_list
                ];
            }
        }
        return (object) [
            'success' => true,
            'message' => 'Lấy thông tin số dư thất bại'
        ];
    }

    public function accounts()
    {
        $result = $this->getList();
        if ($result !== false) {
            if ($result->result->ok == true) {
                return (object) [
                    'success' => true,
                    'message' => 'Thành công',
                    'results' => $result->acct_list
                ];
            }
        }
        return (object) [
            'success' => true,
            'message' => 'Lấy thông tin số dư thất bại'
        ];
    }

    public function getTransactionHistory($from_date, $to_date, $account_no = null)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://mobile.mbbank.com.vn/retail_lite/common/getTransactionHistory', array(
                'proxy' => $this->getProxy(),
                'json' => array(
                    'accountNo'     => $account_no ?? $this->account->account_no,
                    'fromDate'      => $from_date ?? date("d/m/Y"),
                    'historyNumber' => '',
                    'historyType'   => 'DATE_RANGE',
                    'toDate'        => $to_date ?? date("d/m/Y"),
                    'type'          => 'ACCOUNT',
                    'sessionId'     => $this->account->sessionId,
                    'refNo'         => $this->account->ref_no,
                    'deviceIdCommon' => $this->account->imei,
                    'appVersion'    => $this->appVer,
                ),
                'headers'     => [
                    'Host'              => 'mobile.mbbank.com.vn',
                    'Content-Type'      => 'application/json',
                    'User-Agent'        => 'MB%20Bank/2 CFNetwork/1331.0.3 Darwin/21.4.0',
                    'Connection'        => 'keep-alive',
                    'Accept'            => 'application/json',
                    'Accept-Language'   => 'vi-VN,vi;q=0.9',
                    'Authorization'     => 'Basic QURNSU46QURNSU4=',
                    'Accept-Encoding'   => 'gzip, deflate, br'
                ],
            ));
            $this->cookies_to_array($res->getHeader('Set-Cookie'));
            return json_decode($res->getBody());
        } catch (\Throwable $e) {
        }
        return false;
    }

    public function getTransactionHistoryWEB($from_date, $to_date, $account_no = null)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://online.mbbank.com.vn/api/retail-transactionms/transactionms/get-account-transaction-history', array(
                'proxy' => $this->getProxy(),
                'json' => array(
                    'accountNo'     => $account_no ?? $this->account->account_no,
                    'fromDate'      => $from_date ?? date("d/m/Y"),
                    'toDate'        => $to_date ?? date("d/m/Y"),
                    'sessionId'     => $this->account->sessionId,
                    'refNo'         => $this->account->ref_no,
                    'deviceIdCommon' => $this->account->imei,
                ),
                'headers'     => [
                    'Deviceid' => $this->account->imei,
                    'sec-ch-ua' => '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    'sec-ch-ua-mobile' => '?0',
                    'Authorization' => 'Basic RU1CUkVUQUlMV0VCOlNEMjM0ZGZnMzQlI0BGR0AzNHNmc2RmNDU4NDNm',
                    'elastic-apm-traceparent' => '00-690e238f5a479be690001e5257478972-4b8184bf0f444db1-01',
                    'Content-Type' => 'application/json; charset=UTF-8',
                    'RefNo' => $this->account->ref_no,
                    'Accept' => 'application/json, text/plain, */*',
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer' => 'https://online.mbbank.com.vn/information-account/source-account',
                    'X-Request-Id' => $this->account->username . '-' . date('YmdHis'),
                ],
            ));
            $this->cookies_to_array($res->getHeader('Set-Cookie'));
            return json_decode($res->getBody());
        } catch (\Throwable $e) {
        }
        return false;
    }

    private function getList()
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://mobile.mbbank.com.vn/retail_lite/card/getList', array(
                'proxy' => $this->getProxy(),
                'json' => array(
                    'sessionId'     => $this->account->sessionId,
                    'refNo'         => $this->account->ref_no,
                    'deviceIdCommon' => $this->account->imei,
                    'appVersion'    => $this->appVer,
                ),
                'headers'     => [
                    'Host'              => 'mobile.mbbank.com.vn',
                    'Content-Type'      => 'application/json',
                    'User-Agent'        => 'MB%20Bank/2 CFNetwork/1331.0.3 Darwin/21.4.0',
                    'Connection'        => 'keep-alive',
                    'Accept'            => 'application/json',
                    'Accept-Language'   => 'vi-VN,vi;q=0.9',
                    'Authorization'     => 'Basic QURNSU46QURNSU4=',
                    'Accept-Encoding'   => 'gzip, deflate, br'
                ],
            ));
            $this->cookies_to_array($res->getHeader('Set-Cookie'));
            return json_decode($res->getBody());
        } catch (\Throwable $e) {
        }
        return false;
    }

    private function getBalance()
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://mobile.mbbank.com.vn/retail_lite/account/v2.0/getBalance', array(
                'proxy' => $this->getProxy(),
                'json' => array(
                    'sessionId'     => $this->account->sessionId,
                    'refNo'         => $this->account->ref_no,
                    'deviceIdCommon' => $this->account->imei,
                    'appVersion'    => $this->appVer,
                ),
                'headers'     => [
                    'Host'              => 'mobile.mbbank.com.vn',
                    'Content-Type'      => 'application/json',
                    'User-Agent'        => 'MB%20Bank/2 CFNetwork/1331.0.3 Darwin/21.4.0',
                    'Connection'        => 'keep-alive',
                    'Accept'            => 'application/json',
                    'Accept-Language'   => 'vi-VN,vi;q=0.9',
                    'Authorization'     => 'Basic QURNSU46QURNSU4=',
                    'Accept-Encoding'   => 'gzip, deflate, br'
                ],
            ));
            $this->cookies_to_array($res->getHeader('Set-Cookie'));
            return json_decode($res->getBody());
        } catch (\Throwable $e) {
        }
        return false;
    }

    private function doLogin()
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://mobile.mbbank.com.vn/retail_lite/common/doLogin', array(
                'proxy' => $this->getProxy(),
                'json' => array(
                    'userId'    => $this->account->username,
                    'password'  => md5($this->account->password),
                    'refNo'     => $this->account->ref_no,
                    'deviceIdCommon' => $this->account->imei,
                    'deviceId'  => $this->account->imei,
                    'appVersion' => $this->appVer
                ),
                'headers'     => [
                    'Host'              => 'mobile.mbbank.com.vn',
                    'Content-Type'      => 'application/json',
                    'User-Agent'        => 'MB%20Bank/2 CFNetwork/1331.0.3 Darwin/21.4.0',
                    'Connection'        => 'keep-alive',
                    'Accept'            => 'application/json',
                    'Accept-Language'   => 'vi-VN,vi;q=0.9',
                    'Authorization'     => 'Basic QURNSU46QURNSU4=',
                    'Accept-Encoding'   => 'gzip, deflate, br'
                ],
            ));
            $this->cookies_to_array($res->getHeader('Set-Cookie'));
            return json_decode($res->getBody());
        } catch (\Throwable $e) {
        }
        return false;
    }

    private function doLoginWeb()
    {
        $captchaText = "";
        $captcha = $this->getCaptchaMB();
        if ($captcha && $captcha->result && $captcha->result->ok) {
            $resolved_captcha = $this->getCaptchaTHANHTRUNGIT($captcha->imageString);
            if ($resolved_captcha !== false) {
                if ($resolved_captcha->status == true) {
                    $captchaText = $resolved_captcha->captcha;
                    //file_put_contents(public_path().'/uploads/'.$captchaText.'.png', base64_decode($captcha->imageString));
                }
            }
        }

        $client = new Client();
        try {
            $res = $client->request('POST', 'https://online.mbbank.com.vn/retail_web/internetbanking/doLogin', array(
                'proxy' => $this->getProxy(),
                'json' => array(
                    'userId'    => $this->account->username,
                    'password'  => md5($this->account->password),
                    "captcha" => $captchaText,
                    "sessionId" =>  null,
                    'refNo'     => $this->account->ref_no,
                    'deviceIdCommon' => $this->account->imei,
                ),
                'headers'     => [
                    "Accept" => "application/json, text/plain, */*",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,ja;q=0.4,ru;q=0.3,zh-CN;q=0.2,zh;q=0.1",
                    "Authorization" => "Basic QURNSU46QURNSU4=",
                    "Connection" => "keep-alive",
                    "Content-Length" => "229",
                    "Content-Type" => "application/json; charset=UTF-8",
                    "Cookie" => "_ga=GA1.3.**********.**********; _fbp=fb.2.*************.**********; _fbc=fb.2.*************.IwAR2tiDL2VLBOTym0oCKP_4TZZvYfk3QpQcGQdEME6GCG6opD-zKDS3XvWtQ; _gid=GA1.3.*********.**********; BIGipServerk8s_online_banking_pool_9712=**********.61477.0000; MBAnalyticsaaaaaaaaaaaaaaaa_session_=LPIACDFHLHBLGODMOGOCMLMBKCCKDMKKNFCDFHEJDMIICBCBMKNDHAJNKHDCHFHOICLDGABBPKIOHMNNAPMAEFGPMBIMBFEHFLBKDFDGCMOENBFOOCFFDODNGGPNLHJF; BIGipServerk8s_retail_web_internetbankingms_pool_9751=**********.5926.0000; BIGipServerLog_Center_API_9201_POOLS=**********.61731.0000; BIGipServeronline_mbbank_retake_pool_9201=**********.61731.0000; BIGipServeronline_mbbank_retail_web_pool_8686=**********.60961.0000; BIGipServerk8s_retail-web-accountms_pool_10557=**********.15657.0000; JSESSIONID=BE3C8649966066121B854E9C337253A0",
                    "Host" => "online.mbbank.com.vn",
                    "Origin" => "https//online.mbbank.com.vn",
                    "Referer" => "https//online.mbbank.com.vn/pl/login?logout=1",
                    "Sec-Fetch-Dest" => "empty",
                    "Sec-Fetch-Mode" => "cors",
                    "Sec-Fetch-Site" => "same-origin",
                    "User-Agent" => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "X-Request-Id" => "cae7109eb6a3f79261787a882b5ec1ff-****************",
                    "elastic-apm-traceparent" => "00-fba2c58e6d9adca1b39ded194c70b979-79c83c22e5578344-01",
                    "sec-ch-ua" => '"Google Chrome";v="105", "Not)A;Brand";v="8", "Chromium";v="105"',
                    "sec-ch-ua-mobile" => "?0",
                    "sec-ch-ua-platform" => '"macOS"',
                ],
            ));
            $this->cookies_to_array($res->getHeader('Set-Cookie'));
            return json_decode($res->getBody());
        } catch (\Throwable $e) {
            dd($e);
        }
        return false;
    }

    private function doLoginWebV2()
    {
        $captchaText = "";
        $captcha = $this->getCaptchaMB();
        if ($captcha && $captcha->result && $captcha->result->ok) {
            $resolved_captcha = $this->getCaptchaTHANHTRUNGIT($captcha->imageString);
            if ($resolved_captcha !== false) {
                if ($resolved_captcha->status == true) {
                    $captchaText = $resolved_captcha->captcha;
                    //file_put_contents(public_path().'/uploads/'.$captchaText.'.png', base64_decode($captcha->imageString));
                }
            }
        }
        $client = new Client();

        $dataLogin = array(
            'userId'    => $this->account->username,
            'password'  => md5($this->account->password),
            "captcha" => $captchaText,
            "ibAuthen2faString" => '',
            "sessionId" =>  null,
            'refNo'     => $this->account->ref_no,
            'deviceIdCommon' => $this->account->imei,
        );
        $res = $client->request('POST', 'http://103.142.24.84:2155/mb/encrypt-data', array(
            'json' => $dataLogin
        ));
        $resultEnc = json_decode($res->getBody());

        try {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://online.mbbank.com.vn/api/retail_web/internetbanking/v2.0/doLogin',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{"dataEnc":"' . $resultEnc->data . '"}',
                CURLOPT_HTTPHEADER => array(
                    'sec-ch-ua: "Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
                    'sec-ch-ua-mobile: ?0',
                    'Authorization: Basic RU1CUkVUQUlMV0VCOlNEMjM0ZGZnMzQlI0BGR0AzNHNmc2RmNDU4NDNm',
                    'elastic-apm-traceparent: 00-990ae541635925153418b611d6a6a570-3ac4e7378551e9a3-01',
                    'RefNo: dsad-****************',
                    'Content-Type: application/json; charset=UTF-8',
                    'Accept: application/json, text/plain, */*',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
                    'Referer: https://online.mbbank.com.vn/pl/login?returnUrl=%2F',
                    'app: MB_WEB',
                    'X-Request-Id: ' . $dataLogin['refNo'],
                    'sec-ch-ua-platform: "Windows"'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);

            return json_decode($response);
        } catch (\Throwable $e) {
            dd($e);
        }
        return false;
    }

    private function getCaptchaMB()
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://online.mbbank.com.vn/api/retail-web-internetbankingms/getCaptchaImage', [
                'proxy' => $this->getProxy(),
                'json' => array(
                    "deviceIdCommon" => $this->account->imei,
                    "refNo" => $this->account->ref_no,
                    "sessionId" => "",
                ),
                'headers'     => [
                    "Accept" => "application/json, text/plain, */*",
                    "Accept-Encoding" => "gzip, deflate, br",
                    "Accept-Language" => "vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,ja;q=0.4,ru;q=0.3,zh-CN;q=0.2,zh;q=0.1",
                    "Authorization" => "Basic QURNSU46QURNSU4=",
                    "Connection" => "keep-alive",
                    "Content-Length" => "229",
                    "Content-Type" => "application/json; charset=UTF-8",
                    "Cookie" => "_ga=GA1.3.**********.**********; _fbp=fb.2.*************.**********; _fbc=fb.2.*************.IwAR2tiDL2VLBOTym0oCKP_4TZZvYfk3QpQcGQdEME6GCG6opD-zKDS3XvWtQ; _gid=GA1.3.*********.**********; BIGipServerk8s_online_banking_pool_9712=**********.61477.0000; MBAnalyticsaaaaaaaaaaaaaaaa_session_=LPIACDFHLHBLGODMOGOCMLMBKCCKDMKKNFCDFHEJDMIICBCBMKNDHAJNKHDCHFHOICLDGABBPKIOHMNNAPMAEFGPMBIMBFEHFLBKDFDGCMOENBFOOCFFDODNGGPNLHJF; BIGipServerk8s_retail_web_internetbankingms_pool_9751=**********.5926.0000; BIGipServerLog_Center_API_9201_POOLS=**********.61731.0000; BIGipServeronline_mbbank_retake_pool_9201=**********.61731.0000; BIGipServeronline_mbbank_retail_web_pool_8686=**********.60961.0000; BIGipServerk8s_retail-web-accountms_pool_10557=**********.15657.0000; JSESSIONID=BE3C8649966066121B854E9C337253A0",
                    "Host" => "online.mbbank.com.vn",
                    "Origin" => "https//online.mbbank.com.vn",
                    "Referer" => "https//online.mbbank.com.vn/pl/login?logout=1",
                    "Sec-Fetch-Dest" => "empty",
                    "Sec-Fetch-Mode" => "cors",
                    "Sec-Fetch-Site" => "same-origin",
                    "User-Agent" => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "X-Request-Id" => "cae7109eb6a3f79261787a882b5ec1ff-****************",
                    "elastic-apm-traceparent" => "00-fba2c58e6d9adca1b39ded194c70b979-79c83c22e5578344-01",
                    "sec-ch-ua" => '"Google Chrome";v="105", "Not)A;Brand";v="8", "Chromium";v="105"',
                    "sec-ch-ua-mobile" => "?0",
                    "sec-ch-ua-platform" => '"macOS"',
                ],
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    private function getCaptchaTHANHTRUNGIT($base64)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://api.ecaptcha.vn/api/captcha/mb', [
                'json' => array(
                    'api_key' => '34269d96a9369e2fdd64601ac52e1c3c',
                    'base64' => $base64
                )
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    private function cookies_to_array(array $cookies): void
    {
        $cookie = $this->account->cookies;
        foreach ($cookies as $item) {
            if (strstr($item, '=')) {
                $explode = explode('=', $item);
                $cookie[$explode[0]] = explode(';', substr($item, strlen($explode[0]) + 1))[0];
            }
        }
        $this->account->cookies = $cookie;
    }


    private function getTimeNow()
    {
        return round(microtime(true) * 1000);
    }

    public function generateImei()
    {
        return $this->generateRandomString(8) . '-' . $this->generateRandomString(4) . '-' . $this->generateRandomString(4) . '-' . $this->generateRandomString(4) . '-' . $this->get_time_request();
    }

    public function get_time_request()
    {
        $d = getdate();
        $today = $d['hours'] . $d['minutes'] . $d['seconds'];
        $day = date('Y') . date('m') . date('d');
        return $day . $today;
    }

    private function ref_no()
    {
        return $this->account->username . '-' . date('YmdHis');
    }

    public function getProxy()
    {
        $proxy = Proxy::where('status', 1)->first();
        if ($proxy) {

            return $proxy->proxy;
        }

        return "";
    }
}
