<?php
namespace App\Helpers\Bank;

use App\Helper\Helper;
use App\Models\ViettelPay as ModelsViettelPay;
use GuzzleHttp\Client;

class ViettelPay extends Helper

{
    private $account;

    private $image;

    private $session_time = 200;

    private $appCode = '5.1.1';

    private $appVer  = '15.4.1';

    public function loadByPhone($id, $ipv4 = null)
    {
        $id = convert_phone($id);
        $this->account = ModelsViettelPay::where(array(
            'username' => $id
        ))->get()->first();
        if(empty($this->account)){
            $this->account = new ModelsViettelPay();
            $this->account->username = $id;
            $this->account->imei     = strtoupper($this->generateImei());
            $this->account->token_notification = $this->get_TOKEN();
            $this->account->status   = 0;
            $this->generateRsa();
        }
        if($ipv4 != null) {
            if(!empty($this->account->proxy)) {
                $this->account->proxy = $ipv4;
            }
        }
        return $this;
    }

    public function loginAuth($password)
    {
        $result = $this->CHECK_USER();
        if($result !== false){
            if($result->status->code == '00'){
                $this->account->accountId   = $result->data->accountId;
                $this->account->displayName = $result->data->displayName;
                $this->account->avatar      = $result->data->avatar;
                $this->account->password    = $password;
                $result = $this->LOGIN_AUTH();
                if($result !== false){
                    switch ($result->status->code) {
                        case 'AUT0014':
                            $this->account->requestId = $result->data->requestId;
                            $this->account->save();
                            return (object) array(
                                'success' => false,
                                'otp'     => true,
                                'message' => $result->status->displayMessage
                            );
                            break;
                        case '00':
                            $this->account->authorization = $result->data->accessToken;
                            $this->account->refreshToken  = $result->data->refreshToken;
                            // $this->account->save();
                            $result = $this->SESSION();
                            if($result !== false){
                                if($result->status->code == '00'){
                                    $this->account->session_id = $result->data->otherData->sessionId;
                                    $this->account->accNo      = (!empty($result->data->sources->infra)) ? $result->data->sources->infra['0']->accNo : null;
                                    $this->account->status     = 1;
                                    // $this->account->save();
                                    $result = $this->BALANCE_INQUIRY_NO_PIN();
                                    if($result !== false){
                                        if($result->response_code == '00'){
                                            $this->account->balance = $result->balance;
                                            // $this->account->save();
                                        }
                                    }
                                    $this->account->save();
                                    return (object) array(
                                        'success' => true,
                                        'message' => 'Đăng nhập thành công'
                                    );
                                }
                            }
                            break;
                        default:
                            # code...
                            break;
                    }

                }
            }
            return (object) array(
                'success' => false,
                'message' => $result->status->displayMessage
            );
        }
        return (object) array(
            'success' => false,
            'message' => 'Đã xảy ra lỗi đăng nhập vui lòng thử lại'
        );
    }

    public function balance()
    {
        $result = $this->BALANCE_INQUIRY_NO_PIN();
        if($result !== false){
            if($result->response_code == '00'){
                $this->account->balance = $result->balance;
                $this->account->save();
                return (object) array(
                    'success' => true,
                    'balance' => $result->balance,
                    'message' => 'Cập nhật số dư thành công'
                );
            }
        }
        return (object) array(
            'success' => false,
            'message' => 'Cập nhật số dư thất bại'
        );
    }

    public function transfermoney($receiver,$amount = 1000,$comment = 'NULL',$otp = false,$order_id = '')
    {
        $this->account->accounts = array(
            'receiver' => $receiver,
            'amount'   => (int) $amount,
            'comment'  => $comment,
            'otp'      => $otp,
            'order_id' => $order_id
        );
        if(empty($otp)) {
            $result = $this->MONEY_TRANSFER_INSIDE_SMS();
            if($result != false) {
                if($result->response_code == 'OTP') {
                    return (object) array(
                        'success' => true,
                        'otp'     => true,
                        'message' => $result->msg_confirm,
                        'order_id'=> $result->order_id
                    );
                }
                return (object) array(
                    'success' => false,
                    'message' => $result->error_code_detail
                );
            }
            return (object) array(
                'success' => false,
                'message' => 'Đã xảy ra lỗi chuyển tiền vui lòng thử lại'
            );
        }
        $result = $this->MONEY_TRANSFER_INSIDE_SMS_OTP();
        if($result != false) {
            if($result->response_code == '00') {
                return (object) array(
                    'success' => true,
                    'message' => 'Thành công',
                    'balance' => $this->account->balance - $this->account->accounts->amount
                );
            }
            return (object) array(
                'success' => false,
                'message' => $result->error_code_detail
            );
        }
        return (object) array(
            'success' => false,
            'message' => 'Đã xảy ra lỗi chuyển tiền vui lòng thử lại'
        );
    }

    public function transfer($receiver,$bankcode,$amount = 1000,$comment = 'NULL',$otp = false,$order_id = '',$ben_name = '')
    {
        $request = array(
            'bank_acc' => $receiver,
            'bankcode' => $bankcode,
            'amount'   => (int) $amount,
            'comment'  => $comment,
            'otp'      => $otp,
            'order_id' => $order_id,
            'ben_name' => $ben_name
        );
        $this->account->accounts = $request;
        if(empty($otp)) {
            $result = $this->GET_BENNAME_FROM_ACCOUNT_NUMBER();
            if($result != false) {
                if($result->response_code == '00') {
                    $this->account->accounts = array_merge($request, array(
                        'ben_name' => $result->ben_name
                    ));
                    $result = $this->MONEY_TRANSFER_OUTSIDE_SMS();
                    if($result != false) {
                        if($result->response_code == 'OTP') {
                            return (object) array(
                                'success' => true,
                                'otp'     => true,
                                'message' => $result->msg_confirm,
                                'order_id'=> $result->order_id,
                                'ben_name'=> $this->account->accounts->ben_name
                            );
                        }
                        return (object) array(
                            'success' => false,
                            'message' => $result->error_code_detail
                        );
                    }
                    return (object) array(
                        'success' => false,
                        'message' => 'Chuyển tiền thất bại'
                    );
                }
                return (object) array(
                    'success' => false,
                    'message' => $result->error_code_detail
                );
            }
        }
        $result = $this->MONEY_TRANSFER_OUTSIDE_SMS_OTP();
        if($result != false) {
            if($result->response_code == '00') {
                return (object) array(
                    'success' => true,
                    'message' => 'Thành công',
                    'balance' => $this->account->balance - $this->account->accounts->amount
                );
            }
            return (object) array(
                'success' => false,
                'message' => $result->error_code_detail
            );
        }
        return (object) array(
            'success' => false,
            'message' => 'Đã xảy ra lỗi chuyển tiền vui lòng thử lại'
        );
    }

    public function loginWithOTP($code)
    {
        $result = $this->OTP($code);
        if($result !== false){
            if($result->status->code == '00'){
                $this->account->authorization = $result->data->accessToken;
                $this->account->refreshToken  = $result->data->refreshToken;
                $result = $this->SESSION();
                if($result !== false){
                    if($result->status->code == '00'){
                        $this->account->session_id = $result->data->otherData->sessionId;
                        $this->account->accNo      = (!empty($result->data->sources->infra)) ? $result->data->sources->infra['0']->accNo : null;
                        $this->account->request_otp = ($result->status->code == 'CS0203') ? 1 : 0;
                        $this->account->status     = 1;
                        // $this->account->save();
                        $result = $this->BALANCE_INQUIRY_NO_PIN();
                        if($result !== false){
                            if($result->response_code == '00'){
                                $this->account->balance = $result->balance;
                                // $this->account->save();
                            }
                        }
                        $this->account->save();
                        return (object) array(
                            'success' => true,
                            'message' => 'Đăng nhập thành công'
                        );
                    }
                }
            }
            return (object) array(
                'success' => false,
                'message' => $result->status->displayMessage
            );
        }
        return (object) array(
            'success' => false,
            'message' => 'Đăng nhập bằng OTP thất bại'
        );
    }

    public function laysaoke($begin = '',$end = '')
    {
        if(empty($begin) || empty($end)) {
            $begin = date('Y-m-01');
            $end   = date('Y-m-t');
        }

        $result = $this->GET_HISTORY_VTP($begin,$end);
        if($result !== false) {
            if($result->responseCode == '00') {
                return (object) array(
                    'success' => true,
                    'message' => 'Truy vấn lịch sử thành công',
                    'results' => $result->listVTPTransactionHistory
                );
            }
        }
        return (object) array(
            'success' => false,
            'message' => 'Truy vấn lịch sử thất bại'
        );
    }

    public function refresh()
    {
        if(time() - $this->session_time < $this->account->updated_at->timestamp && $this->account->status){
            $result = $this->BALANCE_INQUIRY_NO_PIN();
            if($result !== false){
                if($result->response_code == '00'){
                    $this->account->balance = $result->balance;
                    // $this->account->save();
                }
            }
            $this->account->save();
            return (object) array(
                'success' => true,
                'message' => 'Cập nhật thành công'
            );
        }
        $result = $this->LOGIN_AUTH();
        if($result !== false){
            if($result->status->code == '00'){
                $this->account->authorization = $result->data->accessToken;
                $this->account->refreshToken  = $result->data->refreshToken;
                // $this->account->save();
                $result = $this->SESSION();
                if($result !== false){
                    if($result->status->code == '00'){
                        $this->account->session_id = $result->data->otherData->sessionId;
                        $this->account->accNo      = (!empty($result->data->sources->infra)) ? $result->data->sources->infra['0']->accNo : null;
                        $this->account->status     = 1;
                        // $this->account->save();
                        $result = $this->BALANCE_INQUIRY_NO_PIN();
                        if($result !== false){
                            if($result->response_code == '00'){
                                $this->account->balance = $result->balance;
                                // $this->account->save();
                            }
                        }
                        $this->account->save();
                        return (object) array(
                            'success' => true,
                            'message' => 'Cập nhật thành công'
                        );
                    }
                }
            }
            $this->account->requestId = $result->data->requestId;
            $this->account->status = 0;
            $this->account->request_otp = ($result->status->code == 'AUT0014') ? 1 : 0;
            $this->account->save();
            return (object) array(
                'success' => false,
                'message' => $result->status->displayMessage
            );
        }
        return (object) array(
            'success' => false,
            'message' => 'Cập nhật thất bại'
        );
    }

    public function rank()
    {
        $result = $this->GET_RANK();
        if($result !== false) {
            if($result->status->code == '00') {
                $this->account->rankType = $result->data->rankType;
                $this->account->save();
                return (object) array(
                    'success' => true,
                    'message' => 'Thành công'
                );
            }
        }
        return (object) array(
            'success' => false,
            'message' => 'Thất bại'
        );
    }

    private function MONEY_TRANSFER_OUTSIDE_SMS()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'     => 'VIETTELPAY',
                'typeos'       => 'ios',
                'app_version'  => $this->appCode,
                'os_version'   => $this->appVer,
                'imei'         => $this->account->imei,
                'order_id'     => $this->get_order_id(),
                'money_source_bank_code' => 'VTT',
                'recv_cust_mobile'       => '',
                'bank_code'    => 'VTT',
                'recv_bank_branch_name'  => $this->account->accounts->ben_name,
                'session_id'   => $this->account->session_id,
                'trans_content'=> $this->account->accounts->comment,
                'trans_amount' => $this->account->accounts->amount,
                'recv_cust_bank_acc' => $this->account->accounts->bank_acc,
                'recv_bankcode'=> $this->account->accounts->bankcode,
                'money_source' => $this->account->accNo,
                'pin'          => $this->account->password
            ));
            $request = $this->xml_encrypt('MONEY_TRANSFER_OUTSIDE_SMS',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function MONEY_TRANSFER_OUTSIDE_SMS_OTP()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'     => 'VIETTELPAY',
                'typeos'       => 'ios',
                'app_version'  => $this->appCode,
                'os_version'   => $this->appVer,
                'imei'         => $this->account->imei,
                'order_id'     => $this->get_order_id(),
                'money_source_bank_code' => 'VTT',
                'recv_cust_mobile'       => '',
                'bank_code'    => 'VTT',
                'recv_bank_branch_name'  => $this->account->accounts->ben_name,
                'session_id'   => $this->account->session_id,
                'trans_content'=> $this->account->accounts->comment,
                'trans_amount' => $this->account->accounts->amount,
                'recv_cust_bank_acc' => $this->account->accounts->bank_acc,
                'recv_bankcode'=> $this->account->accounts->bankcode,
                'money_source' => $this->account->accNo,
                'pin'          => $this->account->password,
                'otp_order_id' => $this->account->accounts->order_id,
                'otp_code'     => $this->account->accounts->otp
            ));
            $request = $this->xml_encrypt('MONEY_TRANSFER_OUTSIDE_SMS_OTP',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function GET_BENNAME_FROM_ACCOUNT_NUMBER()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'           => 'VIETTELPAY',
                'typeos'             => 'ios',
                'app_version'        => $this->appCode,
                'os_version'         => $this->appVer,
                'imei'               => $this->account->imei,
                'order_id'           => $this->get_order_id(),
                'session_id'         => $this->account->session_id,
                'recv_cust_bank_acc' => $this->account->accounts->bank_acc,
                'recv_bankcode'      => $this->account->accounts->bankcode,
            ));
            $request = $this->xml_encrypt('GET_BENNAME_FROM_ACCOUNT_NUMBER',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function GET_LIST_BANK_FROM_MSISDN()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'     => 'VIETTELPAY',
                'typeos'       => 'ios',
                'app_version'  => $this->appCode,
                'os_version'   => $this->appVer,
                'imei'         => $this->account->imei,
                'order_id'     => $this->get_order_id(),
                'session_id'   => $this->account->session_id,
                'ben_msisdn'   => '',
            ));
            $request = $this->xml_encrypt('MONEY_TRANSFER_INSIDE_SMS_OTP',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function MONEY_TRANSFER_INSIDE_SMS_OTP()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'          => 'VIETTELPAY',
                'typeos'            => 'ios',
                'app_version'       => $this->appCode,
                'os_version'        => $this->appVer,
                'imei'              => $this->account->imei,
                'order_id'          => $this->get_order_id(),
                'session_id'        => $this->account->session_id,
                'recv_cust_mobile'  => $this->account->accounts->receiver,
                'recv_cust_bank_acc'=> '',
                'recv_bankcode'     => 'VTT',
                'trans_amount'      => $this->account->accounts->amount,
                'trans_content'     => $this->account->accounts->comment,
                'service_type'      => '0',
                'money_source'      => $this->account->accNo,
                'money_source_bank_code' => 'VTT',
                'otp_order_id'      => $this->account->accounts->order_id,
                'otp_code'          => $this->account->accounts->otp,
                'pin'               => $this->account->password
            ));
            $request = $this->xml_encrypt('MONEY_TRANSFER_INSIDE_SMS_OTP',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function MONEY_TRANSFER_INSIDE_SMS()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'          => 'VIETTELPAY',
                'typeos'            => 'ios',
                'app_version'       => $this->appCode,
                'os_version'        => $this->appVer,
                'imei'              => $this->account->imei,
                'order_id'          => $this->get_order_id(),
                'session_id'        => $this->account->session_id,
                'recv_cust_mobile'  => $this->account->accounts->receiver,
                'recv_cust_bank_acc'=> '',
                'recv_bankcode'     => 'VTT',
                'trans_amount'      => $this->account->accounts->amount,
                'trans_content'     => $this->account->accounts->comment,
                'service_type'      => '0',
                'money_source'      => $this->account->accNo,
                'money_source_bank_code' => 'VTT',
            ));
            $request = $this->xml_encrypt('MONEY_TRANSFER_INSIDE_SMS',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function CHANGE_CURRENT_MONEY_SOURCE($amount, $cust_code)
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query([
                'is_lixi'       => '0',
                'bank_code'     => 'VTT',
                'provider_code' => 'VIETTEL',
                'app_name'      => 'VIETTELPAY',
                'money_source_bank_code' => 'VTT',
                'app_version'   => $this->appCode,
                'order_id'      => $this->get_order_id(),
                'status_mm'     => '2',
                'customer_type' => 'TT',
                'type_os'       => 'ios',
                'imei'          => $this->account->imei,
                'trans_amount'  => $amount,
                'is_new'        => '1',
                'cust_code'     => $cust_code,
                'money_source'  => $this->account->accNo,
                'os_version'    => $this->appVer,
                'session_id'    => $this->account->session_id,
                'service_code'  => '100000',
                'pin'           => $this->account->password
            ]);
            $request = $this->xml_encrypt('CHANGE_CURRENT_MONEY_SOURCE',$this->encrypt($request),$this->signature($request));

            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function TELCO_PAYMENT_SMS($cust_code, $amount,$gift)
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query([
                'pin'           => $this->account->password,
                'session_id'    => $this->account->session_id,
                'status_mm'     => '0',
                'money_source_bank_code' => 'VTT',
                'type_os'       => 'ios',
                'app_name'      => 'VIETTELPAY',
                'app_version'   => $this->appCode,
                'customer_type' => 'TT',
                'imei'          => $this->account->imei,
                'is_new'        => '1',
                'cust_code'     => $cust_code,
                'transfer_type' => 'RECHARGE_YOURSELF',
                'order_id'      => $this->get_order_id(),
                'money_source'  => $this->account->accNo,
                'provider_code' => 'VIETTEL',
                'bank_code'     => 'VTT',
                'trans_amount'  => $amount,
                'os_version'    => $this->appVer,
                'service_code'  => '100000',
                'is_lixi'       => '0',
                'voucher_code'  => $gift,
                'prepaid_code'  => $gift
            ]);
            $request = $this->xml_encrypt('TELCO_PAYMENT_SMS',$this->encrypt($request),$this->signature($request));

            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function TELCO_PAYMENT_SMS_OTP($cust_code, $amount, $otp, $order_id, $gift)
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query([
                'pin'           => $this->account->password,
                'session_id'    => $this->account->session_id,
                'status_mm'     => '0',
                'money_source_bank_code' => 'VTT',
                'type_os'       => 'ios',
                'app_name'      => 'VIETTELPAY',
                'app_version'   => $this->appCode,
                'customer_type' => 'TT',
                'imei'          => $this->account->imei,
                'is_new'        => '1',
                'cust_code'     => $cust_code,
                'transfer_type' => 'RECHARGE_YOURSELF',
                'order_id'      => $this->get_order_id(),
                'money_source'  => $this->account->accNo,
                'provider_code' => 'VIETTEL',
                'bank_code'     => 'VTT',
                'trans_amount'  => $amount,
                'os_version'    => $this->appVer,
                'service_code'  => '100000',
                'voucher_code'  => $gift,
                'prepaid_code'  => $gift,
                'is_lixi'       => '0',
                'otp_order_id'  => $order_id,
                'otp_code'      => $otp
            ]);
            $request = $this->xml_encrypt('TELCO_PAYMENT_SMS_OTP',$this->encrypt($request),$this->signature($request));

            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function GET_TRANSACTION_FEE()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'          => 'VIETTELPAY',
                'typeos'            => 'ios',
                'app_version'       => $this->appCode,
                'os_version'        => $this->appVer,
                'imei'              => $this->account->imei,
                'order_id'          => $this->get_order_id(),
                'session_id'        => $this->account->session_id,
                'trans_content'     => json_encode(array (
                    0 => array (
                        'sourceBankCode' => 'VTT',
                        'transType'      => '3',
                        'serviceCode'    => 'TELCO_VIETTEL',
                        'transAmount'    => '5000',
                        'fee'            => '0',
                        'packageBank'    => 'VTT_BANKPLUS_ECO',
                        'walletSrcMoney' => 'VTT',
                        'discount'       => '3-5',
                        'provinceName'   => '',
                        'placeOfIssue'   => '',
                        'discountDefault' => '3-5',
                        'billCode'       => '**********',
                        'packageCode'    => '',
                        'paymentType'    => 'FINANCE',
                        'paymentTypeInternetVT' => '',
                    ),
                    1 => array (
                        'sourceBankCode' => 'MM',
                        'transType'      => '3',
                        'serviceCode'    => 'TOPUP_SRC_MM',
                        'transAmount'    => '5000',
                        'fee'            => '0',
                        'packageBank'    => '',
                        'walletSrcMoney' => '',
                        'discount'       => '0',
                        'provinceName'   => '',
                        'placeOfIssue'   => '',
                        'discountAmount' => '0',
                        'discountDefault' => '0',
                        'feeErrorCode'   => '00',
                        'packageCode'    => 'VT',
                        'finalAmount'    => '5000',
                    ),
                ), JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE),
                'service_type'      => '0',
                'money_source'      => $this->account->accNo,
                'money_source_bank_code' => 'VTT',
            ));
            $request = $this->xml_encrypt('GET_TRANSACTION_FEE',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function GET_RANK()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $res = $client->request('GET','https://api8.viettelpay.vn/loyalty/mobile/v2/accounts/get-account-rank?msisdn=' . $this->account->username,array(
                'headers' => array(
                    'host'            => 'api8.viettelpay.vn',
                    'content-type'    => 'application/json',
                    'accept'          => '*/*',
                    'app_version'     => $this->appCode,
                    'product'         => 'VIETTELPAY',
                    'type_os'         => 'ios',
                    'accept-language' => 'vi',
                    'imei'            => $this->account->imei,
                    'user-agent'      => 'ViettelPay/'.$this->appCode.' (com.viettel.viettelpay; build:1; iOS 15.4) Alamofire/'.$this->appCode.'',
                    'os_version'      => $this->appVer,
                    'authority-party' => 'APP',
                    'authorization'   => 'Bearer '. $this->account->authorization
                ),
                'json'    => array(
                    'refreshToken' => $this->account->refreshToken
                )
            ));
            return json_decode($res->getBody());
        }
        catch (\Throwable $e){}
        return false;
    }

    private function SESSION()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $res = $client->request('GET','https://api8.viettelpay.vn/customer/v1/accounts?sources=1&recommendations=1',array(
                'headers' => array(
                    'host'            => 'api8.viettelpay.vn',
                    'content-type'    => 'application/json',
                    'accept'          => '*/*',
                    'app_version'     => $this->appCode,
                    'product'         => 'VIETTELPAY',
                    'type_os'         => 'ios',
                    'accept-language' => 'vi',
                    'imei'            => $this->account->imei,
                    'user-agent'      => 'ViettelPay/'.$this->appCode.' (com.viettel.viettelpay; build:1; iOS 15.4) Alamofire/'.$this->appCode.'',
                    'os_version'      => $this->appVer,
                    'authority-party' => 'APP',
                    'authorization'   => 'Bearer '. $this->account->authorization
                )
            ));
            return json_decode($res->getBody());
        }
        catch (\Throwable $e){}
        return false;
    }

    private function BALANCE_INQUIRY_NO_PIN()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'    => 'VIETTELPAY',
                'typeos'      => 'ios',
                'app_version' => $this->appCode,
                'os_version'  => $this->appVer,
                'imei'        => $this->account->imei,
                'order_id'    => $this->get_order_id(),
                'session_id'  => $this->account->session_id,
                'bank_code'   => 'VTT',
                'money_source'=> $this->account->accNo,
                'money_source_bank_code' => 'VTT'
            ));
            $request = $this->xml_encrypt('BALANCE_INQUIRY_NO_PIN',$this->encrypt($request),$this->signature($request));
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => trim($request)
            ));
            return $this->decrypt($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function GET_HISTORY_VTP($begin,$end)
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $request = $this->query(array(
                'app_name'       => 'VIETTELPAY',
                'type_os'        => 'ios',
                'app_version'    => $this->appCode,
                'os_version'     => $this->appVer,
                'imei'           => $this->account->imei,
                'order_id'       => $this->get_order_id(),
                'session_id'     => $this->account->session_id,
                'account_number' => $this->account->username,
                'start_date'     => $begin,
                'end_date'       => $end,
                'process_code'   => '0',
                'service_code'   => '',
                'bank_code_query'=> 'ALL',
                'page'           => '0',
                'transfer_type'  => '1',
            ));

            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host'         => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection'   => 'keep-alive',
                    'SOAPAction'   => 'gwOperator',
                    'Accept'       => '*/*',
                    'User-Agent'   => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => $this->xml_encrypt('GET_HISTORY_VTP',$this->encrypt($request),$this->signature($request))
            ));
            return json_decode($this->xml_decrypt($res->getBody()->getContents()));
        }
        catch (\Throwable $e){}
        return false;
    }

    private function OTP($code)
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $res = $client->request('POST','https://api8.viettelpay.vn/auth/v1/authn/login',array(
                'headers' => array(
                    'host'            => 'api8.viettelpay.vn',
                    'content-type'    => 'application/json',
                    'accept'          => '*/*',
                    'app_version'     => $this->appCode,
                    'product'         => 'VIETTELPAY',
                    'type_os'         => 'ios',
                    'accept-language' => 'vi',
                    'imei'            => $this->account->imei,
                    'user-agent'      => 'ViettelPay/'.$this->appCode.' (com.viettel.viettelpay; build:1; iOS 15.4) Alamofire/'.$this->appCode.'',
                    'os_version'      => $this->appVer,
                    'authority-party' => 'APP',
                ),
                'json' => array(
                    'typeOs'    => 'iOS',
                    'notifyToken' => $this->account->token_notification,
                    'userType'  => 'msisdn',
                    'pin'       => $this->account->password,
                    'imei'      => $this->account->imei,
                    'msisdn'    => $this->account->username,
                    'loginType' => 'BASIC',
                    'otp'       => $code,
                    'username'  => $this->account->username,
                    'requestId' => $this->account->requestId,
                )
            ));
            return json_decode($res->getBody());
        }
        catch (\Throwable $e){}
        return false;
    }

    private function CHECK_USER()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $res = $client->request('POST','https://api8.viettelpay.vn/customer/v1/validate/account',array(
                'headers' => array(
                    'host'            => 'api8.viettelpay.vn',
                    'content-type'    => 'application/json',
                    'accept'          => '*/*',
                    'app_version'     => $this->appCode,
                    'product'         => 'VIETTELPAY',
                    'type_os'         => 'ios',
                    'accept-language' => 'vi',
                    'imei'            => $this->account->imei,
                    'user-agent'      => 'ViettelPay/'.$this->appCode.' (com.viettel.viettelpay; build:1; iOS 15.4) Alamofire/'.$this->appCode.'',
                    'os_version'      => $this->appVer,
                    'authority-party' => 'APP',
                ),
                'json' => array(
                    'username' => $this->account->username,
                    'type'     => 'msisdn',
                ),
            ));
            return json_decode($res->getBody());
        }
        catch (\Throwable $e){}
        return false;
    }

    private function LOGIN_AUTH()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $res = $client->request('POST','https://api8.viettelpay.vn/auth/v1/authn/login',array(
                'headers' => array(
                    'host'            => 'api8.viettelpay.vn',
                    'content-type'    => 'application/json',
                    'accept'          => '*/*',
                    'app_version'     => $this->appCode,
                    'product'         => 'VIETTELPAY',
                    'type_os'         => 'ios',
                    'accept-language' => 'vi',
                    'imei'            => $this->account->imei,
                    'user-agent'      => 'ViettelPay/'.$this->appCode.' (com.viettel.viettelpay; build:1; iOS 15.4) Alamofire/'.$this->appCode.'',
                    'os_version'      => $this->appVer,
                    'authority-party' => 'APP',
                ),
                'json' => array(
                    'userType'    => 'msisdn',
                    'loginType'   => 'BASIC',
                    'pin'         => $this->account->password,
                    'msisdn'      => $this->account->username,
                    'imei'        => $this->account->imei,
                    'username'    => $this->account->username,
                    'notifyToken' => $this->account->token_notification,
                    'typeOs'      => 'iOS',
                )
            ));
            return json_decode($res->getBody());
        }
        catch (\Throwable $e){}
        return false;
    }

    private function SETUP_SOFTWARE()
    {
        $client = new Client([
            'proxy'   => $this->account->proxy,
            'timeout' => 10
        ]);
        try {
            $res = $client->request('POST','https://bankplus.vn/MobileAppService2/ServiceAPI',array(
                'headers' => array(
                    'Host' => 'bankplus.vn',
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'Connection' => 'keep-alive',
                    'SOAPAction' => 'gwOperator',
                    'Accept' => '*/*',
                    'User-Agent' => 'ViettelPay/'.$this->appCode.' (iPhone; iOS 15.4; Scale/3.00)',
                    'Accept-Language' => 'vi;q=1',
                ),
                'body'    => $this->xml_encrypt('SETUP_SOFTWARE',urlencode(http_build_query(array(
                    'app_version' => $this->appCode,
                    'order_id'    => $this->get_order_id(),
                    'imei'        => $this->account->imei,
                    'os_version'  => $this->appVer,
                    'type_os'     => 'ios',
                    'token_notification' => $this->account->token_notification,
                    'app_name'    => 'VIETTELPAY',
                ))),'null')
            ));
            return $res->getBody()->getContents();
        }
        catch (\Throwable $e){}
        return false;
    }

    private function xml_encrypt($cmd, $data, $singature)
    {
        $xmlheader = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ba=\"http://bankplus.vn\"><SOAP-ENV:Header/> \r\n<SOAP-ENV:Body>\r\n<ba:gwOperator><cmd>";
        $xmlheader .= $cmd . "</cmd>";
        $xmlheader .= "<encrypted>".$data."</encrypted>";
        $xmlheader .= "<signature>".$singature."</signature>";
        $xmlheader .= "</ba:gwOperator>";
        $xmlheader .= "\r\n";
        $xmlheader .= "</SOAP-ENV:Body>";
        $xmlheader .= "\r\n";
        $xmlheader .= "</SOAP-ENV:Envelope>";
        return $xmlheader;
    }

    private function xml_decrypt($encrypted)
    {
        $string = '';
        $explode = explode('&amp', $encrypted);
        foreach ($explode as $item){
            if(strstr($item, 'encrypted')){
                $encrypted = explode('encrypted=', $item)[1];
                break;
            }
        }
        $array = str_split($encrypted, 344);
        foreach ($array as $rows) {
            $string .= openssl_private_decrypt(strrev(base64_decode($rows)),$decrypted_data,$this->account->client_private_key,OPENSSL_PKCS1_PADDING) ? $decrypted_data : null;
        }

        return $string;
    }

    private function encrypt($request)
    {
        $string = '';
        $array  = str_split($request, 86);
        foreach ($array as $item) {
            if(openssl_public_encrypt($item,$encrypted_data,$this->account->viettel_public_key,OPENSSL_PKCS1_PADDING)) {
                $base64  = strrev($encrypted_data);
                $string .= base64_encode($base64);
            }
        }
        return $string;
    }

    private function decrypt($encrypted)
    {
        if (!is_object(json_decode($encrypted))) {
            $array   = array();
            $explode = explode('&',$encrypted);
            foreach ($explode as $item) {
                $exp = explode('=',$item);
                switch (count($exp)) {
                    case 1:
                        break;
                    case 2:
                        $array[$exp[0]] = $exp[1];
                        break;
                    default:
                        $array[$exp[0]] = substr($item, strlen($exp[0]) + 1);
                        break;
                }

            }
            return (object) $array;
        }
        return json_decode($encrypted);
    }

    private function signature($request)
    {
        openssl_sign($request,$singature,$this->account->client_private_key,OPENSSL_ALGO_SHA1);
        return base64_encode($singature);
    }

    private function query(array $array)
    {
        $string = '';
        foreach ($array as $keys => $item) {

            $string .= $keys;
            $string .= '='.$item;
            $string .= '&';

        }
        return rtrim($string,'&');
    }

    private function generateRsa() : void
    {
        $result = $this->SETUP_SOFTWARE();
        if($result !== false){
            $explode = explode('&amp', $result);
            foreach ($explode as $values){
                if(strstr($values, 'client_private_key')){
                    $client_private_key = substr($values, 20);
                    $this->account->client_private_key = "-----BEGIN PRIVATE KEY-----\n" . $client_private_key ."\n-----END PRIVATE KEY-----";
                }
                else if(strstr($values, 'viettel_public_key')){
                    $viettel_public_key = substr($values, 20);
                    $this->account->viettel_public_key = "-----BEGIN PUBLIC KEY-----\n" . $viettel_public_key ."\n-----END PUBLIC KEY-----";

                }
            }
        }
    }

    private function get_order_id()
    {
        return date('Ymdhis');
    }

}

?>
