<?php

namespace App\Jobs;

use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\HookLog;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ScanTransactionVCB implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BankAccount $vcb
    ) {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Bank Scan transactions started - Vietcombank: ' . $this->vcb->username);
        $result = vietcombank()->loadByPhone($this->vcb->username, $this->vcb->account_no)
            ->saoketaikhoanNew($this->vcb->account_no, 'D');
        if (!$result->success) {
            $result = vietcombank()->loadByPhone($this->vcb->username, $this->vcb->account_no)->loginAuthNew($this->vcb->password);
            if (!$result->success) {
                Log::info('Bank Login fail - Vietcombank: ' . $this->vcb->username);
                return;
            }
            $result = vietcombank()->loadByPhone($this->vcb->username, $this->vcb->account_no)
                ->saoketaikhoanNew($this->vcb->account_no, 'D');
        }

        if (isset($result->results)) {
            $exitsTransactions = BankTransaction::where('bank_account_id', $this->vcb->id)->get()->pluck('cus_tran_id')->toArray();
            foreach ($result->results as $item) {
                $transactionId = now()->parse($item->PostingTime)
                    ->format('Ymd') . $item->PostingTime . '|' . $item->Reference;
                $actualTime = now()->parse($item->PostingDate . ' ' . $item->PostingTime);
                if (!in_array($transactionId, $exitsTransactions)) {
                    BankTransaction::create([
                        'bank_account_id' => $this->vcb->id,
                        'user_id' => $this->vcb->user_id,
                        'transaction_id' => $item->Reference,
                        'cus_tran_id' => $transactionId,
                        'memo' => $item->Description,
                        'amount' => (int)str_replace(',', '', $item->Amount),
                        'type' => $item->CD == "+" ? 1 : 2,
                        'status' => 0,
                        'actual_at' => $actualTime,
                    ]);

                    if ($this->vcb->activeHooks) {
                        $time = $actualTime->format('d-m-Y H:i:s');
                        foreach ($this->vcb->activeHooks as $hook) {
                            if (
                                $hook->event == 'all'
                                || ($hook->event == 'in' && $item->CD == "+")
                                || ($hook->event == 'out' && $item->CD != "+")
                            ) {
                                $dataHook = [
                                    'bank_name' => $this->vcb->bank->name,
                                    'account_no' => $this->vcb->account_no,
                                    'account_holder_name' => $this->vcb->account_holder_name,
                                    'bank_code' => $this->vcb->bank_code,
                                    'amount' => (int)str_replace(',', '', $item->Amount),
                                    'time' => $time,
                                    'description' => $item->Description,
                                    'reference' => $item->Reference,
                                    'type' => $item->CD,
                                ];

                                if ($hook->hook_type == 'larksuite') {
                                    $result = $this->larkService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'slack') {
                                    $result = $this->slackService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'webhook') {
                                    $result = $this->hookService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'telegram') {
                                    $result = $this->telegramService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }
        Log::info('Bank Scan transactions ended - Vietcombank: ' . $this->vcb->username);
    }
}
