<?php

namespace App\Jobs;

use App\Models\BankAccount;
use App\Models\HookLog;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PushHook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected BankAccount $bankAccount,
        protected $transaction
    ) {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach ($this->bankAccount->activeHooks as $hook) {
            if ($hook->skip_if_no_code) {
                $payCodes = $this->bankAccount->user->pay_codes;
                if (empty($payCodes)) {
                    continue;
                }
                $match = false;
                //memo: MBVCB.***********.416147.LE THANH TRUNG DH123456 chuyen tien.CT tu ************* LE THANH TRUNG toi V4TINO8888 CONG TY CO PHAN TAP DOAN TINO tai BIDV
                foreach ($payCodes as $payCode) {
                    $prefix = $payCode['prefix'];
                    $suffixFrom = $payCode['suffix_from'];
                    $suffixTo = $payCode['suffix_to'];
                    $characterType = $payCode['character_type'];
                    $memo = $this->transaction->memo;
                    if (strpos($memo, $prefix) === false) {
                        continue;
                    }
                    $suffix = substr($memo, strlen($prefix));
                    if (strlen($suffix) < $suffixFrom || strlen($suffix) > $suffixTo) {
                        continue;
                    }
                    if ($characterType == 'NumberOnly' && !ctype_digit($suffix)) {
                        continue;
                    }
                    $match = true;
                    break;
                }
                if (!$match) {
                    continue;
                }
            }
            if (
                $hook->event == 'all'
                || ($hook->event == 'in' && $this->transaction->type == 1)
                || ($hook->event == 'out' && $this->transaction->type == 2)
            ) {
                $dataHook = [
                    'bank_name' => $this->bankAccount->bank->name,
                    'account_no' => $this->bankAccount->account_no,
                    'account_holder_name' => $this->bankAccount->account_holder_name,
                    'bank_code' => $this->bankAccount->bank_code,
                    'amount' => $this->transaction->amount,
                    'time' => now()->parse($this->transaction->actual_at)->format('d-m-Y H:i:s'),
                    'description' => $this->transaction->memo,
                    'reference' => $this->transaction->coreRefNo ?? $this->transaction->transaction_id,
                    'type' => $this->transaction->type == 1 ? '+' : '-',

                    'transaction_id' => $this->transaction->transaction_id ?? null,
                    'gateway' => $this->bankAccount->bank_code,
                    'transactionDate' => now()->parse($this->transaction->actual_at)->format('Y-m-d H:i:s'),
                    'accountNumber' => $this->bankAccount->account_no,
                    'code' => null,
                    'content' => $this->transaction->memo,
                    'transferType' => $this->transaction->type == 1 ? 'in' : 'out',
                    'transferAmount' => $this->transaction->amount,
                    'referenceCode' => $this->transaction->coreRefNo ?? null,

                ];

                if ($hook->hook_type == 'larksuite') {
                    $result = $this->larkService->sendHook($hook->endpoint, $dataHook);
                    HookLog::create([
                        'hook_id' => $hook->id,
                        'user_id' => $hook->user_id,
                        'response' => $result,
                    ]);
                }
                if ($hook->hook_type == 'slack') {
                    $result = $this->slackService->sendHook($hook->endpoint, $dataHook);
                    HookLog::create([
                        'hook_id' => $hook->id,
                        'user_id' => $hook->user_id,
                        'response' => $result,
                    ]);
                }
                if ($hook->hook_type == 'webhook') {
                    $result = $this->hookService->sendHook($hook, $dataHook);
                    HookLog::create([
                        'hook_id' => $hook->id,
                        'user_id' => $hook->user_id,
                        'response' => $result,
                    ]);
                }
                if ($hook->hook_type == 'telegram') {
                    $result = $this->telegramService->sendHook($hook, $dataHook);
                    HookLog::create([
                        'hook_id' => $hook->id,
                        'user_id' => $hook->user_id,
                        'response' => $result,
                    ]);
                }
            }
        }
    }
}
