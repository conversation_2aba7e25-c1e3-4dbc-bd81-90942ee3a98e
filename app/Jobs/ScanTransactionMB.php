<?php

namespace App\Jobs;

use App\Bank\Acb;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\HookLog;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ScanTransactionMB implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BankAccount $mb
    ) {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Bank Scan transactions started - MB: ' . $this->mb->username);

        $mbClient = mbank();
        $load_info = $mbClient->loadByUsername($this->mb->username, $this->mb->account_no);

        $response = $load_info->getTransactionHistoryWEB(date("d/m/Y"), date("d/m/Y"), $this->mb->account_no);
        if ($response && !$response->result->ok) {
            $load_info->loginAuth();
            $response = $load_info->getTransactionHistoryWEB(date("d/m/Y"), date("d/m/Y"), $this->mb->account_no);
        }

        if ($response && $response->result->ok == true && !empty($response->transactionHistoryList)) {
            $exitsTransactions = BankTransaction::where('bank_account_id', $this->mb->id)->get()->pluck('cus_tran_id')->toArray();
            foreach ($response->transactionHistoryList as $item) {
                $transactionId = $item->refNo;
                $actualTime = now()->parse(str_replace("/", "-", $item->transactionDate));
                if (!in_array($transactionId, $exitsTransactions)) {
                    BankTransaction::create([
                        'bank_account_id' => $this->mb->id,
                        'user_id' => $this->mb->user_id,
                        'transaction_id' => $item->refNo,
                        'cus_tran_id' => $transactionId,
                        'memo' => $item->description,
                        'amount' => $item->creditAmount != 0
                            ? (int)str_replace(',', '', $item->creditAmount) : ($item->debitAmount != 0
                                ? (int)str_replace(',', '', $item->debitAmount) : 0),
                        'type' => $item->creditAmount != 0 ? 1 : 2,
                        'status' => 0,
                        'actual_at' => $actualTime,
                    ]);

                    if ($this->mb->activeHooks) {
                        $time = $actualTime->format('d-m-Y H:i:s');
                        foreach ($this->mb->activeHooks as $hook) {
                            if (
                                $hook->event == 'all'
                                || ($hook->event == 'in' && $item->creditAmount != 0)
                                || ($hook->event == 'out' && $item->creditAmount == 0)
                            ) {
                                $dataHook = [
                                    'bank_name' => $this->mb->bank->name,
                                    'account_no' => $this->mb->account_no,
                                    'account_holder_name' => $this->mb->account_holder_name,
                                    'bank_code' => $this->mb->bank_code,
                                    'amount' => $item->creditAmount != 0
                                        ? (int)str_replace(',', '', $item->creditAmount) : ($item->debitAmount != 0
                                            ? (int)str_replace(',', '', $item->debitAmount) : 0),
                                    'time' => $time,
                                    'description' => $item->description,
                                    'reference' => $item->refNo,
                                    'type' => $item->creditAmount != 0 ? '+' : '-',
                                ];

                                if ($hook->hook_type == 'larksuite') {
                                    $result = $this->larkService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'slack') {
                                    $result = $this->slackService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'webhook') {
                                    $result = $this->hookService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'telegram') {
                                    $result = $this->telegramService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }
        Log::info('Bank Scan transactions ended - MB: ' . $this->mb->username);
    }
}
