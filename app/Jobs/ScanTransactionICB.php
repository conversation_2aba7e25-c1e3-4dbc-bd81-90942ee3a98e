<?php

namespace App\Jobs;

use App\Bank\Acb;
use App\Bank\Vietinbank;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\HookLog;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ScanTransactionICB implements ShouldQueue
{
    //vietinbank
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BankAccount $icb
    ) {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Bank Scan transactions started - Vietinbank: ' . $this->icb->username);

        $vietinbank = new Vietinbank($this->icb->username, $this->icb->password, $this->icb->account_no);


        $beginTime = Carbon::now()->format("d-m-Y");
        $endTime = Carbon::now()->addDays(1)->format("d-m-Y");
        $begin = strtotime($beginTime) * 1000;
        $end = strtotime($endTime) * 1000;

        $results = $vietinbank->getTransaction();
        if ($results && ($results->error !== false || $results->error == "Bad Request")) {
            $login = $vietinbank->doLogin();
            $results = $vietinbank->getTransaction();
        }

        if ($results && $results->error === false && $results->transactions) {
            $exitsTransactions = BankTransaction::where('bank_account_id', $this->icb->id)->get()->pluck('cus_tran_id')->toArray();
            foreach ($results->transactions as $item) {
                $transactionId = $item->trxId;
                $actualTime = now()->parse($item->processDate);
                if (!in_array($transactionId, $exitsTransactions)) {
                    BankTransaction::create([
                        'bank_account_id' => $this->icb->id,
                        'user_id' => $this->icb->user_id,
                        'transaction_id' => $item->trxId,
                        'cus_tran_id' => $item->trxId,
                        'memo' => $item->remark,
                        'amount' => (int)str_replace(',', '', $item->amount),
                        'type' => $item->dorC == 'C' ? 1 : 2,
                        'status' => 0,
                        'actual_at' => $actualTime,
                    ]);

                    if ($this->icb->activeHooks) {
                        $time = $actualTime->format('d-m-Y H:i:s');
                        foreach ($this->icb->activeHooks as $hook) {
                            if (
                                $hook->event == 'all'
                                || ($hook->event == 'in' && $item->creditAmount != 0)
                                || ($hook->event == 'out' && $item->creditAmount == 0)
                            ) {
                                $dataHook = [
                                    'bank_name' => $this->icb->bank->name,
                                    'account_no' => $this->icb->account_no,
                                    'account_holder_name' => $this->icb->account_holder_name,
                                    'bank_code' => $this->icb->bank_code,
                                    'amount' => (int)str_replace(',', '', $item->amount),
                                    'time' => $time,
                                    'description' => $item->remark,
                                    'reference' => $item->trxId,
                                    'type' => $item->dorC == 'C' ? '+' : '-',
                                ];

                                if ($hook->hook_type == 'larksuite') {
                                    $result = $this->larkService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'slack') {
                                    $result = $this->slackService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'webhook') {
                                    $result = $this->hookService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'telegram') {
                                    $result = $this->telegramService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }
        Log::info('Bank Scan transactions ended - Vietinbank: ' . $this->icb->username);
    }
}
