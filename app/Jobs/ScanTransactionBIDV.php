<?php

namespace App\Jobs;

use App\Bank\Acb;
use App\Bank\Bidv;
use App\Bank\Vietinbank;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\HookLog;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ScanTransactionBIDV implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BankAccount $bidv
    ) {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Bank Scan transactions started - BIDV: ' . $this->bidv->username);


        $bidvClient = new Bidv($this->bidv->username, $this->bidv->password, $this->bidv->account_no);
        $bidvClient = $bidvClient->loadData();


        $beginTime = Carbon::now()->format("d-m-Y");
        $endTime = Carbon::now()->addDays(1)->format("d-m-Y");
        $begin = strtotime($beginTime) * 1000;
        $end = strtotime($endTime) * 1000;

        $results = $bidvClient->getTransactions();

        if (($results && isset($results['status']) && $results['status'] == false)
            || (isset($results['mid']) && $results['mid'] == 12 && isset($results['code']) && $results['code'] != '00')
        ) {
            $login = $bidvClient->doLogin();
            $results = $bidvClient->getTransactions();
        }

        if ($results && isset($results['code']) && $results['code'] == 00 && isset($results['txnList'])) {
            $exitsTransactions = BankTransaction::where('bank_account_id', $this->bidv->id)->get()->pluck('cus_tran_id')->toArray();
            foreach ($results['txnList'] as $item) {
                $transactionId = $item['refNo'];
                $actualTime = now()->parse(str_replace("/", "-", $item['txnDate']) . ' ' . $item['txnTime']);
                if (!in_array($transactionId, $exitsTransactions)) {
                    BankTransaction::create([
                        'bank_account_id' => $this->bidv->id,
                        'user_id' => $this->bidv->user_id,
                        'transaction_id' => $item['refNo'],
                        'cus_tran_id' => $item['refNo'],
                        'memo' => $item['txnRemark'],
                        'amount' => (int)str_replace(',', '', $item['amount']),
                        'type' => $item['txnType'] == '+' ? 1 : 2,
                        'status' => 0,
                        'actual_at' => $actualTime,
                    ]);

                    if ($this->bidv->activeHooks) {
                        $time = $actualTime->format('d-m-Y H:i:s');
                        foreach ($this->bidv->activeHooks as $hook) {
                            if (
                                $hook->event == 'all'
                                || ($hook->event == 'in' && $item['txnType'] == '+')
                                || ($hook->event == 'out' && $item['txnType'] == '-')
                            ) {
                                $dataHook = [
                                    'bank_name' => $this->bidv->bank->shortName . ' - ' . $this->bidv->bank->name,
                                    'account_no' => $this->bidv->account_no,
                                    'account_holder_name' => $this->bidv->account_holder_name,
                                    'bank_code' => $this->bidv->bank_code,
                                    'amount' => (int)str_replace(',', '', $item['amount']),
                                    'time' => $time,
                                    'description' => $item['txnRemark'],
                                    'reference' => $item['refNo'],
                                    'type' => $item['txnType'] == '+' ? '+' : '-',
                                ];

                                if ($hook->hook_type == 'larksuite') {
                                    $result = $this->larkService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'slack') {
                                    $result = $this->slackService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'webhook') {
                                    $result = $this->hookService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'telegram') {
                                    $result = $this->telegramService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }
        Log::info('Bank Scan transactions ended - BIDV: ' . $this->bidv->username);
    }
}
