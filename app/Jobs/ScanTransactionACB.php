<?php

namespace App\Jobs;

use App\Bank\Acb;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\Hook;
use App\Models\HookLog;
use App\Services\HaravanService;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ScanTransactionACB implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;
    protected $haravanService;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BankAccount $acb
    ) {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
        $this->haravanService = new HaravanService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Bank Scan transactions started - Acb: ' . $this->acb->username);

        $acb = new Acb();
        $load_info = $acb->loadByUsername($this->acb->username, $this->acb->password, $this->acb->account_no);

        $beginTime = Carbon::now()->format("d-m-Y");
        $endTime = Carbon::now()->addDays(1)->format("d-m-Y");
        $begin = strtotime($beginTime) * 1000;
        $end = strtotime($endTime) * 1000;

        if ($load_info->account->authorization) {
            $results = $load_info->getHistories($begin, $end);
            if (!$results['status']) {
                $load_info->login();
                $results = $load_info->getHistories($begin, $end);
            }
        } else {
            $load_info->login();
            $results = $load_info->getHistories($begin, $end);
        }

        if (isset($results) && $results['status'] == true && isset($results['data']['data'])) {
            $exitsTransactions = BankTransaction::where('bank_account_id', $this->acb->id)->get()->pluck('cus_tran_id')->toArray();
            foreach ($results['data']['data'] as $item) {
                $transactionId = $item['activeDatetime'] . '|' . $item['transactionNumber'];
                $actualTime = now()->parse($item['activeDatetime'] / 1000)->setTimezone('Asia/Ho_Chi_Minh');
                if (!in_array($transactionId, $exitsTransactions)) {
                    BankTransaction::create([
                        'bank_account_id' => $this->acb->id,
                        'user_id' => $this->acb->user_id,
                        'transaction_id' => $item['transactionNumber'],
                        'cus_tran_id' => $transactionId,
                        'memo' => $item['description'],
                        'amount' => (int)str_replace(',', '', $item['amount']),
                        'type' => $item['type'] == 'IN' ? 1 : 2,
                        'status' => 0,
                        'actual_at' => $actualTime,
                    ]);

                    if ($this->acb->activeHooks) {
                        $time = $actualTime->format('d-m-Y H:i:s');
                        foreach ($this->acb->activeHooks as $hook) {
                            if (
                                $hook->event == 'all'
                                || ($hook->event == 'in' && $item['type'] == 'IN')
                                || ($hook->event == 'out' && $item['type'] != 'IN')
                            ) {
                                $dataHook = [
                                    'bank_name' => $this->acb->bank->name,
                                    'account_no' => $this->acb->account_no,
                                    'account_holder_name' => $this->acb->account_holder_name,
                                    'bank_code' => $this->acb->bank_code,
                                    'amount' => (int)str_replace(',', '', $item['amount']),
                                    'time' => $time,
                                    'description' => $item['description'],
                                    'reference' => $item['transactionNumber'],
                                    'type' => $item['type'] == 'IN' ? '+' : '-',
                                ];

                                if ($hook->hook_type == 'larksuite') {
                                    $result = $this->larkService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'slack') {
                                    $result = $this->slackService->sendHook($hook->endpoint, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'webhook') {
                                    $result = $this->hookService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'telegram') {
                                    $result = $this->telegramService->sendHook($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                                if ($hook->hook_type == 'haravan') {
                                    $result = $this->haravanService->sendTransaction($hook, $dataHook);
                                    HookLog::create([
                                        'hook_id' => $hook->id,
                                        'user_id' => $hook->user_id,
                                        'response' => $result,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }
        Log::info('Bank Scan transactions ended - Acb: ' . $this->acb->username);
    }
}
