<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BIDVVaNumber extends Model
{
    use HasFactory;

    protected $table = 'bidv_va_numbers';
    protected $fillable = [
        'va_number',
        'va_name',
        'bank_account_id',
        'status',
    ];

    public function bankAccount()
    {
        return $this->belongsTo(BankAccount::class, 'bank_account_id', 'id');
    }
}
