<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HookLog extends Model
{
    use HasFactory;

    protected $table = 'hook_logs';
    protected $fillable = [
        'user_id',
        'hook_id',
        'response',
    ];

    public function hook()
    {
        return $this->belongsTo(Hook::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
