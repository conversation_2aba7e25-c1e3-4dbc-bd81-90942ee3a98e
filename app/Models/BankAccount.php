<?php

namespace App\Models;

use App\Enums\BankNameEnum;
use App\Enums\OpenAPIEnum;
use App\Enums\StatusBankEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Facades\Crypt;

class BankAccount extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'bank_accounts';
    protected $fillable = [
        'bank_code',
        'user_id',
        'username',
        'password',
        'account_no',
        'account_holder_name',
        'first_va_number',
        'hint_name',
        'is_business',
        'open_api',
        'last_status',
        'api_token',
        'browserToken',
        'tranId',
        'publicKey',
        'privateKey',
        'authorization',
        'access_key',
        'cookies',
        'accounts',
        'time_login',
        'status',
        'sessionId',
        'ref_no',
        'imei',
        'ipayId',
        'currencyCode',
        'E',
    ];

    protected $casts = [
        'is_business' => 'boolean',
        'accounts' => 'object',
        'cookies'  => 'array',
        'open_api'  => OpenAPIEnum::class,
    ];

    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Crypt::encrypt($value);
    }

    public function getPasswordAttribute()
    {
        if (!$this->attributes['open_api']) {
            return Crypt::decrypt($this->attributes['password']);
        }

        return $this->attributes['password'];
    }

    public function scopeVcb(Builder $query): void
    {
        $query->where('bank_code', 'vcb');
    }

    public function scopeAcb(Builder $query): void
    {
        $query->where('bank_code', 'acb');
    }

    public function scopeMb(Builder $query): void
    {
        $query->where('bank_code', 'mb');
    }

    public function scopeIcb(Builder $query): void
    {
        $query->where('bank_code', 'icb');
    }

    public function scopeBidv(Builder $query): void
    {
        $query->where('bank_code', 'bidv');
    }

    public function scopeBusiness(Builder $query): void
    {
        $query->where('is_business', true);
    }

    public function hooks()
    {
        return $this->hasMany(Hook::class, 'bank_account_id', 'id');
    }

    public function bank()
    {
        return $this->hasOne(Bank::class, 'code', 'bank_code');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function activeHooks()
    {
        return $this->hasMany(Hook::class, 'bank_account_id', 'id')->where('status', 1);
    }

    public function transactions()
    {
        return $this->hasMany(BankTransaction::class, 'bank_account_id', 'id');
    }

    public function vaNumbers()
    {
        return $this->hasMany(BIDVVaNumber::class, 'bank_account_id', 'id');
    }
}
