<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VietQrStore extends Model
{
    use HasFactory;

    protected $table = 'vietqr_stores';
    protected $fillable = [
        'user_id',
        'slug',
        'bank_bin',
        'account_no',
        'account_name',
        'frame_id',
        'is_mask',
        'memo',
        'amount',
    ];

    public function bank()
    {
        return $this->hasOne(Bank::class, 'bin', 'bank_bin');
    }
}
