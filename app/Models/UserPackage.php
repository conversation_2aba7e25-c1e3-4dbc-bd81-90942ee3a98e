<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserPackage extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'user_packages';
    protected $fillable = [
        'user_id',
        'package_id',
        'start_at',
        'end_at',
        'expire_at',
    ];

    public function package()
    {
        return $this->belongsTo(Package::class);
    }
}
