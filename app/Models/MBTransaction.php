<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MBTransaction extends Model
{
    use HasFactory;

    protected $table = 'mb_transactions';

    protected $fillable = [
        'transactionid',
        'transactiontime',
        'referencenumber',
        'amount',
        'content',
        'bankaccount',
        'transType',
        'valueDate',
        'va',
        'reciprocalAccount',
        'reciprocalBankCode',
    ];
}
