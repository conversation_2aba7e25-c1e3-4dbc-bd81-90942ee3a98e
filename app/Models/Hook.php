<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Hook extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bank_account_id',
        'channel',
        'hook_type',
        'endpoint',
        'token',
        'syntax',
        'event',
        'method',
        'status',
        'skip_if_no_code',
    ];

    protected $casts = [
        'skip_if_no_code' => 'boolean',
    ];

    public function bankAccount()
    {
        return $this->belongsTo(BankAccount::class, 'bank_account_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function logs()
    {
        return $this->hasMany(HookLog::class);
    }
}
