<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BIDVBusiness extends Model
{
    use HasFactory;
    protected $table = 'bidv_businesses';

    protected $fillable = [
        'first_va_number',
        'account_number',
        'account_name',
        'hint_name',
        'user_id',
    ];

    public function vaNumbers()
    {
        return $this->hasMany(BIDVVaNumber::class, 'bidv_business_id', 'id');
    }
}
