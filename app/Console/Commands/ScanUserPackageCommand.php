<?php

namespace App\Console\Commands;

use App\Models\Package;
use App\Models\UserPackage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ScanUserPackageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:scan-user-package-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userPackages = UserPackage::all();
        foreach ($userPackages as $userPackage) {
            if ($userPackage->package->price == 0) {
                if ($userPackage->expire_at <= now()->format('Y-m-d H:i:s')) {
                    Log::info('Customer: ' . $userPackage->user_id . ': Renew gói free');
                    $userPackage->start_at = $userPackage->end_at;
                    $userPackage->end_at = now()->parse($userPackage->start_at)->addMonth();
                    $userPackage->expire_at = now()->parse($userPackage->start_at)->addMonth();
                    $userPackage->save();
                }
            }
            if ($userPackage->package->price > 0) {
                if ($userPackage->expire_at <= now()->format('Y-m-d H:i:s')) {
                    Log::info('Customer: ' . $userPackage->user_id . ': Hết hạn gói pro => move về gói free');
                    $packageFree = Package::where('price', 0)->first();
                    $newUserPackage = [
                        'user_id' => $userPackage->user_id,
                        'package_id' => $packageFree->id,
                        'start_at' => $userPackage->expire_at,
                        'end_at' => now()->parse($userPackage->expire_at)->addMonth(),
                        'expire_at' => now()->parse($userPackage->expire_at)->addMonth(),
                    ];
                    $userPackage->delete();
                    UserPackage::create($newUserPackage);
                } else {
                    if ($userPackage->end_at <= now()->format('Y-m-d H:i:s')) {
                        Log::info('Customer: ' . $userPackage->user_id . ': next chu kì tiếp theo trong thời gian còn hạn');
                        $userPackage->start_at = $userPackage->end_at;
                        $userPackage->end_at = now()->parse($userPackage->start_at)->addMonth();
                        $userPackage->save();
                    }
                }
            }
        }
    }
}
