<?php

namespace App\Console\Commands;

use App\Jobs\ScanTransactionACB;
use App\Jobs\ScanTransactionBIDV;
use App\Jobs\ScanTransactionICB;
use App\Jobs\ScanTransactionMB;
use App\Jobs\ScanTransactionVCB;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use DateTime;
use GuzzleHttp\Client;
use Illuminate\Console\Command;

class TransactionScanCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:transaction-scan-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = new Client();
        $banks = BankAccount::with(['user', 'activeHooks'])->where('status', 1)->where('open_api', 0)->get();
        foreach ($banks as $bank) {
            $user = $bank->user;
            $currentPackage = $user->currentPackage;
            $currentPackageTransactions = BankTransaction::withTrashed()->where('user_id', $user->id)
                ->where('actual_at', '>=', $currentPackage->start_at)
                ->where('actual_at', '<=', $currentPackage->end_at)
                ->get();
            if ($currentPackageTransactions->count() >= $currentPackage->package->limit_transactions) {
                continue;
            }
            if ($bank->bank_code == 'vcb') {
                ScanTransactionVCB::dispatchSync($bank);
            }
            if ($bank->bank_code == 'acb') {
                ScanTransactionACB::dispatchSync($bank);
            }
            if ($bank->bank_code == 'mb') {
                ScanTransactionMB::dispatchSync($bank);
            }
            if ($bank->bank_code == 'icb') {
                ScanTransactionICB::dispatchSync($bank);
            }
            if ($bank->bank_code == 'bidv') {
                ScanTransactionBIDV::dispatchSync($bank);
            }
        }
    }
}
