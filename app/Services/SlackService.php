<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class SlackService
{
	public function sendHook($endpoint, $data)
	{
		$client = new Client();
		$textType = $data['type'] == '+' ? 'Đã nhận' : 'Đã bị trừ';

		$message = "*" . $data['bank_name'] . "*\nTài khoản: *" . $data['account_no'] . " - " . $data['account_holder_name'] . "*\n" . $textType . ": `" . $data['type'] . number_format($data['amount']) . "` vào lúc `" . $data['time'] . "`\nNội dung: *" . $data['description'] . "*\nSố tham chiếu: *" . $data['reference'] . "*";

		try {
			$result = $client->post($endpoint, [
				'json' =>  [
					// "text" => $message,
					"blocks" => [
						[
							"type" => "section",
							"text" => [
								"type" => "mrkdwn",
								"text" => ":wave: <PERSON><PERSON> giao dịch mới sếp ơi :money_with_wings:"
							]
						],
						[
							"type" => "section",
							"text" => [
								"type" => "mrkdwn",
								"text" => $message
							],
						]
					]
				]
			]);
			return $result->getBody()->getContents();
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			return $e->getMessage();
		}
	}
}
