<?php

namespace App\Services;

use App\Enums\BankNameEnum;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class TelegramService
{
	public function sendHook($hook, $data)
	{
		$client = new Client();

		$textType = $data['type'] == '+' ? 'Đã nhận' : 'Đã bị trừ';
		$message = "---------- 💰 Có giao dịch mới ----------\n";
		$message .= "<code>" . $data['bank_name'] . "</code>\n" . "Tài khoản: <code>" . $data['account_no'] . " - " . $data['account_holder_name'] . "</code>\n" . $textType . ": <code>" . number_format($data['amount']) . "</code> vào lúc <code>" . $data['time'] . "</code>\nNội dung: <code>" . $data['description'] . "</code>\nSố tham chiếu: <code>" . $data['reference'] . "</code>\n";
		$message .= "Xem chi tiết <a href='https://app.allpay.vn/reports'>tại đây</a>\n";
		$message .= "---------------------------------------------------\n";
		$bot_telegram_token = config('app.bot_telegram_token');
		try {
			$result = $client->get("https://api.telegram.org/bot" . $bot_telegram_token . "/sendMessage?chat_id=" . $hook->endpoint . "&parse_mode=html&text=" . $message);
			return $result->getBody()->getContents();
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			return $e->getMessage();
		}
	}
}
