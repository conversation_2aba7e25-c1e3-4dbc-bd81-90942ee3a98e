<?php

namespace App\Services;

use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Jose\Component\Core\AlgorithmManager;
use Jose\Component\Core\JWK;
use Jose\Component\Encryption\Algorithm\ContentEncryption\A256GCM;
use Jose\Component\Encryption\Algorithm\KeyEncryption\A256KW;
use Jose\Component\Encryption\Compression\CompressionMethodManager;
use Jose\Component\Encryption\Compression\Deflate;
use Jose\Component\Encryption\JWEBuilder;
use Jose\Component\Encryption\Serializer\CompactSerializer;
use Jose\Component\Encryption\Serializer\JSONGeneralSerializer;
use Jose\Component\KeyManagement\JWKFactory;
use Jose\Component\Signature\Algorithm\RS256;
use Jose\Component\Signature\JWSBuilder;
use Jose\Component\Signature\Serializer\CompactSerializer as JwsCompactSerializer;

class BIDVOpenAPIService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('tino_bidv.end_point.base_url')]);
    }

    public function genToken()
    {
        try {
            $res = $this->client->post(config('tino_bidv.end_point.gen_token'), [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => config('tino_bidv.app_api_key'),
                    'client_secret' => config('tino_bidv.app_api_secret'),
                    'scope' => 'ewallet'
                ]
            ]);

            return json_decode($res->getBody()->getContents(), true);
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                Log::error($e->getResponse()->getBody()->getContents());
            } else {
                Log::error($e->getMessage());
            }
            return false;
        }
    }

    public function genVietQR($data)
    {
        try {
            $responseAccessToken = $this->genToken();
            Log::debug($responseAccessToken);
            if (!$responseAccessToken) {
                Log::error('Cannot get access token');
                return false;
            }
            $accessToken = $responseAccessToken['access_token'];

            $encryptData = $this->encryptData($data);
            Log::debug($encryptData);
            if (!$encryptData) {
                Log::error('Cannot encrypt data');
                return false;
            }

            $res = $this->client->post(config('tino_bidv.end_point.gen_vietqr'), [
                'headers' => [
                    'Channel' => 'AllPay-VietQR',
                    'User-Agent' => 'AllPay-VietQR',
                    'X-Client-Certificate' => $this->getCertificate(), // Get file certificate.crt và Đảm bảo là chuỗi đã xóa “-----BEGIN CERTIFICATE-----”, “-----END CERTIFICATE-----” và khoảng trắng
                    'X-API-Interaction-ID' => mt_rand(100000000000, 999999999999), //random 12 kí tự số
                    'Timestamp' => Carbon::now('UTC')->format('Y-m-d\TH:i:s.v\Z'), // now time, format ISO 8061 
                    'Authorization' => "Bearer " . $accessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-JWS-Signature' => $encryptData['compact_jws']
                ],
                'json' => $encryptData['encrypted']
            ]);

            return json_decode($res->getBody()->getContents(), true);
        } catch (RequestException $e) {
            Log::error('Cannot gen VietQR');
            Log::error($e);
            if ($e->hasResponse()) {
                Log::error($e->getResponse()->getBody()->getContents());
            } else {
                Log::error($e->getMessage());
            }
            return false;
        }
    }

    private function getCertificate()
    {
        $rawContent = Storage::get('private/certificate.crt');
        $cleanContent = str_replace([
            '-----BEGIN CERTIFICATE-----',
            '-----END CERTIFICATE-----',
            "\n", "\r", " ", "\t"
        ], '', $rawContent);

        return $cleanContent;
    }

    private function encryptData($data)
    {
        $myKey = config('tino_bidv.app_symmetric_key');

        $keyBytes = hex2bin($myKey);

        $jwk = new JWK([
            'kty' => 'oct',
            'k' => rtrim(strtr(base64_encode($keyBytes), '+/', '-_'), '=')
        ]);

        $payload = json_encode($data);
        // dump($payload);
        // The key encryption algorithm manager with the A256KW algorithm.
        $keyEncryptionAlgorithmManager = new AlgorithmManager([
            new A256KW(),
        ]);

        // The content encryption algorithm manager with the A256CBC-HS256 algorithm.
        $contentEncryptionAlgorithmManager = new AlgorithmManager([
            new A256GCM(),
        ]);
        // The compression method manager with the DEF (Deflate) method.
        $compressionMethodManager = new CompressionMethodManager([
            new Deflate(),
        ]);

        $jweBuilder = new JWEBuilder(
            $keyEncryptionAlgorithmManager,
            $contentEncryptionAlgorithmManager,
            $compressionMethodManager
        );
        $jwe = $jweBuilder
            ->create()              // We want to create a new JWE
            ->withPayload($payload) // We set the payload
            ->withSharedProtectedHeader([
                'alg' => 'A256KW',        // Key Encryption Algorithm
                'enc' => 'A256GCM', // Content Encryption Algorithm
            ])
            ->addRecipient($jwk)    // We add a recipient (a shared key or public key).
            ->build();
        $serializer = new JSONGeneralSerializer();
        $json = $serializer->serialize($jwe, 0);
        // dump($json);

        $serializer = new CompactSerializer(); // The serializer

        $token = $serializer->serialize($jwe, 0);

        // Print the token
        // dd($token);
        /////

        // Split the serialized JWE to extract components
        $split = explode('.', $token);
        $protectedHeader = $split[0];
        $encryptedKey = $split[1];
        $iv = $split[2];
        $ciphertext = $split[3];
        $tag = $split[4];

        // Create recipients object with an empty header
        $recipient = [
            'header' => [],
            'encrypted_key' => $encryptedKey
        ];
        $recipients = [$recipient];

        // Create JweObj equivalent array
        $jweObj = [
            'recipients' => $recipients,
            'protected' => $protectedHeader,
            'ciphertext' => $ciphertext,
            'iv' => $iv,
            'tag' => $tag
        ];

        // Convert JweObj to JSON string
        $jweObjString = json_encode($jweObj, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        // Load the private key from PEM file
        $privateKey = JWKFactory::createFromKeyFile(Storage::path('private/privateKey.key'));

        // Create the JWS Builder
        $jwsBuilder = new JWSBuilder(new AlgorithmManager([new RS256()]));

        // Create the JWS
        $jws = $jwsBuilder
            ->create()
            ->withPayload($jweObjString)
            ->addSignature($privateKey, ['alg' => 'RS256'])
            ->build();

        $jwsSerializer = new JwsCompactSerializer();
        $jwsString = $jwsSerializer->serialize($jws, 0);

        // Split the JWS to create a compact version
        $parts = explode('.', $jwsString);
        if (count($parts) === 3) {
            $compactJws = $parts[0] . ".." . $parts[2];
            return [
                'encrypted' => json_decode($jweObjString, true),
                'compact_jws' => $compactJws
            ];
        } else {
            return false;
        }
    }
}
