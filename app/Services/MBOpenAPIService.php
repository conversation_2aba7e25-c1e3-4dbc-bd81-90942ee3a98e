<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;

class MBOpenAPIService
{
	protected $client;
	public function __construct()
	{
		$this->client = new Client(['base_uri' => 'https://api-sandbox.mbbank.com.vn']);
	}

	public function getToken()
	{
		try {
			$result = $this->client->post('/oauth2-private/v1/token', [
				'auth' => [config('tino_mb.client_id'), config('tino_mb.client_secret')],
				'form_params' => [
					'grant_type' => 'client_credentials'
				]
			]);
			if ($result) {
				return json_decode($result->getBody()->getContents(), true);
			}
		} catch (\Exception $e) {
			Log::info('-----MB getToken----');
			Log::debug($e);
			Log::info('-----MB getToken----');
			return false;
		}
	}

	public function getAccountName($accountNumber)
	{
		$token = $this->getToken();
		if ($token && isset($token['access_token'])) {
			try {
				$result = $this->client->get('/ms/bank-info/v1.0/account/info?accountType=ACCOUNT&transferType=INHOUSE&accountNumber=' . $accountNumber, [
					'headers' => [
						'Authorization' => "Bearer " . $token['access_token'],
						'clientMessageId' => (string) Uuid::uuid4(),
					],
				]);
				if ($result) {
					return json_decode($result->getBody()->getContents(), true);
				}
			} catch (\Exception $e) {
				Log::info('-----start getAccountName error----');
				Log::debug($e);
				Log::info('-----end getAccountName error----');
				return json_decode($e->getResponse()->getBody()->getContents(), true);
			}
		}
		return false;
	}

	public function bdsdSubscribeRequest($data)
	{
		$token = $this->getToken();
		if ($token && isset($token['access_token'])) {
			try {
				$dataRequest = [
					...$data,
					"applicationType" => "WEB_APP",
					"authenType" => "SMS",
				];
				Log::debug(json_encode($dataRequest));
				$result = $this->client->post('/private/ms/push-mesages-partner/v1.0/bdsd/subscribe/request', [
					'headers' => [
						'Authorization' => "Bearer " . $token['access_token'],
						'clientMessageId' => (string) Uuid::uuid4(),
						'transactionId' => Str::random(12),
					],
					'json' => $dataRequest
				]);
				if ($result) {
					$content = $result->getBody()->getContents();
					Log::info($content);
					return json_decode($content, true);
				}
			} catch (\Exception $e) {
				Log::info('-----start bdsdSubscribeRequest error----');
				Log::debug($e);
				$content = $e->getResponse()->getBody()->getContents();
				Log::debug($content);
				Log::info('-----end bdsdSubscribeRequest error----');
				return json_decode($content, true);
			}
		}
		return false;
	}

	public function bdsdSubscribeConfirm($otp, $requestId)
	{
		$token = $this->getToken();
		if ($token && isset($token['access_token'])) {
			try {
				$dataRequest = [
					"applicationType" => "WEB_APP",
					"authenType" => "SMS",
					"otpValue" => $otp,
					"requestId" => $requestId
				];
				Log::debug(json_encode($dataRequest));
				$result = $this->client->post('/private/ms/push-mesages-partner/v1.0/bdsd/subscribe/confirm', [
					'headers' => [
						'Authorization' => "Bearer " . $token['access_token'],
						'clientMessageId' => (string) Uuid::uuid4(),
						'transactionId' => Str::random(12),
					],
					'json' => $dataRequest
				]);
				if ($result) {
					$content = $result->getBody()->getContents();
					Log::info($content);
					return json_decode($content, true);
				}
			} catch (\Exception $e) {
				Log::info('-----start bdsdSubscribeConfirm error----');
				Log::debug($e);
				$content = $e->getResponse()->getBody()->getContents();
				Log::debug($content);
				Log::info('-----end bdsdSubscribeConfirm error----');
				return json_decode($content, true);
			}
		}
		return false;
	}

	public function bdsdUnSubscribeRequest($accountNumber)
	{
		$token = $this->getToken();
		if ($token && isset($token['access_token'])) {
			try {
				$dataRequest = [
					"authenType" => "SMS",
					"accountNumber" => $accountNumber,
				];
				Log::debug(json_encode($dataRequest));
				$result = $this->client->post('/private/ms/push-mesages-partner/v1.0/bdsd/unsubscribe/request', [
					'headers' => [
						'Authorization' => "Bearer " . $token['access_token'],
						'clientMessageId' => (string) Uuid::uuid4(),
						'transactionId' => Str::random(12),
					],
					'json' => $dataRequest
				]);
				if ($result) {
					$content = $result->getBody()->getContents();
					Log::info($content);
					return json_decode($content, true);
				}
			} catch (\Exception $e) {
				Log::info('-----start bdsdUnSubscribeRequest error----');
				Log::debug($e);
				$content = $e->getResponse()->getBody()->getContents();
				Log::debug($content);
				Log::info('-----end bdsdUnSubscribeRequest error----');
				return json_decode($content, true);
			}
		}
		return false;
	}

	public function bdsdUnSubscribeConfirm($otp, $requestId)
	{
		$token = $this->getToken();
		if ($token && isset($token['access_token'])) {
			try {
				$dataRequest = [
					"authenType" => "SMS",
					"otpValue" => $otp,
					"requestId" => $requestId,
				];
				Log::debug(json_encode($dataRequest));
				$result = $this->client->post('/private/ms/push-mesages-partner/v1.0/bdsd/unsubscribe/confirm', [
					'headers' => [
						'Authorization' => "Bearer " . $token['access_token'],
						'clientMessageId' => (string) Uuid::uuid4(),
						'transactionId' => Str::random(12),
					],
					'json' => $dataRequest
				]);
				if ($result) {
					$content = $result->getBody()->getContents();
					Log::info($content);
					return json_decode($content, true);
				}
			} catch (\Exception $e) {
				Log::info('-----start bdsdUnSubscribeConfirm error----');
				Log::debug($e);
				$content = $e->getResponse()->getBody()->getContents();
				Log::debug($content);
				Log::info('-----end bdsdUnSubscribeConfirm error----');
				return json_decode($content, true);
			}
		}
		return false;
	}
}
