<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class HookService
{
	public function sendHook($hook, $data)
	{
		$client = new Client();

		try {
			$result = $client->post($hook->endpoint, [
				'headers' => [
					'ALLPAY-TOKEN' => $hook->token,
					'Authorization' => 'Apikey ' . $hook->token
				],
				'json' =>  $data
			]);
			return $result->getBody()->getContents();
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			return $e->getMessage();
		}
	}
}
