<?php

namespace App\Services;

use App\Enums\BankNameEnum;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class LarkService
{
	public function sendHook($endpoint, $data)
	{
		$client = new Client();
		$color = $data['type'] == '+' ? 'green' : 'red';
		$textType = $data['type'] == '+' ? 'Đã nhận' : 'Đã bị trừ';
		$header = [
			"template" => 'green',
			"title" => [
				"content" => "💰 Có giao dịch mới!",
				"tag" => "plain_text"
			]
		];

		$elements =  [
			[
				"tag" => "div",
				"text" => [
					"content" => "**" . $data['bank_name'] . "**\nTài khoản: **" . $data['account_no'] . " - " . $data['account_holder_name'] . "**\n" . $textType . ": <font color='" . $color . "'>**" . $data['type'] . number_format($data['amount']) . "</font>** vào lúc **" . $data['time'] . "**\nNội dung: **" . $data['description'] . "**\nSố tham chiếu: **" . $data['reference'] . "**",
					"tag" => "lark_md"
				]
			],
		];

		try {
			$result = $client->post($endpoint, [
				'json' =>  [
					"msg_type" => "interactive",
					"card" => [
						"elements" => $elements,
						"header" => $header
					]
				]
			]);
			return $result->getBody()->getContents();
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			return $e->getMessage();
		}
	}
}
