<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class HaravanService
{
	public function sendTransaction($hook, $data)
	{
		$client = new Client();

		try {

			if (empty($data['type']) || $data['type'] == '-') {
				return json_encode([
					'success' => false,
					'message' => 'No transaction in!'
				]);
			}

			$orderId = $this->parse_order_id($data['description'], $hook->syntax);
			if (empty($orderId)) {
				return json_encode([
					'success' => false,
					'message' => 'No order data detected !'
				]);
			}

			$result = $client->post("https://apis.haravan.com/com/orders/$orderId/transactions.json", [
				'headers' => [
					'Authorization' => "Bearer $hook->token"
				],
				'json' =>  [
					"transaction" => [
						'amount' => $data['amount'],
						'kind' => "Capture",
					]
				]
			]);

			return $result->getBody()->getContents();
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			return $e->getMessage();
		}
	}

	public function checkStatus($hook, $orderId)
	{
		$client = new Client();

		try {

			$result = $client->get("https://apis.haravan.com/com/orders/$orderId.json", [
				'headers' => [
					'Authorization' => "Bearer $hook->token"
				]
			]);

			return json_decode($result->getBody()->getContents(), true);
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			return [
				'status' => false,
				'msg' => 'Error',
			];
		}
	}

	function parse_order_id($des, $MEMO_PREFIX)
	{
		$re = '/' . $MEMO_PREFIX . '\d+/im';
		preg_match_all($re, $des, $matches, PREG_SET_ORDER, 0);
		if (count($matches) == 0)
			return null;
		// Print the entire match result
		$orderCode = $matches[0][0];
		$prefixLength = strlen($MEMO_PREFIX);
		$orderId = intval(substr($orderCode, $prefixLength));
		return $orderId;
	}
}
