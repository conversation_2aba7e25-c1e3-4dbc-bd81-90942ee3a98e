<?php

namespace App\Bank;

use App\Models\BankAccount;
use App\Models\Proxy;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Crypt_RSA;

class Bidv
{
	protected $captchaKey = "duy72c70ebc61c8c9a1426afae9cc378";

	protected $defaultPublicKey =   "-----BEGIN PUBLIC KEY-----\r\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAy6bC9ub46VDwZL5rwtbW\r\n2vBlHsqGzn6kr8OX7dKn+jHZxJxHSOGwTlqi+/QsSZ8wbUDkyK66atYB4Y06j1HS\r\nRimLG2zKK6BwqtMwM1VBwepy6nB+JsbobmvDInU/8cArdQRVNwWMHWwV0ZB0a3wp\r\nFCvVSwF61zFh5aG1Gbfvkbwdh4bpRa860MTyK19+rRXboROQmQYXfLWbrsI7vc3Q\r\nFRfgHIdh3baVd0mjmgMhE9yXwzroOxd418aWUQ9eSY1xmEmX9QynG9dYBMl/zzuS\r\nmM6CfJwKdsswKF0vmhRSLOBv+j/jABADcnrcIhcBS3EnTtSXDQPn/O/osqvRu5q\r\nxvQIDAQAB\r\n-----END PUBLIC KEY-----";

	protected $clientPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLDAD6Wr+W7SXLJMECvt3/W9zMmVcbzwUniO7vYLBJDOEcWJoci5TrfAXlA+z3vxLmEKif41f6wlDBiY+Njj0fNkVH9w+dBbIz2CBaB8RsoDSFYA5zzUbdXfVMV+fs3o3nK/dDAZNX1MU96cISsgQTe+dIIkpMs3jSFvrxjtGg+wIDAQAB";

	protected $clientPrivateKey = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
	protected $url = [
		"getCaptcha" => "https://smartbanking.bidv.com.vn/w2/captcha/",
		"auth" => "https://smartbanking.bidv.com.vn/w2/auth",
		"process" => "https://smartbanking.bidv.com.vn/w2/process"
	];

	protected $anycaptcha = 'cd444e0bd6454a6198c99751bb5da481';
	protected $lang = 'vi';
	protected $_timeout = 60;
	protected $DT = "MAC OS";
	protected $E = "";
	protected $OV = "*********";
	protected $PM = "Chrome";
	protected $appVersion = "********";
	protected $captchaToken = "";
	protected $captchaValue = "";
	#account
	protected $username;

	protected $password;
	protected $account_number;
	#store account
	protected $sessionId = "";
	protected $mobileId = "";
	protected $clientId = "";
	protected $cif = "";
	protected $token = "";
	protected $accessToken = "";
	protected $authToken = "";

	private $file = '';
	protected $account;

	public function __construct($username, $password, $account_number)
	{
		$this->username = $username;
		$this->password = $password;
		$this->account_number = $account_number;
		// $this->loadData();
	}

	public function loadData()
	{
		$this->account = BankAccount::where('username', $this->username)
			->where(
				'bank_code',
				'bidv'
			)
			->where('account_no', $this->account_number)->first();
		if ($this->account) {
			$this->account->username = $this->username;
			$this->account->password = $this->password;
			$this->account->account_no = $this->account_number;
		} else {
			$this->account = new BankAccount();
			$this->account->user_id  = Auth::user()->id;
			$this->account->username = $this->username;
			$this->account->password = $this->password;
			$this->account->account_no = $this->account_number;
			$this->account->bank_code = 'bidv';
			$this->account->user_id = Auth::user()->id;
		}

		return $this;
	}

	public function loadByAccount($exitAccount, $username, $password, $accountNo, $ipv4 = null)
	{
		$this->account = $exitAccount;
		$this->account->username = $username;
		$this->account->password = $password;
		$this->account->account_no = $accountNo;

		return $this;
	}

	// public function parseData()
	// {
	// 	$this->username = $this->account->username;
	// 	$this->password = $this->account->password;
	// 	$this->account_number = $this->account->account_no;
	// 	$this->sessionId = $this->account->sessionId;
	// 	$this->clientId = $this->account->clientId;
	// 	$this->token = isset($this->account->token) ? $this->account->token : '';
	// 	$this->accessToken = isset($this->account->accessToken) ? $this->account->accessToken : '';
	// 	$this->authToken = isset($this->account->authToken) ? $this->account->authToken : '';
	// 	$this->cif = $this->account->cif;
	// 	$this->E = $this->account->E;
	// }


	public function saveData()
	{
		$data = [
			'username' 				=> $this->username,
			'password' 				=> $this->password,
			'account_number' 		=> $this->account_number,
			'sessionId' 			=> $this->sessionId,
			'mobileId' 				=> $this->mobileId,
			'clientId' 				=> $this->clientId,
			'cif' 					=> $this->cif,
			'token' 				=> $this->token,
			'accessToken' 			=> $this->accessToken,
			'E' 					=> $this->E,
			'authToken' 			=> $this->authToken,
		];
		file_put_contents($this->file, json_encode($data));
	}

	public function doLogin()
	{
		$solveCaptcha = $this->solveCaptcha();
		if ($solveCaptcha['status'] == false) {
			return $solveCaptcha;
		}
		$param = array(
			"DT" => $this->DT,
			"E" => $this->account->E ?? '',
			"OV" => $this->OV,
			"PM" => $this->PM,
			"appVersion" => $this->appVersion,
			"captchaToken" => $this->captchaToken,
			"captchaValue" => $this->captchaValue,
			"clientId" => $this->account->browserToken ?? '',
			"mid" => 1,
			"pin" => $this->password,
			"user" => $this->username
		);
		$result = $this->curlPost($this->url['auth'], $param);
		$result['authToken'] = $this->authToken;

		if ($result['code'] == 00) {
			if (isset($result['loginType']) && $result['loginType'] == 1) {
				$this->account->access_key    = $result['accessKey'];
				$this->account->authorization    = $result['accessToken'];
				$this->account->sessionId    = $result['sessionId'];
				$this->account->accounts      = $result;
				$this->account->account_holder_name      = $result['name'] ?? '';
				$this->account->last_status      = 'Đăng nhập thành công';
				$this->account->time_login    = time();
				$this->account->status        = 1;
				$this->account->save();
				return (object) [
					'success' => true,
					'request_otp' => false,
					'message' => 'Đăng nhập thành công'
				];
			}
			if (isset($result['loginType']) && $result['loginType'] == 3) {
				$this->account->accounts = $result;
				$this->account->last_status = 'Đợi xác nhận OTP lưu trình duyệt';
				$this->account->save();
				return (object) [
					'success' => true,
					'request_otp' => true,
					'message' => 'Mã OTP đã được gửi đến số điện thoại của bạn'
				];
			}
		} else {
			return (object) [
				'success' => false,
				'request_otp' => false,
				'message' => $result['des'] ?? ''
			];
		}

		return $result;
	}

	public function verifyOTP($otp)
	{
		$this->E = Str::random(10) .  $this->account->username;

		$data = [
			'user' =>   $this->account->username,
			'clientId' =>  $this->clientId,
			'location' =>  '',
			'otp' =>  $otp,
			'mid' =>  56,
			"DT" => $this->DT,
			"E" => $this->E,
			"OV" => $this->OV,
			"PM" => $this->PM,
			"appVersion" => $this->appVersion,
			'token' =>  $this->account->accounts->token
		];
		$res = $this->curlPost($this->url['auth'], $data);
		$res['E'] = $this->E;
		$res['authToken'] = $this->authToken;
		if ($res['code'] == '00') {
			$this->account->browserToken = $res['clientId'];
			$this->account->sessionId  = $res['sessionId'];
			$this->account->access_key  = $res['accessKey'];
			$this->account->authorization = $res['accessToken'];
			$this->account->accounts = $res;
			$this->account->account_holder_name      = $res['name'] ?? '';
			$this->account->last_status      = 'Đăng nhập thành công';
			$this->account->status        = 1;
			$this->account->E        = $res['E'];
			$this->account->save();

			return ['status' => true, 'message' => 'Đăng nhập thành công', 'data' => $res];
		} else {
			return ['status' => false, 'message' => $res['des'], 'data' => $res];
		}
	}

	public function getTransactions()
	{
		$param = array(
			"DT" => $this->DT,
			"E" => $this->account->E,
			"OV" => $this->OV,
			"PM" => $this->PM,
			"appVersion" => $this->appVersion,
			"clientId" => $this->account->browserToken,
			"accType" => "D",
			"accNo" => $this->account->account_no,
			"mid" => 12,
			"moreRecord" => "N",
			"nextRunbal" => "",
			"postingDate" => "",
			"postingOrder" => ""
		);
		$result = $this->curlPost($this->url['process'], $param, ['Authorization' => $this->account->accounts->authToken ?? '']);
		return $result;
	}

	public function getBalance($E, $clientId, $access, $accNo)
	{
		$param = array(
			"DT" => $this->DT,
			"E" => $E,
			"OV" => $this->OV,
			"PM" => $this->PM,
			"appVersion" => $this->appVersion,
			"clientId" => $clientId,
			"accType" => "D",
			"mid" => 10,
			"isCache" => false,
			"maxRequestInCache" => false
		);
		$result = $this->curlPost($this->url['process'], $param, ['Authorization' => $access]);
		return $result;
	}

	public function getCaptcha()
	{
		$this->captchaToken = Str::random(30);
		$client = new Client(['http_errors' => false]);
		$res = $client->request('GET', $this->url['getCaptcha'] . $this->captchaToken, [
			'proxy' => $this->getProxy(),
			'timeout' => $this->_timeout,
			'headers' => array(
				'user-agent' => $this->getUserAgent()
			),
		]);
		$result = $res->getBody()->getContents();
		return base64_encode($result);
	}

	public function solveCaptcha()
	{
		$getCaptcha = $this->getCaptcha();


		$curl = curl_init();
		$dataPost = array(
			"api_key" => "413145b2f6d981e32d0ee69a56b0e839",
			"img_base64" => $getCaptcha,
		);
		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://ecaptcha.sieuthicode.net/api/captcha/bidv',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $dataPost,
		));
		$response = curl_exec($curl);

		curl_close($curl);
		// 		$result = json_decode($res->getBody()->getContents());
		$result = json_decode($response);
		$this->captchaValue = $result->data->captcha;
		return ["status" => true, "key" => $this->captchaToken, "captcha" => $this->captchaValue];
		// 		if ($result->status !== true) {
		// 			return ["status" => false,"msg" =>"Solve Captcha failed: "];
		// 		} else {
		// 			$this->captchaValue = $result->data->captcha;
		// 			return ["status" => true,"key" => $this->captchaToken,"captcha" => $this->captchaValue];
		// 		}
	}


	public function encryptData($str)
	{
		$str["clientPubKey"] = $this->clientPublicKey;
		$key = Str::random(32);
		$iv = Str::random(16);
		$rsa = new Crypt_RSA();
		$rsa->loadKey($this->defaultPublicKey);
		$rsa->setEncryptionMode(2);
		$body = base64_encode($iv . openssl_encrypt(json_encode($str), 'AES-256-CTR', $key, OPENSSL_RAW_DATA, $iv));
		$header = base64_encode($rsa->encrypt(base64_encode($key)));
		return [
			'd' => $body,
			'k' => $header,
		];
	}
	public function decryptData($cipher)
	{
		$header = $cipher->k;
		$body = base64_decode($cipher->d);
		$rsa = new Crypt_RSA();
		$rsa->loadKey($this->clientPrivateKey);
		$rsa->setEncryptionMode(2);
		$key = $rsa->decrypt(base64_decode($header));
		$iv = substr($body, 0, 16);
		$cipherText = substr($body, 16);
		$text = openssl_decrypt($cipherText, 'AES-256-CTR', base64_decode($key), OPENSSL_RAW_DATA, $iv);
		return (array) json_decode($text, true);
	}


	private function curlPost($url = "", $data = array(), $header = [])
	{
		try {
			$client = new Client();
			$res = $client->request('POST', $url, [
				'proxy' => $this->getProxy(),
				'timeout' => $this->_timeout,
				'headers' => $this->headerNull($header),
				'body' => json_encode($this->encryptData($data)),
			]);
			$result = json_decode($res->getBody()->getContents());
			$this->authToken = $res->getHeaderLine("Authorization");
			return $this->decryptData($result);
		} catch (\Exception $e) {
			if ($e->getResponse()->getStatusCode() == '403')
				return ["status" => false, "msg" => "Token hết hạn vui lòng đăng nhập lại"];
			$response = $e->getResponse()->getBody()->getContents();
			return $this->decryptData(json_decode($response));

			return false;
		}
	}


	public function headerNull($header = [])
	{
		$headers = [
			'Accept-Language' =>  'vi',
			'Accept'        =>  'application/json',
			'Content-Type'  =>  'application/json',
			'User-Agent'    =>  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36',
			'Host'          =>  'smartbanking.bidv.com.vn',
			'Origin'        =>  'https://smartbanking.bidv.com.vn',
			'Referer'       =>  'https://smartbanking.bidv.com.vn/',
		];
		if ($headers) {
			$headers  = array_merge($headers, $header);
		}
		return $headers;
	}

	public function getUserAgent()
	{
		$userAgentArray[] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36";
		$getArrayKey = array_rand($userAgentArray);
		return $userAgentArray[$getArrayKey];
	}

	public function isJson($string)
	{
		json_decode($string);
		return json_last_error() === JSON_ERROR_NONE;
	}

	public function getProxy()
	{
		$proxy = Proxy::where('status', 1)->first();
		if ($proxy) {

			return $proxy->proxy;
		}

		return "";
	}
}
