<?php

namespace App\Services;

use App\Models\BankAccount;
use Guz<PERSON>Http\Client;

class SeaBank
{
    public $username;
    public $password;
    public $accessToken;
    public $account_no;

    public $account;

    public function loadByUsername($username, $password, $account_no)
    {
        $this->account = BankAccount::where(
            'username',
            $username
        )
            ->first();
        if (!$this->account) {
            $this->account = new BankAccount();
            $this->account->username = $username;
            $this->account->password = $password;
            $this->account->account_no = $account_no;
            $this->account->save();
        } else {
            $this->account->username = $username;
            $this->account->password = $password;
            $this->account->account_no = $account_no;
            $this->account->save();
        }
        return $this;
    }

    public function login()
    {
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        );
        $data = array(
            'username' => $this->account->username,
            'password' => $this->account->password,
            'channel' => 'SEAMOBILE3.0',
            'context' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
            'rememberMe' => false,
            'subChannel' => "SEANET",
            'captcha' => "",
            'uuid' => "",

        );
        $login = $this->curl('POST', $data, 'https://ebankbackend.seanet.vn/canhan/api/authenticate', $headers);
        if ($login != false) {
            if (isset($login['message']) && $login['message'] == "Success") {
                $this->accessToken = $login['data']['id_token'];
                $this->account->access_token = $this->accessToken;
                $this->account->save();
                return array(
                    'status' => true,
                    'message' => 'Import SeaBank successfully.',
                    'access_token' => $this->accessToken,
                );
            } else {
                return array(
                    'status' => false,
                    'message' => 'Cannot login this account!'
                );
            }
        }
        return array(
            'status' => false,
            'message' => 'Connect false!'
        );
    }

    public function history($fromDate, $toDate)
    {
        $status = false;
        $message = 'Error exception';
        $data = [
            "accountID" => $this->account->account_no,
            "fromDate" => $fromDate,
            "toDate" => $toDate
        ];
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . trim($this->account->access_token)
        );
        $history = $this->curl('POST', $data, 'https://ebankms1.seanet.vn/p03/api/p03-statement/get-trans-list-new', $headers);
        if ($history != false) {
            $data = $history;
            $status = true;
            $message = 'Successfully';
        } else {
            $login = $this->login();
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function curl($method, $data, $url, $headers)
    {
        try {
            $client = new Client();
            $res = $client->request($method, $url, [
                'verify' => true,
                'timeout' => 30,
                'headers' => $headers,
                'body' => json_encode($data),
            ]);
            $body = json_decode($res->getBody(), true);
            return $body;
        } catch (\Exception $e) {
            return false;
        }
    }
}
