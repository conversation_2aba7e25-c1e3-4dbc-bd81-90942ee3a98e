<?php

namespace App\Bank;

use App\Models\BankAccount;
use App\Models\Proxy;
use GuzzleHttp\Client;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Crypt_RSA;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Vietinbank
{
    # URL;
    public $url = array(
        'captcha' => 'https://api-ipay.vietinbank.vn/api/get-captcha/',
        'login' => 'https://api-ipay.vietinbank.vn/ipay/wa/signIn',
        'getCustomerDetails' => 'https://api-ipay.vietinbank.vn/ipay/wa/getCustomerDetails',
        'getEntitiesAndAccounts' => 'https://api-ipay.vietinbank.vn/ipay/wa/getEntitiesAndAccounts',
        'getCmsData' => 'https://api-ipay.vietinbank.vn/ipay/wa/getCmsData',
        'getBillPayees' => 'https://api-ipay.vietinbank.vn/ipay/wa/getBillPayees',
        'creditAccountList' => 'https://api-ipay.vietinbank.vn/ipay/wa/creditAccountList',
        'getAvgAccountBal' => 'https://api-ipay.vietinbank.vn/ipay/wa/getAvgAccountBal',
        'getHistTransactions' => 'https://api-ipay.vietinbank.vn/ipay/wa/getHistTransactions',
        'getAccountDetails' => 'https://api-ipay.vietinbank.vn/ipay/wa/getAccountDetails',
        'getCodeMapping' => 'https://api-ipay.vietinbank.vn/ipay/wa/getCodeMapping',
        'napasTransfer' => 'https://api-ipay.vietinbank.vn/ipay/wa/napasTransfer',
        'makeInternalTransfer' => 'https://api-ipay.vietinbank.vn/ipay/wa/makeInternalTransfer',
        'getPayees' => 'https://api-ipay.vietinbank.vn/ipay/wa/getPayees',
        'authenSoftOtp' => "https://api-ipay.vietinbank.vn/ipay/wa/authenSoftOtp"
    );

    # Declare value;
    public $browserInfo = "Chrome-98.********";
    public $lang = 'vi';
    public $clientInfo = '127.0.0.1;MacOSProMax';
    public $_timeout = 60;
    public $_publicKey = '-----BEGIN PUBLIC KEY-----
    MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLenQHmHpaqYX4IrRVM8H1uB21
    xWuY+clsvn79pMUYR2KwIEfeHcnZFFshjDs3D2ae4KprjkOFZPYzEWzakg2nOIUV
    WO+Q6RlAU1+1fxgTvEXi4z7yi+n0Zs0puOycrm8i67jsQfHi+HgdMxCaKzHvbECr
    +JWnLxnEl6615hEeMQIDAQAB
    -----END PUBLIC KEY-----';

    # Storage value;
    public $sessionId;
    public $accountNumber;
    public $ipayId;
    public $tokenId;
    public $username;
    public $accessCode; // Mật khẩu
    public $captchaCode; // Mã captcha
    public $captchaId; // Id captcha
    public $requestId;
    public $sign;
    public $customerNumber;
    public $bsb;
    public $accountType;
    public $currencyCode;
    public $account;
    public function __construct($username, $accessCode, $accountNumber)
    {
        $this->username = $username;
        $this->accessCode = $accessCode;
        $this->accountNumber = $accountNumber;
        $this->loadData();
    }

    public function loadData()
    {
        $this->account = BankAccount::where('username', $this->username)
            ->where(
                'bank_code',
                'icb'
            )
            ->where('account_no', $this->accountNumber)->first();
        if ($this->account) {
            $this->sessionId = $this->account->imei;
            $this->ipayId = $this->account->ipayId;
            $this->tokenId = $this->account->access_key;
            $this->currencyCode = $this->account->currencyCode;
        } else {
            $this->account = new BankAccount();
            $this->account->user_id  = Auth::user()->id;
        }

        return $this;
    }

    public function loadByAccount($exitAccount, $username, $accountNo, $ipv4 = null)
    {
        $this->account = $exitAccount;
        $this->account->username = $username;
        $this->account->account_no = $accountNo;

        $this->sessionId = $this->account->imei;
        $this->ipayId = $this->account->ipayId;
        $this->tokenId = $this->account->access_key;
        $this->currencyCode = $this->account->currencyCode;

        return $this;
    }

    public function setValueTranfer($bsb, $accountType, $currencyCode)
    {
        $this->bsb = $bsb;
        $this->accountType = $accountType;
        $this->currencyCode = $currencyCode;
        return true;
    }


    public function getCaptcha()
    {
        $this->captchaId = Str::random(9);
        $client = new Client(['http_errors' => false]);
        $res = $client->request('GET', $this->url['captcha'] . $this->captchaId, [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => null
        ]);
        $svg = $res->getBody()->getContents();
        $this->captchaCode = $this->bypassCaptcha($svg);
        return true;
    }

    public function doLogin()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $svgCaptcha = $this->getCaptcha();
        $params = array(
            'accessCode' => $this->accessCode,
            'browserInfo' => $this->browserInfo,
            'captchaCode' => $this->captchaCode,
            'captchaId' => $this->captchaId,
            'clientInfo' => $this->clientInfo,
            'lang' => $this->lang,
            'requestId' => $this->requestId,
            'userName' => $this->username
        );

        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['login'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        $login = json_decode($res->getBody());
        Log::info(json_encode($login));
        if ($login->error == 0) {
            $this->sessionId = $login->sessionId;
            $this->ipayId = $login->ipayId;
            $this->tokenId = $login->tokenId;
            $info = $this->getEntitiesAndAccounts();
            Log::info(json_encode($info));
            $this->bsb = $info->accounts[0]->bsb;
            $this->accountType = $info->accounts[0]->type;
            $this->currencyCode = $info->accounts[0]->currencyCode;

            $this->account->username = $this->username;
            $this->account->password = $this->accessCode;
            $this->account->account_no = $this->accountNumber;
            $this->account->imei = $login->sessionId;
            $this->account->ipayId = $login->ipayId;
            $this->account->access_key = $login->tokenId;
            $this->account->currencyCode = $info->accounts[0]->currencyCode;
            $this->account->account_holder_name = $info->accounts[0]->title;
            $this->account->bank_code = 'icb';
            $this->account->status = 1;
            $this->account->last_status      = 'Đăng nhập thành công';
            $this->account->save();
            return array(
                'success' => true,
                'message' => "success",
                'data' => $login ?: ""
            );
        } else {
            return array(
                'success' => false,
                'message' => $login->errorMessage,
                'data' => $login ?: ""
            );
        }
    }

    // lấy thông tin người dùng
    public function getCustomerDetails()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getCustomerDetails'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    // lấy thông tin tài khoản
    public function getEntitiesAndAccounts()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getEntitiesAndAccounts'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    public function getBillPayees()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getBillPayees'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    public function creditAccountList()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['creditAccountList'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    public function getAvgAccountBal()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'accountNumber' => $this->accountNumber,
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getAvgAccountBal'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    // lấy lsgd
    public function getTransaction($startDate = null, $endDate = null)
    {
        $startDate = $startDate ?? Carbon::now()->format('Y-m-d');
        $endDate = $endDate ?? Carbon::now()->format('Y-m-d');
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'accountNumber' => $this->accountNumber,
            'endDate' => $endDate,
            'lang' => 'vi',
            'maxResult' => '*********',
            'pageNumber' => 0,
            'requestId' => $this->requestId,
            'searchFromAmt' => '',
            'searchKey' => '',
            'searchToAmt' => '',
            'startDate' => $startDate,
            'tranType' => ''
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getHistTransactions'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    // lấy danh sách ngân hàng để ck
    public function getCodeMapping()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'context' => "BENEFICIAL_BANK",
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getCodeMapping'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }

    //lấy tên tài khoản cùng bank
    public function nameInternalTransfer($beneficiaryAccount)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'accountNumber' => $beneficiaryAccount,
            'formAction' => "inquiryToAcctDetail",
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['makeInternalTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }

    public function createInternalTransfer($toAccountNumber, $amount, $message)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'accountNumber' => $this->accountNumber,
            "accountType" => $this->accountType,
            'formAction' => "validateTransaction",
            'lang' => $this->lang,
            "amount" => $amount,
            "reference" => $message,
            "fromBranchId" => $this->bsb,
            "currency" => $this->currencyCode,
            "toAccountNumber" =>  $toAccountNumber,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['makeInternalTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    public function sendSmsOtpInternalTransfer()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'formAction' => "sendSmsOtp",
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['makeInternalTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    public function confirmSmsInternalTransfer($authenticationActionCode, $otpValue)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'formAction' => "confirmTransaction",
            'otpType' => "SMS",
            'authenticationActionCode' => $authenticationActionCode,
            'enteredAuthCode' => $otpValue,
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['makeInternalTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }



    //lấy tên tài khoản khác bank
    public function nameNapasTransfer($beneficiaryAccount, $beneficiaryBin)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'beneficiaryAccount' => $beneficiaryAccount,
            'beneficiaryBin' => $beneficiaryBin,
            "beneficiaryType" => "account",
            'fromAccount' => $this->accountNumber,
            'formAction' => "validateToAccount",
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['napasTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }

    // tạo yêu cầu chuyển khoản ngoài
    public function createNapasTransfer($amount, $message)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            "amount" => $amount,
            'fromAccount' => $this->accountNumber,
            'formAction' => "validateTransaction",
            'lang' => $this->lang,
            'reference' => $message,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['napasTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    #sms
    // tạo yêu cầu gửi otp chuyển khoản
    public function sendSmsOtpNapasTransfer()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'fromAccount' => $this->accountNumber,
            'formAction' => "sendSmsOtp",
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['napasTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }

    // confirm otp chuyển khoản ngoài
    public function confirmSmsTransfer($authenticationActionCode, $otpValue)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'fromAccount' => $this->accountNumber,
            'formAction' => "confirmTransaction",
            'otpType' => "SMS",
            'authenticationActionCode' => $authenticationActionCode,
            'otpValue' => $otpValue,
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['napasTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }
    // lấy kq chuyển khoản ngoài
    public function getDetailSmsNapasTransfer()
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['getPayees'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }



    #softOTP dùng chung cho cả trong và ngoài
    public function confirmSoftTransfer($authenticationActionCode)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'fromAccount' => $this->accountNumber,
            'formAction' => "confirmTransaction",
            'otpType' => "SOFT_OTP",
            'authenticationActionCode' => $authenticationActionCode,
            'otpValue' => "",
            'lang' => $this->lang,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['napasTransfer'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }

    // lấy kq chuyển khoản ngoài
    public function getDetailSoftTransfer($paymentId)
    {
        $this->requestId = strtoupper(Str::random(12)) . '|' . Carbon::now()->valueOf();
        $params = array(
            'username' => $this->username,
            'formAction' => "inqTran",
            'lang' => $this->lang,
            'paymentId' => $paymentId,
            'requestId' => $this->requestId
        );
        $client = new Client(['http_errors' => false]);
        $res = $client->request('POST', $this->url['authenSoftOtp'], [
            'proxy' => $this->getProxy(),
            'timeout' => $this->_timeout,
            'headers' => $this->headerNull(),
            'body' => $this->makeBodyRequestJson($params)
        ]);
        return json_decode($res->getBody());
    }


    public function makeBodyRequestJson($params)
    {
        if ($this->sessionId) {
            $params['sessionId'] = $this->sessionId;
        }
        ksort($params);
        $this->sign = md5(http_build_query($params));
        $params['signature'] = $this->sign;
        ksort($params);
        $rsa = new Crypt_RSA();
        $rsa->loadKey($this->_publicKey);
        $data = base64_encode($rsa->encrypt(json_encode($params)));
        return json_encode(array(
            'encrypted' => $data
        ));
    }
    private function bypassCaptcha($svg)
    {
        $model = [
            "MCLCLCLCLCLCCLCLCLCLCLCLCCLCLCLCLCCLCLCLCLCCZMCCLCLCCLCLCCLCLCCLCZ" => 0,
            "MLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLZ" => 1,
            "MLLLLLLLLLLLLCLCLCCLCLCLLLLLLLLLLLLLLLLLLLLLLLLLLLLLZ" => 2,
            "MCLCCLCLCCLCLCLLLLLLLLLLLCCCCCLLLLLLLLLLLLCCCCCCCCLLLLLCCCLCCLCCLCLCCZ" => 3,
            "MLLLLLLLLLLLLLLLLLCLCLCCLCLCLLLLLLLLLLLLLLLLLLLLLLLLLZMLLLLLLLLLLLLLLLZMLLLZ" => 4,
            "MCLCLCLCLCLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLCCLLLLLLLCLCLCZ" => 5,
            "MCLCLCLCLCCCLCLCLCLCLCLCLCLCLCLCLCLCLCLCLCLCCLCLCZMLCCCCLCLCCLCZ" => 6,
            "MLCLCCLCLCLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLCZ" => 7,
            "MCLCLCLCCLCCLCLCLCLCCLCLCLCLCCLCCLCLCLCCLCLCCLCLCZMLCLCCCCLCZMLCCLCLCCCCLCZ" => 8,
            "MLCLCLCLCLCLCCLCLCLCLCCLCLCLCLCCCCCLCLCLCLCLCLCLCLCZMLCCCCCLCLCCCCLCZ" => 9
        ];
        $chars = array();
        preg_match_all('#<path fill="(.*?)" d="(.*?)"/>#', $svg, $matches);
        if (sizeof($matches) != 3) return;

        $paths = $matches[2];
        foreach ($paths as $path) {
            if (preg_match("#M([0-9]+)#", $path, $p)) {
                $pattern = preg_replace("#[0-9 \.]#", "", $path);
                $chars[$p[1]] = $model[$pattern];
            }
        }
        ksort($chars);
        return implode("", $chars);
    }

    private function headerNull()
    {
        return array(
            'origin' => 'https://ipay.vietinbank.vn',
            'content-type' => 'application/json;charset=UTF-8',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8',
            'accept' => 'application/json, text/plain, */*',
            'lang' => 'vi',
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.95 Safari/537.36'
        );
    }

    public function getProxy()
    {
        $proxy = Proxy::where('status', 1)->first();
        if ($proxy) {

            return $proxy->proxy;
        }

        return "";
    }
}
