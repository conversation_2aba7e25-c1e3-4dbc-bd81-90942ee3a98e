<?php

namespace App\Bank;

use GuzzleHttp\Client;
use App\Models\Acb as AcbModel;
use App\Models\BankAccount;
use App\Models\Proxy;
use Illuminate\Support\Facades\Auth;

class Acb
{
    public $username;
    public $password;
    public $clientId = 'iuSuHYVufIUuNIREV0FB9EoLn9kHsDbm';
    public $accessToken;
    public $stk;

    public $account;

    public function loadByUsername($username, $password, $account_no)
    {
        $this->account = BankAccount::where(
            'username',
            $username
        )->where(
            'bank_code',
            'acb'
        )
            ->first();
        if (!$this->account) {
            $this->account = new BankAccount();
            $this->account->bank_code = 'acb';
            $this->account->username = $username;
            $this->account->password = $password;
            $this->account->account_no = $account_no;
            $this->account->user_id  = Auth::user()->id;
            // $this->account->save();
        } else {
            $this->account->password = $password;
            $this->account->account_no = $account_no;
            // $this->account->save();
        }
        return $this;
    }

    public function loadByAccount($exitAccount, $username, $password, $accountNo, $ipv4 = null)
    {
        $this->account = $exitAccount;
        $this->account->username = $username;
        $this->account->password = $password;
        $this->account->account_no = $accountNo;

        return $this;
    }

    public function login()
    {
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
        );
        $data = array(
            'username' => $this->account->username,
            'password' => $this->account->password,
            'clientId' => $this->clientId
        );
        // $login = $this->curl('POST', $data, 'https://apiapp.acb.com.vn/mb/auth/tokens', $headers);
        $login = $this->curl('POST', $data, 'https://apiapp.acb.com.vn/mb/v2/auth/tokens', $headers);
        if ($login != false) {
            if (array_key_exists('accessToken', $login)) {
                $this->accessToken = $login['accessToken'];
                $this->account->authorization = $this->accessToken;
                $this->account->accounts = $login['identity'] ?? [];
                $this->account->status        = 1;
                $this->account->account_holder_name      = ($login['identity']['name']['givenName'] ?? '') . ' ' . ($login['identity']['name']['familyName'] ?? '');
                $this->account->last_status      = 'Đăng nhập thành công';
                $this->account->save();
                return array(
                    'status' => true,
                    'message' => 'Import ACB successfully.',
                    'access_token' => $this->accessToken,
                );
            } else {
                if ($this->account->id) {
                    $this->account->last_status = 'Đăng nhập không thành công';
                    $this->account->save();
                }
                return array(
                    'status' => false,
                    'message' => 'Cannot login this account!'
                );
            }
        }
        if ($this->account->id) {
            $this->account->last_status = 'Đăng nhập không thành công';
            $this->account->save();
        }
        return array(
            'status' => false,
            'message' => 'Sai thông tin tài khoản hoặc mật khẩu'
        );
    }

    public function history()
    {
        $status = false;
        $message = 'Error exception';
        $data = array();
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Authorization' => 'Bearer ' . trim($this->account->authorization)
        );
        $count = 0;
        while (true) {
            $history = $this->curl('GET', null, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/transfers/account-payment?account=' . $this->account->account_no, $headers);
            if ($history != false) {
                $data = $history;
                $status = true;
                $message = 'Successfully';
                break;
            } else {
                $login = $this->login();
            }
            $count++;
            if ($count > 5) {
                $message = 'Connect false';
                break;
            }
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function getHistories($from_date, $to_date, $page = 1, $size = 5)
    {
        //from_date và to_date để dạng milisecond int (Unix Timestamp)
        $data = array();
        $headers = array(
            'Host' => 'apiapp.acb.com.vn',
            'Accept' => 'application/json, text/plain, */*',
            'Connection' => 'keep-alive',
            'User-Agent' => 'ACB-MBA/3 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Accept-Language' => 'vi',
            'Authorization' => 'Bearer ' . trim($this->account->authorization),
            'Accept-Encoding' => 'gzip, deflate, br'
        );

        $dataPost = [
            'account' => $this->account->account_no,
            'maxRows' => $size
        ];
        $history = $this->curl('GET', null, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/saving/tx-history?' . http_build_query($dataPost), $headers);
        if ($history && isset($history['codeStatus']) && $history['codeStatus'] == 200 && $history['messageStatus'] == 'success') {
            return array(
                'status' => true,
                'data' => $history
            );
        }

        return array(
            'status' => false,
        );
    }


    public function tranferLimit($receivedBank, $napasBankCode, $tranferTo)
    {
        $status = false;
        $message = 'Error exception';
        $data = array();
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Authorization' => 'Bearer ' . trim($this->account->authorization)
        );
        $params = array(
            "accountNumber" => $this->username,
            "transferType" => "3",
            "receivedBank" => $receivedBank, // bank bên get tên
            "napasBankCode" => $napasBankCode, // id bank
            "receivedAccountNumber" => $tranferTo, // stk gửi
            "transferTime" => ["type" => 1, "period" => 0, "startDate" => 0, "endDate" => 0],
            "receivedCardNumber" => "",
            "receivedIdCardNumber" => "",
            "receivedPassportNumber" => ""
        );
        $count = 0;
        while (true) {
            $limit = $this->curl('POST', $params, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/transfers/transaction-limits', $headers);
            if ($limit != false) {
                $data = $limit;
                $status = true;
                $message = 'Successfully';
                break;
            } else {
                $login = $this->login();
            }
            $count++;
            if ($count > 5) {
                $message = 'Connect false';
                break;
            }
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function tranfer($local, $type, $amount, $partnerName, $name_bank, $comment, $tranferTo, $receivedBank, $napasBankCode, $bySMS, $bySafeKey)
    {
        $status = false;
        $message = 'Error exception';
        $data = array();
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Authorization' => 'Bearer ' . trim($this->account->authorization)
        );
        $count = 0;
        $params = array(
            "type" => $type,
            "authMethod" => "OTPS", //SMS
            "menu" => "$local",
            "amount" => intval($amount),
            "currency" => "VND",
            "fromAccount" => $this->account->account_no,
            "transactionAmount" => intval($amount),
            "receiverName" => $partnerName, // tên người nhận
            "bankName" => $name_bank,
            "comment" => $comment, // nội dung
            "transferTime" => ["type" => 1, "period" => 0, "startDate" => 0, "endDate" => 0],
            "fee" => 0,
            "resultToEmails" => [],
            "accountInfo" => [
                "accountNumber" => $tranferTo,
                "bankCode" => $receivedBank,
                "bankName" => $name_bank,
                "napasBankCode" => $napasBankCode,
                "bankcheckId" => 0
            ],
            "bankCode" => $receivedBank,
            "napasBankCode" => $napasBankCode,
            "referenceNumber" => "",
            "province" => "",
            "mbTransactionLimitInfo" => [
                "byPass" => 0,
                "bySMS" => $bySMS,
                "byToken" => 0,
                "bySafeKey" => $bySafeKey,
                "byAdvSafeKey" => null
            ]
        );
        while (true) {
            $tranfer = $this->curl('POST', $params, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/transfers/submit', $headers);
            if ($tranfer != false) {
                $data = $tranfer;
                $status = true;
                $message = 'Successfully';
                break;
            } else {
                $login = $this->login();
            }
            $count++;
            if ($count > 5) {
                $message = 'Connect false';
                break;
            }
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function confirmTranfer($uuid, $otp)
    {
        $status = false;
        $message = 'Error exception';
        $data = array();
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json, text/plain, */*',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Authorization' => 'Bearer ' . trim($this->account->authorization),
            'Host' => 'apiapp.acb.com.vn',
            'Connection' => 'keep-alive',
            'Accept-Language' => 'vi',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Cookie' => ''
        );
        $params = array(
            'uuid' => $uuid,
            'code' => $otp,
            'authMethod' => 'OTPS'
        );
        $count = 0;
        while (true) {
            $confirm = $this->curl('POST', $params, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/transfers/verify', $headers);
            if ($confirm != false) {
                $data = $confirm;
                $status = true;
                $message = 'Successfully';
                break;
            } else {
                $login = $this->login();
            }
            $count++;
            if ($count > 5) {
                $message = 'Connect false';
                break;
            }
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function getDetail()
    {
        $status = false;
        $message = 'Error exception';
        $data = array();
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Authorization' => 'Bearer ' . trim($this->account->authorization)
        );
        $count = 0;
        while (true) {
            $detail = $this->curl('GET', null, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/transfers/list/account-payment', $headers);
            if ($detail != false) {
                $data = $detail;
                $status = true;
                $message = 'Successfully';
                break;
            } else {
                $login = $this->login();
            }
            $count++;
            if ($count > 5) {
                $message = 'Connect false';
                break;
            }
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function getBankName($tranferTo, $bankCode)
    {
        $status = false;
        $message = 'Error exception';
        $data = array();
        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ACB-MBA/1 CFNetwork/1128.0.1 Darwin/19.6.0',
            'Authorization' => 'Bearer ' . trim($this->account->authorization)
        );
        $count = 0;
        while (true) {
            $bankName = $this->curl('GET', null, 'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/transfers/accounts/' . $tranferTo . '?bankCode=' . $bankCode . '&accountNumber=' . $this->account->account_no, $headers);
            if ($bankName != false) {
                $data = $bankName;
                $status = true;
                $message = 'Successfully';
                break;
            } else {
                $login = $this->login();
            }
            $count++;
            if ($count > 5) {
                $message = 'Connect false';
                break;
            }
        }
        return array(
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
    }

    public function curl($method, $data, $url, $headers)
    {
        try {
            $client = new Client();
            $res = $client->request($method, $url, [
                'proxy' => $this->getProxy(),
                'verify' => true,
                'timeout' => 30,
                'headers' => $headers,
                'body' => json_encode($data),
            ]);
            $body = json_decode($res->getBody(), true);
            return $body;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getProxy()
    {
        $proxy = Proxy::where('status', 1)->first();
        if ($proxy) {

            return $proxy->proxy;
        }

        return "";
    }
}
