<?php

namespace App\Http\Controllers;

use App\Models\Hook;
use App\Services\HaravanService;
use Illuminate\Http\Request;

class HaravanController extends Controller
{
    protected $haravanService;
    public function __construct()
    {
        $this->haravanService = new HaravanService();
    }
    public function status(Request $request)
    {
        $hook = Hook::where('id', $request->hook_id)->whereIn('hook_type', ['haravan'])->first();
        if (!$hook) {
            return response()->json([
                'pay_status' => false,
            ]);
        }
        $result  = $this->haravanService->checkStatus($hook, $request->order_id);
        if (isset($result['status']) && $result['status'] == false) {
            return response()->json([
                'pay_status' => false,
            ]);
        }
        if (isset($result['order']) && isset($result['order']['financial_status']) && $result['order']['financial_status'] == 'paid') {
            return response()->json([
                'pay_status' => true,
            ]);
        }
        return response()->json([
            'pay_status' => false,
        ]);
    }
}
