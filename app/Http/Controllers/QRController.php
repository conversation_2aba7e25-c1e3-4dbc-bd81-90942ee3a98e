<?php

namespace App\Http\Controllers;

use App\Models\Bank;
use App\Models\VietQrStore;
use App\Services\VietQR;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Http\Request;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class QRController extends Controller
{
    public function generate()
    {
        return view('qr.generate');
    }

    public function quicklink(Request $request, $bank_code, $account_no, $account_name)
    {
        $bank = Bank::where('code', $bank_code)->first();
        abort_if(empty($bank), 404);
        $dataPost = [
            "bank_bin" => $bank->bin,
            "account_no" => $account_no,
            "account_name" => $account_name,
            // "frame_id" => "",
            // "is_mask" => "",
            // "amount" => "",
            // "memo" => "",
        ];
        if ($request->has('bg')) {
            abort_if($request->bg < 0 || $request->bg > 0, 404);
            $dataPost['frame_id'] = $request->bg;
        }
        if ($request->has('is_mask') && $request->is_mask == 1) {
            $dataPost['is_mask'] = 1;
        }
        if ($request->has('amount') && $request->amount >= 1000 && $request->amount <= *************) {
            $dataPost['amount'] = $request->amount;
        }
        if ($request->has('memo') && strlen($request->memo) <= 25) {
            $dataPost['memo'] = $request->memo;
        }
        $str_bank = $dataPost['account_no'];
        if (isset($dataPost['is_mask'])) {
            $str_bank = $this->stringToSecret($str_bank);
        }

        $rs = VietQR::get_string($dataPost);

        $qrCode = QrCode::create($rs)
            ->setEncoding(new Encoding('UTF-8'))
            ->setSize(450)
            ->setMargin(10);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);

        $logo_bank = Bank::where('bin', $bank->bin)->first();

        if (isset($dataPost['frame_id'])) {
            $manager = new ImageManager(
                new Driver()
            );
            $qr = $manager->read($result->getDataUri());

            $file_logo = $manager->read(public_path('assets/media/logo_banks/' . $logo_bank->code . '.png'))
                ->scale(290);
            $allpay_logo = $manager->read(public_path('assets/media/logos/logo-green2.png'))
                ->scale(270);
            $str_bank = $dataPost['account_no'];
            if (isset($dataPost['is_mask'])) {
                $str_bank = $this->stringToSecret($str_bank);
            }

            $frame = @imagecreatefrompng(public_path('assets/media/frames/bg-' . $dataPost['frame_id'] . '.png'));
            $img = $manager
                ->read($frame)
                ->place($file_logo, 'center', 0, 290)
                ->place($allpay_logo, 'top-left', 160, 100)
                ->place($qr, 'center', 0, -45)
                ->text('Chủ tài khoản: ' . strtoupper($dataPost['account_name']), 424, 930, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM Avo.ttf'));
                    $font->size(30);
                    $font->color('#1e427e');
                    $font->align('center');
                })
                ->text('Số tài khoản: ' . $str_bank, 424, 975, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM AvoBold.ttf'));
                    $font->size(30);
                    $font->color('#1e427e');
                    $font->align('center');
                })
                ->text($logo_bank->name, 424, 1020, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM Avo.ttf'));
                    $font->size(25);
                    $font->color('#1e427e');
                    $font->align('center');
                });
            // header('Content-Type: image/png');
            // echo $img->toPng();
            // exit;


            return response($img->toPng(), 200)
                ->header('Content-Type', 'image/png')
                ->header('Access-Control-Allow-Origin', '*');
        } else {
            // header('Content-Type: ' . $result->getMimeType());
            // echo $result->getString();
            // exit;
            return response($result->getString(), 200)
                ->header('Content-Type', $result->getMimeType())
                ->header('Access-Control-Allow-Origin', '*');
        }
    }

    public function vietqrStore($slug)
    {
        $qrStore = VietQrStore::where('slug', $slug)->first();
        abort_if(!$qrStore, 404);
        $dataPost = $qrStore->toArray();

        $str_bank = $dataPost['account_no'];
        if (isset($dataPost['is_mask'])) {
            $str_bank = $this->stringToSecret($str_bank);
        }

        $rs = VietQR::get_string($dataPost);

        $qrCode = QrCode::create($rs)
            ->setEncoding(new Encoding('UTF-8'))
            ->setSize(450)
            ->setMargin(10);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);

        $logo_bank = Bank::where('bin', $dataPost['bank_bin'])->first();

        if (isset($dataPost['frame_id']) && $dataPost['frame_id'] != "") {
            $manager = new ImageManager(
                new Driver()
            );
            $qr = $manager->read($result->getDataUri());

            $file_logo = $manager->read(public_path('assets/media/logo_banks/' . $logo_bank->code . '.png'))
                ->scale(290);
            $allpay_logo = $manager->read(public_path('assets/media/logos/logo-green2.png'))
                ->scale(270);
            $str_bank = $dataPost['account_no'];
            if (isset($dataPost['is_mask'])) {
                $str_bank = $this->stringToSecret($str_bank);
            }

            $frame = @imagecreatefrompng(public_path('assets/media/frames/bg-' . $dataPost['frame_id'] . '.png'));
            $img = $manager
                ->read($frame)
                ->place($file_logo, 'center', 0, 290)
                ->place($allpay_logo, 'top-left', 160, 100)
                ->place($qr, 'center', 0, -45)
                ->text('Chủ tài khoản: ' . strtoupper($dataPost['account_name']), 424, 930, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM Avo.ttf'));
                    $font->size(30);
                    $font->color('#1e427e');
                    $font->align('center');
                })
                ->text('Số tài khoản: ' . $str_bank, 424, 975, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM AvoBold.ttf'));
                    $font->size(30);
                    $font->color('#1e427e');
                    $font->align('center');
                })
                ->text($logo_bank->name, 424, 1020, function ($font) {
                    $font->file(public_path('assets/fonts/utm/UTM Avo.ttf'));
                    $font->size(25);
                    $font->color('#1e427e');
                    $font->align('center');
                });
            // header('Content-Type: image/png');
            // echo $img->toPng();
            // exit;

            return response($img->toPng(), 200)
                ->header('Content-Type', 'image/png');
        } else {
            // header('Content-Type: ' . $result->getMimeType());
            // echo $result->getString();
            // exit;
            return response($result->getString(), 200)
                ->header('Content-Type', $result->getMimeType());
        }
    }

    private function stringToSecret(string $string = NULL)
    {
        if (!$string) {
            return NULL;
        }
        $length = strlen($string);
        $visibleCount = (int) round($length / 4);
        $hiddenCount = $length - ($visibleCount * 2);
        return substr($string, 0, $visibleCount) . str_repeat('*', $hiddenCount) . substr($string, ($visibleCount * -1), $visibleCount);
    }
}
