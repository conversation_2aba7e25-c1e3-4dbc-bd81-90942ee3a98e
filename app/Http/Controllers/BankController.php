<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use Illuminate\Support\Facades\Auth;
use App\Models\BIDVVaNumber;

class BankController extends Controller
{
    public function addVcb()
    {
        return view('bank.add.vcb');
    }

    public function addAcb()
    {
        return view('bank.add.acb');
    }

    public function addMb()
    {
        return view('bank.add.mb');
    }

    public function addIcb()
    {
        return view('bank.add.icb');
    }

    public function addBidv()
    {
        return view('bank.add.bidv');
    }

    public function addBusinessBidv()
    {
        return view('bank.add.business.bidv');
    }

    public function accountsVcb()
    {
        return view('bank.accounts.vcb');
    }

    public function accountsAcb()
    {
        return view('bank.accounts.acb');
    }

    public function accountsMb()
    {
        return view('bank.accounts.mb');
    }

    public function accountsIcb()
    {
        return view('bank.accounts.icb');
    }

    public function accountsBusinessBidv()
    {
        return view('bank.accounts.business.bidv');
    }

    public function detailBusinessBidv($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->with('vaNumbers')
            ->business()->bidv()->where('id', $id)->first();
        abort_if(!$bank, 404);
        return view('bank.detail.business.bidv', compact('bank'));
    }

    public function addVaNumberBidv($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->with('vaNumbers')
            ->business()->bidv()->where('id', $id)->first();
        abort_if(!$bank, 404);
        return view('bank.add.va-number.bidv', compact('bank'));
    }

    public function accountsBidv()
    {
        return view('bank.accounts.bidv');
    }

    public function reports()
    {
        return view('transaction.report');
    }

    public function editAccountVcb($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->vcb()->where('id', $id)->first();
        abort_if(!$bank, 404);

        return view('bank.edit.vcb', compact('bank'));
    }

    public function editAccountAcb($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->acb()->where('id', $id)->first();
        abort_if(!$bank, 404);

        return view('bank.edit.acb', compact('bank'));
    }

    public function editAccountMb($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->mb()->where('id', $id)->first();
        abort_if(!$bank, 404);

        return view('bank.edit.mb', compact('bank'));
    }

    public function editAccountIcb($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->icb()->where('id', $id)->first();
        abort_if(!$bank, 404);

        return view('bank.edit.icb', compact('bank'));
    }

    public function editAccountBidv($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->bidv()->where('id', $id)->first();
        abort_if(!$bank, 404);

        return view('bank.edit.bidv', compact('bank'));
    }

    public function unsubscribeMB($id)
    {
        $bank = BankAccount::where('user_id', Auth::user()->id)->mb()->where('open_api', 1)->where('id', $id)->first();
        abort_if(!$bank, 404);

        return view('bank.unsubscribe.mb', compact('bank'));
    }

    public function deleteVA($id)
    {
        $va = BIDVVaNumber::where('id', $id)
            ->whereHas('bankAccount', function ($query) {
                $query->where('user_id', Auth::user()->id);
            })
            ->first();

        if (!$va) {
            abort(404);
        }

        $bankId = $va->bank_account_id;
        $va->delete();

        return redirect()->route('detail.business.bidv', ['id' => $bankId])
            ->with('success', 'Đã xoá tài khoản ảo thành công');
    }
}
