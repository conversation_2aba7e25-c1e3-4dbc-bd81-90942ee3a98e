<?php

namespace App\Http\Controllers;

use App\Http\Resources\BankTransactionResource;
use App\Models\Bank;
use App\Models\BankTransaction;
use App\Models\DepositHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{
    public function transactions($token, Request $request)
    {

        $result = BankTransaction::whereHas('bankAccount', function ($q) use ($token) {
            $q->where('api_token', $token);
        })
            ->orderBy('actual_at', 'DESC')->limit($request->limit ?? 5)->get();

        return BankTransactionResource::collection($result);
    }

    public function banks()
    {
        return response()->json(Bank::all());
    }

    public function depositEndpoint(Request $request)
    {
        $token = config('app.allpay_token_deposit');

        $allPayToken = $request->header('ALLPAY-TOKEN');

        if ($token !== $allPayToken) {

            return response([
                'success' => false,
                'message' => 'Token missmatch !'
            ], 400);
        }
        $data = $request->all();

        if ($data['type'] == '-') {
            return;
        }

        if (!preg_match(sprintf('/%s(\d+)/i', config('app.deposit_syntax')), $data['description'], $matches)) {
            return;
        }

        $userId = preg_replace('#(\D+)#', '', $matches[1]);
        $user = User::find($userId);
        if (!$user) {
            return;
        }
        $exists = DepositHistory::where('bank_code', $data['bank_code'])->where('ref', $data['reference'])->first();

        if ($exists) {
            return;
        }

        try {
            DB::beginTransaction();
            $user->coin += $data['amount'];
            $user->total_coin += $data['amount'];
            $user->save();

            DepositHistory::create([
                'user_id' => $user->id,
                'amount' => $data['amount'],
                'description' => $data['description'],
                'ref' => $data['reference'],
                'bank_code' => $data['bank_code'],
            ]);
            DB::commit();

            return response()->json([
                'status' => true,
                'msg' => 'OK',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Deposit Endpoint Error" . $e->getMessage());
            return response()->json([
                'status' => true,
                'msg' => $e->getMessage(),
            ]);
        }
    }
}
