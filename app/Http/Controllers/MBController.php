<?php

namespace App\Http\Controllers;

use App\Jobs\PushHook;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\HookLog;
use App\Models\MBTransaction;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MBController extends Controller
{
    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;

    public function __construct()
    {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
    }

    public function token_generate(Request $request)
    {
        $username = $request->getUser();
        $password = $request->getPassword();
        if (Auth::attempt(['username' => $username, 'password' => $password])) {
            $user = Auth::user();
            $token = $user->createToken('API Token', ['*'], now()->addMinute());

            return response()->json([
                'access_token' => $token->plainTextToken,
                'type' => 'bearer',
                'expires_in' => 59,
            ]);
        }

        return response()->json(['message' => 'Invalid credentials'], 401);
    }

    public function transactionSync(Request $request)
    {
        try {
            $data = $request->all();
            Log::debug($request->all());
            $bankAccount = BankAccount::where('account_no', $data['bankaccount'])->where('open_api', 1)->first();
            if ($bankAccount) {
                $actualTime = Carbon::createFromTimestampMs($data['transactiontime'], 'Asia/Ho_Chi_Minh');
                $transaction = BankTransaction::create([
                    'bank_account_id' => $bankAccount->id,
                    'user_id' => $bankAccount->user_id,
                    'transaction_id' => $data['referencenumber'],
                    'cus_tran_id' => $data['referencenumber'],
                    'memo' => $data['content'],
                    'amount' => $data['amount'],
                    'type' => $data['transType'] == 'C' ? 1 : 2,
                    'status' => 0,
                    'actual_at' => $actualTime,
                ]);
                // $trans = MBTransaction::create($request->all());
                if ($bankAccount->activeHooks) {
                    PushHook::dispatch($bankAccount, $transaction);
                }
                return response()->json([
                    "error" => false,
                    "errorReason" =>  "000",
                    "toastMessage" =>  "",
                    "object" =>  [
                        "reftransactionid" =>  $transaction->id,
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error($e);
            Log::debug($request->all());
            return response()->json([
                "error" =>  true,
                "errorReason" =>  "001",
                "toastMessage" =>  $e->getMessage(),
            ]);
        }
    }
}
