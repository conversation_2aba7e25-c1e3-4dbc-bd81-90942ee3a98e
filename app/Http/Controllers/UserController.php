<?php

namespace App\Http\Controllers;

use App\Http\Requests\ChangePassRequest;
use App\Http\Requests\EditUserRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegisterRequest;
use App\Models\Category;
use App\Models\User;
use App\Models\HistoryBank;
use App\Models\HistoryBuy;
use App\Models\Login;
use App\Models\Package;
use App\Models\Type;
use App\Models\UserPackage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function username()
    {
        return 'username';
    }

    public function login(Request $request)
    {
        if (Auth::check()) {
            return redirect()->route('dashboard');
        }
        return view('auth.login');
    }

    public function register(Request $request)
    {
        if (Auth::check()) {
            return redirect()->route('dashboard');
        }
        return view('auth.register');
    }

    public function logout()
    {
        Auth::logout();
        return redirect()->route("login");
    }

    public function postLogin(LoginRequest $request)
    {
        $data = [
            'username' => $request->get("username"),
            'password' => $request->get("password"),
        ];

        $remember = $request->has('remember') ? true : false;


        if (Auth::attempt($data, $remember)) {
            $user = Auth::user();
            return redirect()->route('dashboard');
        } else {
            return redirect()->route("login")->with('login_fail', 'Thông tin đăng nhập không chính xác!');
        }
    }

    public function postRegister(RegisterRequest $request)
    {
        $user = new User();
        $user->username = $request->username;
        $user->password = bcrypt($request->password);
        $user->email = $request->email;
        $user->save();

        $packageFree = Package::where('price', 0)->first();
        UserPackage::create(
            [
                'user_id' => $user->id,
                'package_id' => $packageFree->id,
                'start_at' => now(),
                'end_at' => now()->addMonth(),
                'expire_at' => now()->addMonth(),
            ]
        );
        return redirect()->route('login')->with('register_completed', 'Tạo tài khoản thành công. Bạn có thể đăng nhập');
    }
}
