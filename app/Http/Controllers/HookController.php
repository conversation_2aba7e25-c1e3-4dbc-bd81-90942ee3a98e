<?php

namespace App\Http\Controllers;

use App\Models\Hook;

class HookController extends Controller
{
    public function index()
    {
        return view('hook.index');
    }

    public function add()
    {
        return view('hook.add');
    }

    public function logs()
    {
        return view('hook.logs');
    }

    public function embed($id)
    {
        $hook = Hook::with('bankAccount')->where('id', $id)->whereIn('hook_type', ['haravan'])->first();
        abort_if(!$hook, 404);
        $bankAccount = $hook->bankAccount;
        $data = [
            "account_number" =>  $bankAccount->account_no,
            "bank_bin" =>  $bankAccount->bank->bin,
            "bank_brand_name" =>  $bankAccount->bank->shortName,
            "account_name" =>  $bankAccount->account_holder_name,
            "prefix" =>  $hook->syntax,
            "bank_code" =>  $bankAccount->bank->code,
            "hook_id" =>  $id
        ];
        $jsonData = json_encode($data);
        $base64 = base64_encode($jsonData);

        return view('hook.embed', compact('base64'));
    }
}
