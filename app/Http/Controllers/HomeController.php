<?php

namespace App\Http\Controllers;

use App\Models\BankTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
    public function dashboard()
    {

        $inMonthlySums = BankTransaction::select(DB::raw('SUM(amount) as sum'), DB::raw('MONTH(actual_at) as month'))
            ->whereYear('actual_at', date('Y')) // Current year
            ->where('type', 1)
            ->where('user_id', Auth::user()->id)
            ->groupBy(DB::raw('MONTH(actual_at)'))
            ->orderBy(DB::raw('MONTH(actual_at)'))
            ->pluck('sum', 'month');
        $inMonthlyData = array_fill(0, 12, 0);

        foreach ($inMonthlySums as $month => $sum) {
            $inMonthlyData[$month - 1] = $sum;
        }

        $outMonthlySums = BankTransaction::select(DB::raw('SUM(amount) as sum'), DB::raw('MONTH(actual_at) as month'))
            ->whereYear('actual_at', date('Y')) // Current year
            ->where('type', 2)
            ->where('user_id', Auth::user()->id)
            ->groupBy(DB::raw('MONTH(actual_at)'))
            ->orderBy(DB::raw('MONTH(actual_at)'))
            ->pluck('sum', 'month');
        $outMonthlyData = array_fill(0, 12, 0);

        foreach ($outMonthlySums as $month => $sum) {
            $outMonthlyData[$month - 1] = $sum;
        }

        $lastTransactions = BankTransaction::with('bankAccount')
            ->where('user_id', Auth::user()->id)
            ->orderBy('actual_at', 'DESC')->limit(3)->get();

        $currentPackage = Auth::user()->currentPackage;
        $currentPackageTransactions = BankTransaction::withTrashed()->where('user_id', Auth::user()->id)
            ->where('actual_at', '>=', $currentPackage->start_at)
            ->where('actual_at', '<=', $currentPackage->end_at)
            ->get();
        return view('dashboard', compact(
            'lastTransactions',
            'inMonthlyData',
            'outMonthlyData',
            'currentPackage',
            'currentPackageTransactions'
        ));
    }

    public function naptien()
    {
        return view('naptien');
    }

    public function upgradePlan()
    {
        return view('upgrade-plan');
    }

    public function depositHistory()
    {
        return view('deposit-history');
    }

    public function renewPackage()
    {
        $user = Auth::user();
        $currentPackage = $user->currentPackage;
        if ($currentPackage->package->price == 0) {
            return response()->json(['status' => false]);
        }

        if ($currentPackage->package->price > $user->coin) {
            return response()->json(['status' => false, 'msg' => 'Số dư không đủ']);
        }

        try {
            DB::beginTransaction();
            $currentPackage->expire_at = now()->parse($currentPackage->expire_at)->addMonth();
            $currentPackage->save();
            $user->coin -= $currentPackage->package->price;
            $user->save();
            DB::commit();
            return response()->json(['status' => true, 'msg' => 'Gia hạn thành công']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json(['status' => false, 'msg' => 'Lỗi hệ thống']);
        }
    }
}
