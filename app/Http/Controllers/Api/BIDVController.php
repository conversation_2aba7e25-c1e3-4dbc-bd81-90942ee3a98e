<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\PushHook;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use App\Models\BIDVVaNumber;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\HaravanService;
use App\Services\HookService;
use App\Services\LarkService;
use App\Services\SlackService;
use App\Services\TelegramService;
use App\Models\HookLog;

class BIDVController extends Controller
{
    protected $larkService;
    protected $hookService;
    protected $telegramService;
    protected $slackService;
    protected $haravanService;

    public function __construct()
    {
        $this->larkService = new LarkService();
        $this->hookService = new HookService();
        $this->telegramService = new TelegramService();
        $this->slackService = new SlackService();
        $this->haravanService = new HaravanService();
    }

    public function paybill(Request $request)
    {
        try {
            $data = $request->validate([
                'trans_id' => 'required',
                'coreRefNo' => 'required',
                'trans_date' => 'required',
                'customer_id' => 'required',
                'amount' => 'required',
                'remark' => 'required',
                'checksum' => 'required',
                'service_id' => 'nullable',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'result_code' => "001",
                'result_message' => 'Thiếu tham số'
            ]);
        }
        try {
            $secret_code = config('tino_bidv.secret_code');

            $checksum = base64_encode(hash_hmac('sha256', $secret_code . '|' . $data['trans_id'] . '|' .  $data['customer_id'] . '|' . $data['amount'], $secret_code, true));
            if ($checksum != $data['checksum']) {
                return response()->json([
                    'result_code' => "004",
                    'result_message' => 'Sai chữ ký/Sai checksum'
                ]);
            }

            $vaNumber = BIDVVaNumber::where('va_number', $data['customer_id'])
                ->whereHas('bankAccount', function ($query) use ($data) {
                    $query->where('service_id', $data['service_id']);
                })->first();

            if (!$vaNumber) {
                return response()->json([
                    'result_code' => "011",
                    'result_message' => 'Mã khách hàng không đúng/ không tồn tại'
                ]);
            }
            $existTrans = BankTransaction::where('transaction_id', $data['trans_id'])
                ->where('bank_account_id', $vaNumber->bankAccount->id)->first();

            if ($existTrans) {
                return response()->json([
                    'result_code' => "023",
                    'result_message' => 'Giao dịch đã được gạch nợ (mỗi giao dịch chỉ gạch nợ 1 lần)'
                ]);
            }

            $transaction = BankTransaction::create([
                'bank_account_id' => $vaNumber->bankAccount->id,
                'user_id' => $vaNumber->bankAccount->user_id,
                'transaction_id' => $data['trans_id'],
                'coreRefNo' => $data['coreRefNo'],
                'cus_tran_id' => $data['trans_id'],
                'memo' => $data['remark'],
                'amount' => (int)str_replace(',', '', $data['amount']),
                'type' => 1,
                'status' => 0,
                'actual_at' => Carbon::createFromFormat('YmdHis', $data['trans_date'])->format('Y-m-d H:i:s'),
            ]);
            if ($vaNumber->bankAccount->activeHooks) {
                PushHook::dispatch($vaNumber->bankAccount, $transaction);
            }
            Log::debug($data);


            return response()->json([
                'result_code' => "000",
                'result_message' => 'Thành công'
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                'result_code' => "031",
                'result_message' => 'Có lỗi hệ thống'
            ]);
        }
    }

    public function getbill(Request $request)
    {
        try {
            $data = $request->validate([
                'customer_id' => 'required',
                'checksum' => 'required',
                'service_id' => 'required',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'result_code' => "001",
                'result_message' => 'Thiếu tham số'
            ]);
        }
        try {
            Log::debug($data);
            $secret_code = config('tino_bidv.secret_code');

            $checksum = base64_encode(hash_hmac('sha256', $secret_code . '|' . $data['service_id'] . '|' . $data['customer_id'], $secret_code, true));
            if ($checksum != $data['checksum']) {
                return response()->json([
                    'result_code' => "004",
                    'result_message' => 'Sai chữ ký/Sai checksum'
                ]);
            }
            $existServiceId = BankAccount::where('service_id', $data['service_id'])->first();
            if (!$existServiceId) {
                return response()->json([
                    'result_code' => "006",
                    'result_message' => 'Service ID không đúng/ không tồn tại'
                ]);
            }
            $vaNumber = BIDVVaNumber::where('va_number', $data['customer_id'])->first();
            if (!$vaNumber) {
                return response()->json([
                    'result_code' => "011",
                    'result_message' => 'Mã khách hàng không đúng/ không tồn tại'
                ]);
            }
            return response()->json([
                'result_code' => "000",
                'result_desc' => 'success',
                'customer_id' => $vaNumber->va_number,
                'customer_name' => $vaNumber->va_name,
                'account_no' => $vaNumber->bankAccount->account_no,
                'type' => $vaNumber->type,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                'result_code' => "031",
                'result_message' => 'Có lỗi phát sinh từ hệ thống'
            ]);
        }
    }
}
