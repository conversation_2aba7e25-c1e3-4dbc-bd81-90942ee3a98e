<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BankAccount;
use App\Models\BIDVVaNumber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BIDVController extends Controller
{
    public function paybill(Request $request)
    {
        try {
            $data = $request->validate([
                'trans_id' => 'required',
                'trans_date' => 'required',
                'customer_id' => 'required',
                'amount' => 'required',
                'remark' => 'required',
                'checksum' => 'required',
                'service_id' => 'nullable',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'result_code' => "001",
                'result_message' => 'Thiếu tham số'
            ]);
        }
        try {
            $secret_code = config('tino_bidv.secret_code');

            $checksum = base64_encode(hash_hmac('sha256', $secret_code . '|' . $data['trans_id'] . '|' .  $data['customer_id'] . '|' . $data['amount'], $secret_code, true));
            if ($checksum != $data['checksum']) {
                return response()->json([
                    'result_code' => "004",
                    'result_message' => 'Sai chữ ký/Sai checksum'
                ]);
            }
            Log::debug($data);

            return response()->json([
                'result_code' => "000",
                'result_message' => 'Thành công'
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                'result_code' => "031",
                'result_message' => 'Có lỗi hệ thống'
            ]);
        }
    }

    public function getbill(Request $request)
    {
        try {
            $data = $request->validate([
                'customer_id' => 'required',
                'checksum' => 'required',
                'service_id' => 'required',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'result_code' => "001",
                'result_message' => 'Thiếu tham số'
            ]);
        }
        try {
            Log::debug($data);
            $secret_code = config('tino_bidv.secret_code');

            $checksum = base64_encode(hash_hmac('sha256', $secret_code . '|' . $data['service_id'] . '|' . $data['customer_id'], $secret_code, true));
            if ($checksum != $data['checksum']) {
                return response()->json([
                    'result_code' => "004",
                    'result_message' => 'Sai chữ ký/Sai checksum'
                ]);
            }
            $existServiceId = BankAccount::where('service_id', $data['service_id'])->first();
            if (!$existServiceId) {
                return response()->json([
                    'result_code' => "006",
                    'result_message' => 'Service ID không đúng/ không tồn tại'
                ]);
            }
            $vaNumber = BIDVVaNumber::where('va_number', $data['customer_id'])->first();
            if (!$vaNumber) {
                return response()->json([
                    'result_code' => "011",
                    'result_message' => 'Mã khách hàng không đúng/ không tồn tại'
                ]);
            }
            return response()->json([
                'result_code' => "000",
                'result_desc' => 'success',
                'customer_id' => $vaNumber->va_number,
                'customer_name' => $vaNumber->va_name,
                'account_no' => $vaNumber->bankAccount->account_no,
                'type' => $vaNumber->type,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                'result_code' => "031",
                'result_message' => 'Có lỗi phát sinh từ hệ thống'
            ]);
        }
    }
}
