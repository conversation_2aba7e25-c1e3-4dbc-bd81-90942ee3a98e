<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BankTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transaction_date' => $this->actual_at,
            'transaction_id' => $this->transaction_id,
            'amount' => $this->amount,
            'description' => $this->memo,
            'type' => $this->type == 1 ? "in" : "out",
        ];
    }
}
