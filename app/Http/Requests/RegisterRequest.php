<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'username' => 'required|unique:users|regex:/^[a-zA-Z0-9._-]{3,}$/',
            'password' => 'required',
            'password_confirm' => 'same:password',
            'email' => 'required|email|unique:users',
        ];

        return $rules;
    }

    public function messages()
    {
        return [
            'username.regex' => 'Tài khoản chỉ cho phép chữ và số, tối thiểu 3 kí tự.',
            'password_confirm.same' => 'Xác nhận mật khẩu không khớp.',
            'email.unique' => 'Email đã tồn tại.',
            'username.unique' => 'Tài khoản đã tồn tại.',
        ];
    }
}
