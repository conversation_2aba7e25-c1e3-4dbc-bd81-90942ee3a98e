<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionResource\Pages;
use App\Filament\Resources\TransactionResource\RelationManagers;
use App\Models\BankTransaction;
use App\Models\Transaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TransactionResource extends Resource
{
    protected static ?string $model = BankTransaction::class;
    public static ?string $label = 'Giao dịch';

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('#ID')->sortable(),
                TextColumn::make('user.username')->label('Khách hàng'),
                TextColumn::make('bankAccount.bank_code')->label('')
                    ->view('filament.tables.banks.icon')
                    ->sortable(),
                TextColumn::make('bankAccount.account_no')->label('Số tài khoản')
                    ->badge()
                    ->sortable(),
                TextColumn::make('cus_tran_id')->label('System TxId')->copyable()->copyMessage('copied')
                    ->copyMessageDuration(1500)->sortable(),
                TextColumn::make('amount')->label('Số tiền')->numeric()->badge()->sortable(),
                TextColumn::make('memo')->label('Nội dung')->badge()->copyable()->copyMessage('copied')
                    ->copyMessageDuration(1500),
                TextColumn::make('actual_at')->label('Ngày giao dịch')->badge()
            ])->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            'create' => Pages\CreateTransaction::route('/create'),
            'edit' => Pages\EditTransaction::route('/{record}/edit'),
        ];
    }
}
