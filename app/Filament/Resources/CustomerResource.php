<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Resources\Tables\Columns;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class CustomerResource extends Resource
{
    protected static ?string $model = User::class;
    public static ?string $label = 'Khách hàng';

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('username')->disabled(),
                        Forms\Components\TextInput::make('email')
                            ->label('Email address')
                            ->required()
                            ->email()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        ToggleButtons::make('role')->label('Quyền')->options([
                            0 => 'Khách hàng',
                            9 => 'Quản trị viên',
                        ])->inline()->columnSpanFull()
                            ->colors([
                                0 => 'info',
                                9 => 'success',
                            ])->icons([
                                0 => 'heroicon-m-sparkles',
                                9 => 'heroicon-m-check-badge',
                            ]),
                        // Radio::make('role')
                        //     ->label('Quyền')
                        //     ->options([
                        //         0 => 'Khách hàng',
                        //         9 => 'Quản trị viên',
                        //     ])->columnSpanFull(),
                        TextInput::make('password')
                            ->label(__('filament-panels::pages/auth/edit-profile.form.password.label'))
                            ->password()
                            ->revealable(filament()->arePasswordsRevealable())
                            ->rule(Password::default())
                            ->autocomplete('new-password')
                            ->dehydrated(fn ($state): bool => filled($state))
                            ->dehydrateStateUsing(fn ($state): string => Hash::make($state))
                            ->live(debounce: 500)
                            ->same('passwordConfirmation'),
                        TextInput::make('passwordConfirmation')
                            ->label(__('filament-panels::pages/auth/edit-profile.form.password_confirmation.label'))
                            ->password()
                            ->revealable(filament()->arePasswordsRevealable())
                            ->required()
                            ->visible(fn (Get $get): bool => filled($get('password')))
                            ->dehydrated(false)
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn ($record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Đăng ký')
                            ->content(fn ($record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Cập nhật gần nhất')
                            ->content(fn ($record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn ($record) => $record === null),
            ])
            ->columns(3);
    }

    // public static function form(Form $form): Form
    // {
    //     return $form
    //         ->schema([
    //             Radio::make('role')
    //                 ->label('Quyền')
    //                 ->options([
    //                     0 => 'Khách hàng',
    //                     9 => 'Quản trị viên',
    //                 ]),
    //             TextInput::make('Đổi mật khẩu')->password()->hiddenOn('view')

    //         ]);
    // }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('#ID')->searchable()->sortable(),
                TextColumn::make('username')->label('Tài khoản')->searchable()->sortable(),
                TextColumn::make('email')->label('Email')->searchable()->sortable(),
                TextColumn::make('currentPackage.package.name')->label('Gói sử dụng')->badge()->sortable(),
                TextColumn::make('created_at')->label('Ngày đăng ký')->sortable(),
            ])->defaultSort('id', 'desc')
            ->filters([])

            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('login_as')->label('Đăng nhập')->action(function (User $user) {
                    Auth::login($user);
                    redirect()->route('dashboard');
                })->requiresConfirmation()->color('danger')->icon('heroicon-m-finger-print'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
            'view' => Pages\ViewCustomer::route('/{record}'),
        ];
    }
}
