<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HookResource\Pages;
use App\Filament\Resources\HookResource\RelationManagers;
use App\Models\Hook;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HookResource extends Resource
{
    protected static ?string $model = Hook::class;
    public static ?string $label = 'Hooks';
    protected static ?string $navigationGroup = 'Hook';

    protected static ?string $navigationIcon = 'heroicon-o-code-bracket-square';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('#ID')->sortable(),
                TextColumn::make('user.username')->label('Khách hàng'),
                TextColumn::make('hook_type')->label('Loại')->badge()->copyable()
                    ->copyMessage('copied')
                    ->copyMessageDuration(1500),
                TextColumn::make('endpoint')->label('Endpoint')->copyable()
                    ->copyMessage('copied')
                    ->copyMessageDuration(1500),
                ViewColumn::make('status')
                    ->sortable()
                    ->label('Status')
                    ->view('filament.tables.banks.status')
            ])->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHooks::route('/'),
            'create' => Pages\CreateHook::route('/create'),
            'edit' => Pages\EditHook::route('/{record}/edit'),
        ];
    }
}
