<?php

namespace App\Filament\Resources;

use App\Enums\StatusBankEnum;
use App\Filament\Resources\BankAccountResource\Pages;
use App\Filament\Resources\BankAccountResource\RelationManagers;
use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Filters\QueryBuilder;
use Filament\Tables\Filters\QueryBuilder\Constraints\TextConstraint;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BankAccountResource extends Resource
{
    protected static ?string $model = BankAccount::class;
    protected static ?string $label = 'Tài khoản';

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ViewColumn::make('bank_code')
                    ->label('')
                    ->view('filament.tables.banks.icon')
                    ->sortable(),
                TextColumn::make('account_holder_name')->label('Tên tài khoản')->sortable(),
                TextColumn::make('username')->label('Tài khoản')
                    ->sortable()
                    ->copyable()
                    ->copyMessage('copied')
                    ->copyMessageDuration(1500),
                TextColumn::make('account_no')->label('Số tài khoản')
                    ->sortable()
                    ->copyable()
                    ->copyMessage('copied')
                    ->copyMessageDuration(1500),
                TextColumn::make('user.username')->label('Khách hàng')->sortable(),
                TextColumn::make('last_status')->label('Trạng thái gần nhất')->badge()->sortable(),
                ViewColumn::make('status')
                    ->sortable()
                    ->label('Status')
                    ->view('filament.tables.banks.status')
            ])
            ->filters([
                SelectFilter::make('user_id')->label('Khách hàng')
                    ->options(User::all()->pluck('username', 'id')->toArray()),
                SelectFilter::make('bank_code')->label('Ngân hàng')
                    ->options(Bank::all()->pluck('name', 'code')->toArray()),
                SelectFilter::make('status')->label('Status')
                    ->options([
                        1 => 'Hoạt động',
                        0 => 'Tạm dừng',
                    ]),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccounts::route('/'),
            'create' => Pages\CreateBankAccount::route('/create'),
            'edit' => Pages\EditBankAccount::route('/{record}/edit'),
        ];
    }
}
