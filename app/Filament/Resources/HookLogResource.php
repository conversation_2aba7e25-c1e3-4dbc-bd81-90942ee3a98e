<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HookLogResource\Pages;
use App\Filament\Resources\HookLogResource\RelationManagers;
use App\Models\HookLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HookLogResource extends Resource
{
    protected static ?string $model = HookLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';
    protected static ?string $navigationGroup = 'Hook';
    protected static ?string $label = 'Logs';


    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('ID')->sortable(),
                TextColumn::make('user.username')->label('Khách hàng')->sortable(),
                TextColumn::make('hook_id')->label('Hook ID')->sortable(),
                TextColumn::make('created_at')->label('Thời gian')->sortable(),
                TextColumn::make('hook.endpoint')->label('Hook Endpoint')->sortable(),
                TextInputColumn::make('response')->label('Hook Response'),
            ])->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHookLogs::route('/'),
            'create' => Pages\CreateHookLog::route('/create'),
            'edit' => Pages\EditHookLog::route('/{record}/edit'),
        ];
    }
}
