<?php

namespace App\Filament\Widgets;

use App\Models\BankTransaction;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class Orders<PERSON>hart extends ChartWidget
{
	protected static ?string $heading = 'Giao dịch mỗi tháng';

	protected static ?int $sort = 2;

	protected function getType(): string
	{
		return 'bar';
	}

	protected function getData(): array
	{
		$monthlyTransactions = BankTransaction::select(DB::raw('COUNT(id) as count'), DB::raw('MONTH(actual_at) as month'))
			->whereYear('actual_at', date('Y')) // Current year
			->groupBy(DB::raw('MONTH(actual_at)'))
			->orderBy(DB::raw('MONTH(actual_at)'))
			->pluck('count', 'month');
		$monthlyDataTransactions = array_fill(0, 12, 0);

		foreach ($monthlyTransactions as $month => $count) {
			$monthlyDataTransactions[$month - 1] = $count;
		}

		return [
			'datasets' => [
				[
					'label' => 'Giao dịch',
					'data' => $monthlyDataTransactions,
					'fill' => 'start',
					// 'backgroundColor' => '#5EBB46',
					// 'borderColor' => '#308A5A',
				],
			],
			'labels' => ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
		];
	}
}
