<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\Shop\OrderResource;
use App\Filament\Resources\TransactionResource;
use App\Models\Shop\Order;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Squire\Models\Currency;

class LatestTransactions extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 3;
    protected static ?string $heading = 'Giao dịch gần nhất';

    public function table(Table $table): Table
    {
        return $table
            ->query(TransactionResource::getEloquentQuery())
            ->defaultPaginationPageOption(5)
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('id')->label('#ID')->sortable(),
                TextColumn::make('user.username')->label('<PERSON>h<PERSON>ch hàng'),
                TextColumn::make('bankAccount.bank_code')->label('')
                    ->view('filament.tables.banks.icon')
                    ->sortable(),
                TextColumn::make('bankAccount.account_no')->label('Số tài khoản')
                    ->badge()
                    ->sortable(),
                TextColumn::make('cus_tran_id')->label('System TxId')->copyable()->copyMessage('copied')
                    ->copyMessageDuration(1500)->sortable(),
                TextColumn::make('amount')->label('Số tiền')->numeric()->badge()->sortable(),
                TextColumn::make('memo')->label('Nội dung')->badge()->copyable()->copyMessage('copied')
                    ->copyMessageDuration(1500),
                TextColumn::make('actual_at')->label('Ngày giao dịch')->badge()->sortable()
            ])
            ->actions([]);
    }
}
