<?php

namespace App\Filament\Widgets;

use App\Models\User;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class CustomersChart extends ChartWidget
{
    protected static ?string $heading = 'Khách hàng';

    protected static ?int $sort = 1;

    protected function getType(): string
    {
        return 'line';
    }

    protected function getData(): array
    {

        $inMonthlyCustomers = User::select(DB::raw('COUNT(id) as count'), DB::raw('MONTH(created_at) as month'))
            ->whereYear('created_at', date('Y')) // Current year
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy(DB::raw('MONTH(created_at)'))
            ->pluck('count', 'month');
        $inMonthlyDataCustomer = array_fill(0, 12, 0);

        foreach ($inMonthlyCustomers as $month => $count) {
            $inMonthlyDataCustomer[$month - 1] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Khách hàng',
                    'data' => $inMonthlyDataCustomer,
                    'fill' => 'start',
                    // 'backgroundColor' => '#5EBB46',
                    // 'borderColor' => '#308A5A',
                ],
            ],
            'labels' => ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
        ];
    }
}
