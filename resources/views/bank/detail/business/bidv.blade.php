@extends('layout.app')
@section('content')
    <div class="content">
        <div class="block block-rounded">
            <div class="block-header block-header-default bg-primary text-white">
                <h3 class="block-title">Chi tiết tài khoản BIDV</h3>
            </div>
            <div class="block-content block-content-full space-y-3">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="mb-2">
                            <label class="form-label" for="example-text-input">Số Tài khoản</label>
                            <input type="text" class="form-control" value="{{ $bank->account_no }}" readonly>
                        </div>
                        <div class="mb-2">
                            <label class="form-label" for="example-text-input">Tên chủ Tài khoản</label>
                            <input type="text" class="form-control" value="{{ $bank->account_holder_name }}" readonly>
                        </div>  
                    </div>
                </div>
            </div>
        </div>
        <div class="block block-rounded">
            <div class="block-header block-header-default bg-primary text-white">
                <h3 class="block-title">Danh sách tài khoản ảo (VA)</h3>
            </div>
            <div class="block-content block-content-full space-y-3">
                <div class="flex justify-end">
                    <a href="{{ route('bank.add.va.number.bidv', ['id' => $bank->id]) }}" class="btn btn-primary">Tạo mới VA</a>
                </div>
                <table class="table table-vcenter mt-4">
        <thead>
            <tr class="bg-xeco">
                <th class="bg-xeco text-white">ID</th>
                <th class="bg-xeco text-white">Số VA</th>
                <th class="bg-xeco text-white">Tên hiển thị</th>
                <th class="bg-xeco text-white"></th>
            </tr>
        </thead>
        <tbody>
            @if ($bank->vaNumbers)
            @foreach ($bank->vaNumbers as $item)
            <tr>
                <td class="">
                    <span class="">{{ $item->id}}</span>
                </td>
                <td class="">
                    <span class="fw-bold">{{ $item->va_number}}</span>
                </td>
                <td class="">
                    {{ $item->va_name ?? '' }}
                </td>
                <td class="">
                    <button class="btn btn-sm btn-danger" 
                            onclick="confirmDeleteVA({{ $item->id }})">
                        <i class="fa fa-trash-can"></i> Xoá
                    </button>
                </td>
            </tr>
            @endforeach
            @endif
        </tbody>
    </table>
            </div>
        </div>
    </div>
@endsection
@push('custom-scripts')
<script>
function confirmDeleteVA(vaId) {
    if (confirm('Xác nhận xoá tài khoản ảo này?')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/bank/va/delete/${vaId}`;
        
        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        // Add method spoofing for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
