<div class="content">
     @if (session('notifySuccess'))
        <div class="alert alert-success mt-2">
            {{ session('notifySuccess') }}
        </div>
    @endif
    <div class="block block-rounded">
        <div class="block-header block-header-default bg-primary text-white">
            <h3 class="block-title">Cấu trúc mã thanh toán</h3>
        </div>
        <div class="block-content block-content-full space-y-3">
            <button class="btn btn-primary" wire:click="addPayCode">Thêm cấu trúc mã mới</button>

            <div id="accordion" role="tablist" aria-multiselectable="true">
                <div class="block block-rounded mb-1">
                    
                    @if (!empty($payCodes))
                        @foreach ($payCodes as $key => $payCode)
                        <div class="block-header block-header-default" role="tab" id="accordion_h{{ $key }}">
                            <a class="fw-semibold" data-bs-toggle="collapse" data-bs-parent="#accordion"
                                href="#accordion_q{{ $key }}" aria-expanded="true" aria-controls="accordion_q{{ $key }}">Mẫu thứ {{ $key +1  }}</a>
                            <button class="btn btn-sm btn-danger" wire:click="deletePayCode({{ $key }})">Xoá</button>
                        </div>
                        <div id="accordion_q{{ $key }}" class="collapse show" role="tabpanel" aria-labelledby="accordion_h{{ $key }}"
                            data-bs-parent="#accordion">
                            <div class="block-content">
                                <div class="card-body pt-0">
                                    <div class="d-flex align-items-start gap-2">
                                        <div>
                                            <label class="form-label">Tiền tố</label>
                                            <input type="text" class="form-control @error('payCodes.' . $key . '.prefix') is-invalid @enderror" name="PayCodeStructures[{{ $key }}][prefix]"
                                                value="{{ $payCode['prefix'] }}" minlength="2" maxlength="5" placeholder="VD: DH" required="" wire:model="payCodes.{{ $key }}.prefix">
                                            <div class="form-text">2-5 ký tự</div>
                                            @error('payCodes.' . $key . '.prefix')
                                            <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="w-100">
                                            <label class="form-label">Hậu tố</label>
                                            <div class="d-flex align-items-center gap-2">
                                                <span>Từ</span>
                                                <select class="form-control @error('payCodes.' . $key . '.suffix_from') is-invalid @enderror" name="PayCodeStructures[{{ $key }}][suffix_from]" wire:model="payCodes.{{ $key }}.suffix_from">
                                                    @for ($i = 1; $i <= 30; $i++)
                                                        <option value="{{ $i }}" {{ $payCode['suffix_from'] == $i ? 'selected' : '' }}>
                                                            Từ {{ $i }} ký tự
                                                        </option>
                                                    @endfor
                                                </select>
                                                <span>Đến</span>
                                                <select class="form-control @error('payCodes.' . $key . '.suffix_to') is-invalid @enderror" name="PayCodeStructures[{{ $key }}][suffix_to]" wire:model="payCodes.{{ $key }}.suffix_to">
                                                    @for ($i = 1; $i <= 30; $i++)
                                                        <option value="{{ $i }}" {{ $payCode['suffix_to'] == $i ? 'selected' : '' }}>
                                                            Đến {{ $i }} ký tự
                                                        </option>
                                                    @endfor
                                                </select>
                                                <span>Là</span>
                                                <select class="form-control @error('payCodes.' . $key . '.character_type') is-invalid @enderror" name="PayCodeStructures[{{ $key }}][character_type]" wire:model="payCodes.{{ $key }}.character_type">
                                                    <option value="NumberOnly" {{ $payCode['character_type'] == 'NumberOnly' ? 'selected' : '' }}>
                                                        Số nguyên
                                                    </option>
                                                    <option value="NumberAndLetter" {{ $payCode['character_type'] == 'NumberAndLetter' ? 'selected' : '' }}>
                                                        Số và chữ
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach

                    @endif
                </div>
            </div>
            <button class="btn btn-primary" wire:click="savePayCode">Lưu cấu hình</button>
        </div>
    </div>
</div>