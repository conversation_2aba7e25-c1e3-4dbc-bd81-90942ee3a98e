<div class="content">
    <div class="flex justify-end">
        <a href="{{ route('bank.add.business.bidv') }}" class="btn btn-primary">Thêm tài khoản</a>
    </div>
    <table class="table table-vcenter mt-4">
        <thead>
            <tr class="bg-xeco">
                <th class="bg-xeco text-white">T<PERSON><PERSON>n</th>
                <th class="bg-xeco text-white">Chủ tài khoản</th>
                <th class="bg-xeco text-white">Mã đầu định danh</th>
                <th class="bg-xeco text-white"></th>
            </tr>
        </thead>
        <tbody>
            @if ($this->accounts)
            @foreach ($this->accounts as $item)
            <tr>
                <td class="">
                    <span class="fw-bold">{{ $item->account_no}}</span>
                </td>
                <td class="">
                    {{ $item->account_holder_name ?? '' }}
                </td>
                <td class="">
                    {{ $item->first_va_number ?? '' }}
                </td>
                <td class="">
                    <a href="{{ route('detail.business.bidv', ['id' => $item->id]) }}">Quản lý</a>
                </td>
            </tr>
            @endforeach
            @endif
        </tbody>
    </table>
    <div wire:loading>
        <div style="display: flex; justify-content: center; align-items: center; background-color: black; position: fixed; top: 0px; left: 0px; z-index: 9999; width: 100%; height: 100%; opacity: .75;">
        <div class="loader"></div> 
        </div>
    </div>
</div>