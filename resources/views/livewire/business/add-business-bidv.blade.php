<div class="content">
    <div class="block block-rounded">
        <div class="block-header block-header-default bg-primary text-white">
            <h3 class="block-title">Thêm thông tin ngân hàng BIDV API doanh nghiệp</h3>
        </div>
        <div class="block-content block-content-full space-y-3">
            <div class="row">
                <div class="col-lg-6">
                    <form wire:submit="save">
                        <div class="mb-2">
                            <label class="form-label" for="example-text-input">Số Tài khoản <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('account_no') is-invalid @enderror"
                                wire:model="account_no" placeholder="">
                            @error('account_no')
                            <div id="val-username-error" class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-2">
                            <label class="form-label" for="example-text-input">Tên chủ Tà<PERSON>ả<PERSON> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('account_holder_name') is-invalid @enderror"
                                wire:model="account_holder_name" placeholder="">
                            @error('account_holder_name')
                            <div id="val-username-error" class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-2">
                            <label class="form-label" for="example-text-input">Mã đầu định danh <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('first_va_number') is-invalid @enderror"
                                wire:model="first_va_number" placeholder="V2TN">
                            @error('first_va_number')
                            <div id="val-username-error" class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-2">
                            <label class="form-label" for="example-text-input">Tên gợi nhớ</label>
                            <input type="text" class="form-control @error('hint_name') is-invalid @enderror"
                                wire:model="hint_name" placeholder="">
                            @error('hint_name')
                            <div id="val-username-error" class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary">Thêm</button>
                        <div wire:loading>
                            Đang kiểm tra thông tin...
                        </div>
                    </form>

                </div>
                <div class="col-lg-6">
                    <div class="rounded-2 bg-body-light text-muted fs-sm p-2">
                        <p>- Nếu chưa có tài khoản doanh nghiệp, vui lòng liên hệ AllPay để được hỗ trợ mở tài khoản.</p>
                        <p>- Sau khi có tài khoản doanh nghiệp. Ký kết ủy quyền ba bên giữa BIDV, AllPay và khách hàng doanh nghiệp. Ở bước này, bạn cần liên hệ AllPay để được tư vấn chi tiết.</p>
                        <p>- Điền đầy đủ chính xác các thông tin được cung cấp từ phía ngân hàng BIDV (bạn có thể liên hệ AllPay để hỗ trợ cấu hình)</p>
                        <p>
                            - Mã đầu định danh: Sau khi kí kết hợp đồng với BIDV, KHDN sẽ được cấp mã đầu định danh có thể chọn 1 trong 2 định dạng sau:
                        </p>
                        <ul>
                            <li>
                                Bắt đầu bằng V, ví dụ: V2TN
                            </li>
                            <li>
                                Bắt đầu bằng 96, ví dụ: 96233
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- <div wire:loading>
        <div
            style="display: flex; justify-content: center; align-items: center; background-color: black; position: fixed; top: 0px; left: 0px; z-index: 9999; width: 100%; height: 100%; opacity: .75;">
            <div class="loader"></div>
        </div>
    </div> --}}
</div>