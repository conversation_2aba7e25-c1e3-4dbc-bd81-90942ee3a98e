<div class="content">
    <div class="block block-rounded">
        <div class="block-header block-header-default bg-primary text-white">
            <h3 class="block-title">Tạo hook</h3>
        </div>
        <div class="block-content block-content-full space-y-3">
            <form wire:submit="save">
                <div class="mb-4">
                    <label class="form-label">Ch<PERSON><PERSON> tài k<PERSON>n</label>
                    <select class="form-select @error('bank_account_id') is-invalid @enderror" wire:model='bank_account_id'>
                        <option>Chọn tài khoản</option>
                        @foreach ($accounts as $item)
                        <option value="{{ $item->id }}">{{ $item->bank->shortName }} - {{ $item->account_holder_name }} - {{ $item->account_no }}</option>
                        @endforeach
                    </select>
                    @error('bank_account_id')
                            <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label class="form-label">Loại</label>
                    <select class="form-select @error('hook_type') is-invalid @enderror" wire:model.live='hook_type'>
                        <option value="">Vui lòng chọn</option>
                        <option value="webhook">Webhook</option>
                        <option value="slack">Slack</option>
                        <option value="telegram">Telegram</option>
                        <option value="larksuite">Larksuite</option>
                        <option value="haravan">Haravan</option>
                        <option value="sapo" disabled>Sapo (Đang phát triển)</option>
                    </select>
                    @error('hook_type')
                            <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                @if ($this->hook_type == 'haravan')
                <p class="mb-0 mt-1">Tích hợp Haravan (<a href=''>Hướng dẫn</a>)</p>
                @endif
                </div>
                @if ($this->hook_type != 'haravan')
                <div class="mb-4">
                    <label class="form-label">Endpoint</label>
                    <input type="text" wire:model='endpoint' class="form-control @error('endpoint') is-invalid @enderror" placeholder="Endpoint">
                    @error('endpoint')
                        <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                @endif
                <div class="mb-4">
                    <label class="form-label">Token</label>
                    <div class="input-group @error('token') is-invalid @enderror">
                        <input type="text" wire:model='token' class="form-control @error('token') is-invalid @enderror"
                            placeholder="Token dùng để xác minh request được gửi từ hệ thống">
                            @if ($this->hook_type != 'haravan')
                        <button type="button" class="btn btn-primary" wire:click='generateToken'>Tạo Ngẫu nhiên
                            Token</button>
                            @endif
                    </div>
                    @error('token')
                        <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                @if ($this->hook_type == 'haravan')
                <div class="mb-4">
                    <label class="form-label">Cú pháp định danh đơn hàng</label>
                    <div class="input-group @error('syntax') is-invalid @enderror">
                        <input type="text" wire:model='syntax' class="form-control @error('syntax') is-invalid @enderror"
                            placeholder="">
                    </div>
                    @error('syntax')
                        <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                @endif


                <div class="mb-4">
                    <label class="form-label">Phương thức</label>
                    <select class="form-select @error('method') is-invalid @enderror" wire:model='method'>
                        <option value="post">POST</option>
                    </select>
                    @error('method')
                        <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <label class="form-label">Sự kiện</label>
                    <select class="form-select @error('event') is-invalid @enderror" wire:model='event'>
                        <option value="all">Tất cả</option>
                        <option value="in">Tiền vào</option>
                        <option value="out">Tiền ra</option>
                    </select>
                    @error('event')
                        <div id="val-username-error" class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" wire:model="skip_if_no_code" name="skip_if_no_code"
                            id="skip_if_no_code" />
                        <label class="form-check-label" for="skip_if_no_code">
                            Bỏ qua nếu không có mã thanh toán
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">Thêm Hook</button>
                <div wire:loading>
                    Đang kiểm tra thông tin...
                </div>
            </form>
        </div>
    </div>
    <div wire:loading>
        <div style="display: flex; justify-content: center; align-items: center; background-color: black; position: fixed; top: 0px; left: 0px; z-index: 9999; width: 100%; height: 100%; opacity: .75;">
        <div class="loader"></div> 
        </div>
    </div>
</div>