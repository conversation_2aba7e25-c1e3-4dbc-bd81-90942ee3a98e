<div class="content">
    <div class="block block-rounded">
        <div class="block-header block-header-default bg-primary text-white">
            <h3 class="block-title">Thêm tài <PERSON><PERSON><PERSON> (VA) cho BIDV</h3>
        </div>
        <div class="block-content block-content-full space-y-3">
            <form wire:submit="save">
                <div class="mb-2">
                    <label class="form-label" for="example-text-input">Số VA <span class="text-danger">*</span></label>
                    <p class="mb-0 text-muted">Số VA chỉ chứa các chữ số và chữ cái in hoa, giới hạn độ dài tối đa 5 ký tự.</p>
                    <div class="input-group">
                        <span class="input-group-text">
                            {{ $first_va_number }}
                        </span>
                        <input type="text" class="form-control @error('va_number') is-invalid @enderror" wire:model="va_number">
                        <button type="button" class="btn btn-info" wire:click="generateVaNumber">
                            <i class="fa fa-arrows-rotate"></i> Tạo ngẫu nhiên
                        </button>
                        @error('va_number')
                    <div id="val-username-error" class="invalid-feedback animated fadeIn">{{ $message }}</div>
                    @enderror
                    </div>
                    
                </div>
                <div class="mb-2">
                    <label class="form-label" for="example-text-input">Tên hiển thị <span class="text-danger">*</span></label>
                    <p class="mb-0 text-muted">Tên hiển thị chỉ chứa các chữ cái in hoa không dấu, chữ số, khoảng trắng và giới hạn 50 ký tự</p>
                    <input type="text" class="form-control @error('va_name') is-invalid @enderror"
                        wire:model="va_name" placeholder="">
                    @error('va_name')
                    <div id="val-username-error" class="invalid-feedback animated fadeIn">{{ $message }}</div>
                    @enderror
                </div>
                <a href="{{ route('detail.business.bidv', ['id' => $bank_id]) }}" class="btn btn-secondary">Quay lại</a>
                <button type="submit" class="btn btn-primary">Thêm</button>
                <div wire:loading wire:target="save">
                    Đang kiểm tra thông tin...
                </div>
            </form>
        </div>
    </div>
</div>