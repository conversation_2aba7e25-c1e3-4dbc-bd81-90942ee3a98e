<?php

namespace Database\Seeders;

use App\Models\BankAccount;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SeederEncryptPasswordBank extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banks = BankAccount::all();
        foreach ($banks as $bank) {
            $bank->password = $bank->password;
            $bank->save();
        }
    }
}
