<?php

namespace Database\Seeders;

use App\Models\Package;
use App\Models\User;
use App\Models\UserPackage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SeederPackageUser extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $packageFree = Package::where('price', 0)->first();
        foreach ($users as $user) {
            UserPackage::create(
                [
                    'user_id' => $user->id,
                    'package_id' => $packageFree->id,
                    'start_at' => now(),
                    'end_at' => now()->addMonth(),
                    'expire_at' => now()->addMonth(),
                ]
            );
        }
    }
}
