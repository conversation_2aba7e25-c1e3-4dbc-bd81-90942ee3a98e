<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('deposit_histories', function (Blueprint $table) {
            $table->string('bank_code')->nullable()->change();
            $table->integer('type')->default(1)->after('bank_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('deposit_histories', function (Blueprint $table) {
            $table->dropColumn(['type']);
        });
    }
};
