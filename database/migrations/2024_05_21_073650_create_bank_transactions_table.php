<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_transactions', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->uuid('bank_account_id');
            $table->string('transaction_id');
            $table->string('cus_tran_id');
            $table->timestamp('actual_at');
            $table->string('memo');
            $table->integer('amount');
            $table->integer('type')->default(1)->comment('1:in, 2:out');
            $table->integer('status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_transactions');
    }
};
