<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hooks', function (Blueprint $table) {
            $table->dropColumn(['channel']);
            $table->string('syntax')->after('endpoint')->nullable();
            $table->string('endpoint')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hooks', function (Blueprint $table) {
            $table->dropColumn(['syntax']);
            $table->string('channel')->after('hook_type');
        });
    }
};
