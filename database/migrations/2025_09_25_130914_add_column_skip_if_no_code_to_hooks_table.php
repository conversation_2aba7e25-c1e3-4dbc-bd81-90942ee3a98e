<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hooks', function (Blueprint $table) {
            $table->boolean('skip_if_no_code')->after('syntax')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hooks', function (Blueprint $table) {
            $table->dropColumn(['skip_if_no_code']);
        });
    }
};
