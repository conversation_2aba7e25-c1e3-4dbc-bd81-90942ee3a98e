<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_accounts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('bank_code');
            $table->integer('user_id');
            $table->string('username');
            $table->string('password');
            $table->string('account_no');
            $table->string('account_holder_name')->nullable();
            $table->string('last_status')->nullable();
            $table->string('api_token')->nullable();
            $table->string('browserToken')->nullable();
            $table->string('tranId')->nullable();
            $table->text('publicKey')->nullable();
            $table->text('privateKey')->nullable();
            $table->text('authorization')->nullable();
            $table->string('access_key')->nullable();
            $table->longText('cookies')->nullable();
            $table->longText('accounts')->nullable();
            $table->bigInteger('time_login')->default(0);
            $table->integer('status')->default(0);
            $table->string('sessionId')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_accounts');
    }
};
