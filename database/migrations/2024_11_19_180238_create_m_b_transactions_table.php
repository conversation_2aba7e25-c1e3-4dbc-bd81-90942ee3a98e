<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mb_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transactionid');
            $table->string('transactiontime');
            $table->string('referencenumber');
            $table->integer('amount');
            $table->text('content');
            $table->string('bankaccount');
            $table->string('transType');
            $table->string('valueDate')->nullable();
            $table->string('va')->nullable();
            $table->string('reciprocalAccount');
            $table->string('reciprocalBankCode')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mb_transactions');
    }
};
