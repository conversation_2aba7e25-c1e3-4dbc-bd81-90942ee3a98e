<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bidv_va_numbers', function (Blueprint $table) {
            $table->integer('type')->after('va_name')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bidv_va_numbers', function (Blueprint $table) {
            $table->dropColumn(['type']);
        });
    }
};
