<?php

use App\Http\Controllers\Api\BIDVController;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\HaravanController;
use App\Http\Controllers\MBController;
use Illuminate\Support\Facades\Route;

Route::get('/transactions/{token}', [ApiController::class, 'transactions']);
Route::get('/banks', [ApiController::class, 'banks']);
Route::post('/deposit/endpoint', [ApiController::class, 'depositEndpoint'])->name('depositEndpoint');
Route::group(['prefix' => 'haravan'], function () {
    Route::post('/transactions/status', [HaravanController::class, 'status'])->name('checkStatusHaravan');
});


//for mb bank
Route::post('/token_generate', [MBController::class, 'token_generate'])->name('token_generate');
Route::post('/transaction-sync', [MBController::class, 'transactionSync'])->name('transaction_sync')->middleware(['auth:sanctum']);


//for BIDV
Route::prefix('allpay-bidv')->group(function () {
    Route::post('/paybill', [BIDVController::class, 'paybill'])->name('bidv.paybill');
    Route::post('/getbill', [BIDVController::class, 'getbill'])->name('bidv.getbill');
});
