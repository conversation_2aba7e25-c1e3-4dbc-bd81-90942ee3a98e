/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class r{static initChartsBars(){Chart.defaults.color="#818d96",Chart.defaults.scale.grid.color="transparent",Chart.defaults.scale.grid.zeroLineColor="transparent",Chart.defaults.scale.beginAtZero=!0,Chart.defaults.elements.line.borderWidth=1,Chart.defaults.plugins.legend.labels.boxWidth=12;let r,o,a=document.getElementById("js-chartjs-analytics-bars");o={labels:["MON","TUE","WED","THU","FRI","SAT","SUN"],datasets:[{label:"This Week",fill:!0,backgroundColor:"rgba(103, 114, 229, .75)",borderColor:"rgba(103, 114, 229, 1)",pointBackgroundColor:"rgba(103, 114, 229, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(103, 114, 229, 1)",data:[500,750,650,570,582,480,680]},{label:"Last Week",fill:!0,backgroundColor:"rgba(108, 117, 125, .15)",borderColor:"rgba(108, 117, 125, .75)",pointBackgroundColor:"rgba(108, 117, 125, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(108, 117, 125, 1)",data:[352,530,541,521,410,395,460]}]},null!==a&&(r=new Chart(a,{type:"bar",data:o,options:{responsive:!0,maintainAspectRatio:!1}}))}static init(){this.initChartsBars()}}Dashmix.onLoad((()=>r.init()))}();