/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class t{static animationsToggle(){let t,i;document.querySelectorAll(".js-animation-section button").forEach((a=>{a.addEventListener("click",(s=>{t=a.dataset.animationClass,i=a.closest(".js-animation-section"),i.querySelector(".js-animation-preview").textContent=t;let n=i.querySelector(".js-animation-object");n.classList="",n.classList.add("js-animation-object"),n.classList.add("animated"),n.classList.add(t)}))}))}static init(){this.animationsToggle()}}Dashmix.onLoad((()=>t.init()))}();