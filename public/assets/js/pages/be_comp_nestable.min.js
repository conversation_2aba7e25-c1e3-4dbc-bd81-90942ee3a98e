/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class e{static nestable2(){jQuery(".js-nestable-connected-simple").add(".js-nestable-connected-icons").add(".js-nestable-connected-treeview").nestable({group:0}),jQuery(".js-nestable-simple").nestable({group:1}),jQuery(".js-nestable-icons").nestable({group:2}),jQuery(".js-nestable-treeview").nestable({group:3})}static init(){this.nestable2()}}Dashmix.onLoad((()=>e.init()))}();