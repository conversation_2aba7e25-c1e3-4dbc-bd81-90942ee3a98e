/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class i{static initValidation(){Dashmix.helpers("jq-validation"),jQuery(".js-validation-reminder").validate({rules:{"reminder-credential":{required:!0,minlength:3}},messages:{"reminder-credential":{required:"Please enter a valid credential"}}})}static init(){this.initValidation()}}Dashmix.onLoad((()=>i.init()))}();