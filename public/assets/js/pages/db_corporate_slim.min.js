/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class t{static initCorporateChartJS(){Chart.defaults.color="#7c7c7c",Chart.defaults.scale.grid.color="transparent",Chart.defaults.scale.grid.zeroLineColor="transparent",Chart.defaults.scale.display=!1,Chart.defaults.scale.beginAtZero=!0,Chart.defaults.elements.line.borderWidth=2,Chart.defaults.elements.point.radius=6,Chart.defaults.elements.point.hoverRadius=10,Chart.defaults.plugins.tooltip.radius=3,Chart.defaults.plugins.legend.display=!1;let t,e,a=document.getElementById("js-chartjs-corporate-slim-projects"),r=document.getElementById("js-chartjs-corporate-slim-tickets");null!==a&&(t=new Chart(a,{type:"line",data:{labels:["MON","TUE","WED","THU","FRI","SAT","SUN"],datasets:[{label:"This Week",fill:!0,backgroundColor:"rgba(73, 80, 87, .1)",borderColor:"rgba(73, 80, 87, .35)",pointBackgroundColor:"rgba(73, 80, 87, .5)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(73, 80, 87, .5)",data:[14,16,6,14,10,19,12]}]},options:{responsive:!0,maintainAspectRatio:!1,tension:.4,scales:{y:{suggestedMin:0,suggestedMax:22}},interaction:{intersect:!1},plugins:{tooltip:{callbacks:{label:function(t){return" "+t.parsed.y+" Sales"}}}}}})),null!==r&&(e=new Chart(r,{type:"line",data:{labels:["MON","TUE","WED","THU","FRI","SAT","SUN"],datasets:[{label:"This Week",fill:!0,backgroundColor:"rgba(130, 181, 75, .1)",borderColor:"rgba(130, 181, 75, .35)",pointBackgroundColor:"rgba(130, 181, 75, .5)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(130, 181, 75, .5)",data:[35,20,29,20,40,34,45]}]},options:{responsive:!0,maintainAspectRatio:!1,tension:.4,scales:{y:{suggestedMin:0,suggestedMax:50}},interaction:{intersect:!1},plugins:{tooltip:{callbacks:{label:function(t){return" "+t.parsed.y+" Tickets"}}}}}}))}static init(){this.initCorporateChartJS()}}Dashmix.onLoad((()=>t.init()))}();