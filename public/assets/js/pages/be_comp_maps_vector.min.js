/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";let i={map:"",backgroundColor:"#ffffff",regionStyle:{initial:{fill:"#1F75D5","fill-opacity":1,stroke:"none","stroke-width":0,"stroke-opacity":1},hover:{"fill-opacity":.8,cursor:"pointer"}}};class a{static initMapWorld(){i.map="world_mill_en",jQuery(".js-vector-map-world").vectorMap(i)}static initMapEurope(){i.map="europe_mill_en",jQuery(".js-vector-map-europe").vectorMap(i)}static initMapUsa(){i.map="us_aea_en",jQuery(".js-vector-map-usa").vectorMap(i)}static initMapIndia(){i.map="in_mill_en",jQuery(".js-vector-map-india").vectorMap(i)}static initMapChina(){i.map="cn_mill_en",jQuery(".js-vector-map-china").vectorMap(i)}static initMapAustralia(){i.map="au_mill_en",jQuery(".js-vector-map-australia").vectorMap(i)}static initMapSouthAfrica(){i.map="za_mill_en",jQuery(".js-vector-map-south-africa").vectorMap(i)}static initMapFrance(){i.map="fr_mill_en",jQuery(".js-vector-map-france").vectorMap(i)}static initMapGermany(){i.map="de_mill_en",jQuery(".js-vector-map-germany").vectorMap(i)}static init(){this.initMapWorld(),this.initMapEurope(),this.initMapUsa(),this.initMapIndia(),this.initMapChina(),this.initMapAustralia(),this.initMapSouthAfrica(),this.initMapFrance(),this.initMapGermany()}}Dashmix.onLoad((()=>a.init()))}();