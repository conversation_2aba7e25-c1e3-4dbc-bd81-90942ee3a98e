/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class i{static initValidation(){Dashmix.helpers("jq-validation"),jQuery(".js-validation-lock").validate({rules:{"lock-password":{required:!0,minlength:3}},messages:{"lock-password":{required:"Please enter your valid password"}}})}static init(){this.initValidation()}}Dashmix.onLoad((()=>i.init()))}();