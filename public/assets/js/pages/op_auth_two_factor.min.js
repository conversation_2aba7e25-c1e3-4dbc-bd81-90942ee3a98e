/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class e{static init2fa(){let e=document.getElementById("num1"),t=document.getElementById("num2"),u=document.getElementById("num3"),n=document.getElementById("num4"),i=document.getElementById("num5"),s=document.getElementById("num6");e.focus(),e.addEventListener("keyup",(()=>{this.isNumber(e.value)?t.focus():e.value=""})),t.addEventListener("keyup",(()=>{this.isNumber(t.value)?u.focus():t.value=""})),u.addEventListener("keyup",(()=>{this.isNumber(u.value)?n.focus():u.value=""})),n.addEventListener("keyup",(()=>{this.isNumber(n.value)?i.focus():n.value=""})),i.addEventListener("keyup",(()=>{this.isNumber(i.value)?s.focus():i.value=""})),s.addEventListener("keyup",(()=>{this.isNumber(s.value)?document.getElementById("form-2fa").submit():s.value=""}))}static isNumber(e){return!!["1","2","3","4","5","6","7","8","9"].includes(e)}static init(){this.init2fa()}}Dashmix.onLoad((()=>e.init()))}();