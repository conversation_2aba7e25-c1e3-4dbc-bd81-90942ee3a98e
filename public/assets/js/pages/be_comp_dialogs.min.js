/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class e{static sweetAlert2(){let e=Swal.mixin({buttonsStyling:!1,target:"#page-container",customClass:{confirmButton:"btn btn-primary m-1",cancelButton:"btn btn-danger m-1",input:"form-control"}}),t=document.querySelector(".js-swal-simple");t&&t.addEventListener("click",(t=>{e.fire("Hi, this is just a simple message!")}));let n=document.querySelector(".js-swal-success");n&&n.addEventListener("click",(t=>{e.fire("Success","Everything was updated perfectly!","success")}));let i=document.querySelector(".js-swal-info");i&&i.addEventListener("click",(t=>{e.fire("Info","Just an informational message!","info")}));let s=document.querySelector(".js-swal-warning");s&&s.addEventListener("click",(t=>{e.fire("Warning","Something needs your attention!","warning")}));let r=document.querySelector(".js-swal-error");r&&r.addEventListener("click",(t=>{e.fire("Oops...","Something went wrong!","error")}));let o=document.querySelector(".js-swal-question");o&&o.addEventListener("click",(t=>{e.fire("Question","Are you sure about that?","question")}));let c=document.querySelector(".js-swal-confirm");c&&c.addEventListener("click",(t=>{e.fire({title:"Are you sure?",text:"You will not be able to recover this imaginary file!",icon:"warning",showCancelButton:!0,customClass:{confirmButton:"btn btn-danger m-1",cancelButton:"btn btn-secondary m-1"},confirmButtonText:"Yes, delete it!",html:!1,preConfirm:e=>new Promise((e=>{setTimeout((()=>{e()}),50)}))}).then((t=>{t.value?e.fire("Deleted!","Your imaginary file has been deleted.","success"):"cancel"===t.dismiss&&e.fire("Cancelled","Your imaginary file is safe :)","error")}))}));let l=document.querySelector(".js-swal-custom-position");l&&l.addEventListener("click",(t=>{e.fire({position:"top-end",title:"Perfect!",text:"Nice Position!",icon:"success"})}))}static init(){this.sweetAlert2()}}Dashmix.onLoad((()=>e.init()))}();