/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class t{static initChartsBitcoin(){Chart.defaults.color="#818d96",Chart.defaults.scale.grid.color="transparent",Chart.defaults.scale.grid.zeroLineColor="transparent",Chart.defaults.scale.beginAtZero=!0,Chart.defaults.elements.point.radius=0,Chart.defaults.elements.point.hoverRadius=0,Chart.defaults.plugins.tooltip.radius=3,Chart.defaults.plugins.legend.display=!1;let t=document.getElementById("js-chartjs-bitcoin"),a=[];for(let t=0;t<30;t++)a[t]=29===t?"1 day ago":30-t+" days ago";null!==t&&new Chart(t,{type:"line",data:{labels:a,datasets:[{label:"Bitcoin Price",fill:!0,backgroundColor:"rgba(255,193,7,.25)",borderColor:"rgba(255,193,7,1)",pointBackgroundColor:"rgba(255,193,7,1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255,193,7,1)",data:[10500,10400,9500,8268,10218,8250,8707,9284,9718,9950,9879,10147,10883,11071,11332,11584,11878,13540,16501,16007,15142,14869,16762,17276,16808,16678,16771,12900,13100,14e3]}]},options:{responsive:!0,maintainAspectRatio:!1,tension:.4,scales:{y:{suggestedMin:6e3,suggestedMax:2e4}},interaction:{intersect:!1},plugins:{tooltip:{callbacks:{label:function(t){return" $"+t.parsed.y}}}}}})}static init(){this.initChartsBitcoin()}}Dashmix.onLoad((()=>t.init()))}();