/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class a{static initRating(){jQuery(".js-rating").each(((a,t)=>{let e=jQuery(t);e.raty({score:e.data("score")||0,number:e.data("number")||5,cancel:e.data("cancel")||!1,target:e.data("target")||!1,targetScore:e.data("target-score")||!1,precision:e.data("precision")||!1,cancelOff:e.data("cancel-off")||"fa fa-fw fa-times-circle text-danger",cancelOn:e.data("cancel-on")||"fa fa-fw fa-times-circle",starHalf:e.data("star-half")||"fa fa-fw fa-star-half text-warning",starOff:e.data("star-off")||"fa fa-fw fa-star text-muted",starOn:e.data("star-on")||"fa fa-fw fa-star text-warning",starType:"i",hints:["Just Bad!","Almost There!","It’s ok!","That’s nice!","Incredible!"],click:function(a,t){}})}))}static init(){this.initRating()}}Dashmix.onLoad((()=>a.init()))}();