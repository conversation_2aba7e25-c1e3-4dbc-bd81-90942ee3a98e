/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";let t,a,s,e,d,n,r,l,i,c,k;class o{static initTasks(){let o,p,j,b,u=this;a=jQuery(".js-tasks"),s=jQuery("#js-task-form"),e=jQuery("#js-task-input"),n=jQuery(".js-task-list"),r=jQuery(".js-task-list-starred"),l=jQuery(".js-task-list-completed"),i=jQuery(".js-task-badge"),c=jQuery(".js-task-badge-starred"),k=jQuery(".js-task-badge-completed"),t=10,this.badgesUpdate(),s.on("submit",(t=>{t.preventDefault(),d=e.prop("value"),d&&(u.taskAdd(d),e.prop("value","").focus())})),a.on("click",".js-task-status",(t=>{o=jQuery(t.currentTarget).closest(".js-task"),p=o.data("task-id"),o.data("task-completed")?u.taskSetActive(p):u.taskSetCompleted(p)})),a.on("click",".js-task-star",(t=>{j=jQuery(t.currentTarget).closest(".js-task"),b=j.data("task-id"),j.data("task-starred")?u.taskStarRemove(b):u.taskStarAdd(b)})),a.on("click",".js-task-remove",(t=>{j=jQuery(t.currentTarget).closest(".js-task"),b=j.data("task-id"),u.taskRemove(b)}))}static badgesUpdate(){i.text(n.children().length||""),c.text(r.children().length||""),k.text(l.children().length||"")}static taskAdd(a){n.prepend('\n      <div class="js-task block block-rounded mb-2 animated fadeIn" data-task-id="'.concat(t,'" data-task-completed="false" data-task-starred="false">\n        <table class="table table-borderless table-vcenter mb-0">\n          <tr>\n            <td class="text-center pe-0" style="width: 38px;">\n              <div class="js-task-status form-check">\n                <input type="checkbox" class="form-check-input" id="tasks-cb-id').concat(t,'" name="tasks-cb-id').concat(t,'">\n                <label class="form-check-label" for="tasks-cb-id').concat(t,'"></label>\n              </div>\n            </td>\n            <td class="js-task-content fw-semibold ps-0">\n              ').concat(jQuery("<span />").text(a).html(),'\n            </td>\n            <td class="text-end" style="width: 100px;">\n              <button type="button" class="js-task-star btn btn-sm btn-link text-warning">\n                <i class="far fa-star fa-fw"></i>\n              </button>\n              <button type="button" class="js-task-remove btn btn-sm btn-link text-danger">\n                <i class="fa fa-times fa-fw"></i>\n              </button>\n            </td>\n          </tr>\n        </table>\n      </div>\n    ')),this.badgesUpdate(),t++}static taskRemove(t){jQuery('.js-task[data-task-id="'+t+'"]').remove(),this.badgesUpdate()}static taskStarAdd(t){let a=jQuery('.js-task[data-task-id="'+t+'"]');a.length>0&&(a.data("task-starred",!0),a.find(".js-task-star > i").toggleClass("fa far"),a.data("task-completed")||a.prependTo(r),this.badgesUpdate())}static taskStarRemove(t){let a=jQuery('.js-task[data-task-id="'+t+'"]');a.length>0&&(a.data("task-starred",!1),a.find(".js-task-star > i").toggleClass("fa far"),a.data("task-completed")||a.prependTo(n),this.badgesUpdate())}static taskSetActive(t){let a=jQuery('.js-task[data-task-id="'+t+'"]');a.length>0&&(a.data("task-completed",!1),a.find(".table").toggleClass("bg-body"),a.find(".js-task-status > input").prop("checked",!1),a.find(".js-task-content > del").contents().unwrap(),a.data("task-starred")?a.prependTo(r):a.prependTo(n),this.badgesUpdate())}static taskSetCompleted(t){let a=jQuery('.js-task[data-task-id="'+t+'"]');a.length>0&&(a.data("task-completed",!0),a.find(".table").toggleClass("bg-body"),a.find(".js-task-status > input").prop("checked",!0),a.find(".js-task-content").wrapInner("<del></del>"),a.prependTo(l),this.badgesUpdate())}static init(){this.initTasks()}}Dashmix.onLoad((()=>o.init()))}();