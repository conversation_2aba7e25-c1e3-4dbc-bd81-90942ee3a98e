/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class a{static initValidationInstallation(){Dashmix.helpers("jq-validation"),jQuery(".js-validation-installation").validate({rules:{"install-db-name":{required:!0},"install-db-username":{required:!0},"install-db-password":{required:!0},"install-admin-email":{required:!0,emailWithDot:!0},"install-admin-password":{required:!0,minlength:5},"install-admin-password-confirm":{required:!0,equalTo:"#install-admin-password"}},messages:{"install-db-name":{required:"Please provide the name of your database"},"install-db-username":{required:"Please provide the username with access to your database"},"install-db-password":{required:"Please provide the password of your database user"},"install-admin-email":{required:"Please enter your email"},"install-admin-password":{required:"Please provide a password",minlength:"Your password must be at least 5 characters long"},"install-admin-password-confirm":{required:"Please provide a password",minlength:"Your password must be at least 5 characters long",equalTo:"Please enter the same password as above"}}})}static init(){this.initValidationInstallation()}}Dashmix.onLoad((()=>a.init()))}();