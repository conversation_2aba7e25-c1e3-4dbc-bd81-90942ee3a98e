/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";let e=["fs-sm","d-inline-block","fw-medium","animated","fadeIn","bg-body-light","border-3","px-3","py-2","mb-2","shadow-sm","mw-100"],t=["border-end","border-primary","rounded-start","text-start"],a=["border-start","border-dark","rounded-end"],d=["fs-sm","text-muted","animated","fadeIn","my-2"];class s{static initChat(){let e=this;document.querySelectorAll(".js-chat-form form").forEach((t=>{t.addEventListener("submit",(t=>{t.preventDefault();let a=t.currentTarget.querySelector(".js-chat-input");e.chatAddMessage(parseInt(a.dataset.targetChatId),a.value,"self",a)}))}))}static chatAddHeader(e,t,a){let s=document.querySelector('.js-chat-messages[data-chat-id="'+e+'"]');if(t&&null!==s){let e=document.createElement("div"),l=document.createElement("div");d.forEach((e=>{l.classList.add(e)})),"self"===a?(l.classList.add("text-end"),e.classList.add("ms-4")):e.classList.add("me-4"),l.innerText=t,e.appendChild(l),s.appendChild(e),s.scrollTop=s.scrollHeight}}static chatAddMessage(d,s,l,i){let c=document.querySelector('.js-chat-messages[data-chat-id="'+d+'"]');if(s&&null!==c){let d=document.createElement("div"),r=document.createElement("div");e.forEach((e=>{r.classList.add(e)})),"self"===l?(d.classList.add("text-end"),d.classList.add("ms-4"),t.forEach((e=>{r.classList.add(e)}))):(d.classList.add("me-4"),a.forEach((e=>{r.classList.add(e)}))),r.innerText=s,d.appendChild(r),c.appendChild(d),c.scrollTop=c.scrollHeight,null!==i&&(i.value="")}}static init(){this.initChat()}static addHeader(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";this.chatAddHeader(e,t,a)}static addMessage(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";this.chatAddMessage(e,t,a,null)}}Dashmix.onLoad((()=>{s.init(),window.Chat=s}))}();