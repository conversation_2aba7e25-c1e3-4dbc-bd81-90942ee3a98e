/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class t{static barsRandomize(){document.querySelectorAll(".js-bar-randomize").forEach((t=>{t.addEventListener("click",(e=>{t.closest(".block").querySelectorAll(".progress-bar").forEach((t=>{let e=Math.floor(91*Math.random()+10),a=t.querySelector("span");t.style.width=e+"%",a&&(a.textContent=e+"%")}))}))}))}static init(){this.barsRandomize()}}Dashmix.onLoad((()=>t.init()))}();