/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class e{static initImageCropper(){let e=document.getElementById("js-img-cropper");Cropper.setDefaults({aspectRatio:4/3,preview:".js-img-cropper-preview"});let t=new Cropper(e,{crop:function(e){}});document.querySelectorAll('[data-toggle="cropper"]').forEach((e=>{e.addEventListener("click",(o=>{let a=e.dataset.method||!1,r=e.dataset.option||!1,s={zoom:()=>{t.zoom(r)},setDragMode:()=>{t.setDragMode(r)},rotate:()=>{t.rotate(r)},scaleX:()=>{t.scaleX(r),e.dataset.option=-r},scaleY:()=>{t.scaleY(r),e.dataset.option=-r},setAspectRatio:()=>{t.setAspectRatio(r)},crop:()=>{t.crop()},clear:()=>{t.clear()}};s[a]&&s[a]()}))}))}static init(){this.initImageCropper()}}Dashmix.onLoad((()=>e.init()))}();