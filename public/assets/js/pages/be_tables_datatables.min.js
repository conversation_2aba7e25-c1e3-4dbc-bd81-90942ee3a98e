/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class a{static initDataTables(){jQuery.extend(!0,DataTable.ext.classes,{search:{input:"form-control"},length:{select:"form-select"}}),jQuery.extend(!0,DataTable.defaults,{language:{lengthMenu:"_MENU_",search:"_INPUT_",searchPlaceholder:"Search..",info:"Page <strong>_PAGE_</strong> of <strong>_PAGES_</strong>",paginate:{first:'<i class="fa fa-angle-double-left"></i>',previous:'<i class="fa fa-angle-left"></i>',next:'<i class="fa fa-angle-right"></i>',last:'<i class="fa fa-angle-double-right"></i>'}}}),jQuery.extend(!0,DataTable.Buttons.defaults,{dom:{button:{className:"btn btn-sm btn-primary"}}}),jQuery(".js-dataTable-full").DataTable({pagingType:"simple_numbers",layout:{topStart:{pageLength:{menu:[5,10,15,20]}}},pageLength:5,autoWidth:!1}),jQuery(".js-dataTable-buttons").DataTable({pagingType:"simple_numbers",layout:{topStart:{buttons:["copy","excel","csv","pdf","print"]}},pageLength:5,autoWidth:!1}),jQuery(".js-dataTable-full-pagination").DataTable({layout:{topStart:{pageLength:{menu:[5,10,15,20]}}},pageLength:5,autoWidth:!1}),jQuery(".js-dataTable-simple").DataTable({pagingType:"simple_numbers",pageLength:5,layout:{topStart:null,topEnd:null},autoWidth:!1}),jQuery(".js-dataTable-responsive").DataTable({pagingType:"simple_numbers",layout:{topStart:{pageLength:{menu:[5,10,15,20]}}},pageLength:5,autoWidth:!1,responsive:!0})}static init(){this.initDataTables()}}Dashmix.onLoad((()=>a.init()))}();