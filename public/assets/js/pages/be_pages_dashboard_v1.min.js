/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
!function(){"use strict";class a{static initChartsMain(){Chart.defaults.color="#818d96",Chart.defaults.scale.grid.color="transparent",Chart.defaults.scale.grid.zeroLineColor="transparent",Chart.defaults.scale.display=!1,Chart.defaults.scale.beginAtZero=!0,Chart.defaults.elements.point.radius=0,Chart.defaults.elements.point.hoverRadius=0,Chart.defaults.plugins.tooltip.radius=3,Chart.defaults.plugins.legend.labels.boxWidth=12;let a,o,r,t,e,l,d=document.getElementById("js-chartjs-dashboard-earnings");o={responsive:!0,maintainAspectRatio:!1,tension:.4,scales:{y:{suggestedMin:0,suggestedMax:260}},interaction:{intersect:!1},plugins:{tooltip:{callbacks:{label:function(a){return" "+a.parsed.y+" Sales"}}}}},r={labels:["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"],datasets:[{label:"This Year",fill:!0,backgroundColor:"rgba(6, 101, 208, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, 1)",data:[50,210,110,90,230,130,190,75,155,120,140,230]},{label:"Last Year",fill:!0,backgroundColor:"rgba(6, 101, 208, .2)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, .2)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, .2)",data:[210,150,90,220,150,216,143,150,240,230,136,150]}]},t={labels:["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"],datasets:[{label:"This Year",fill:!0,backgroundColor:"rgba(6, 101, 208, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, 1)",data:[50,210,110,90,230,130,190,75,155,120,140,230]},{label:"Last Year",fill:!0,backgroundColor:"rgba(6, 101, 208, .2)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, .2)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, .2)",data:[210,150,90,220,150,216,143,150,240,230,136,150]}]};let n=[];for(let a=0;a<30;a++)n[a]=29===a?"1 day ago":30-a+" days ago";e={labels:n,datasets:[{label:"This Month",fill:!0,backgroundColor:"rgba(6, 101, 208, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, 1)",data:[50,210,110,90,230,130,190,75,155,120,140,230,50,210,110,90,230,130,155,120,140,230,50,210,110,90,230,130,190,75]},{label:"Last Month",fill:!0,backgroundColor:"rgba(6, 101, 208, .2)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, .2)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, .2)",data:[210,150,90,220,150,216,143,150,136,150,210,150,90,220,150,216,240,230,136,150,210,150,90,220,150,216,143,150,240,230]}]},l={labels:["MON","TUE","WED","THU","FRI","SAT","SUN"],datasets:[{label:"This Week",fill:!0,backgroundColor:"rgba(6, 101, 208, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, 1)",data:[34,42,62,78,39,83,98]},{label:"Last Week",fill:!0,backgroundColor:"rgba(6, 101, 208, .2)",borderColor:"transparent",pointBackgroundColor:"rgba(6, 101, 208, .2)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, .2)",data:[130,95,125,160,187,110,143]}]},null!==d&&(a=new Chart(d,{type:"line",data:r,options:o})),document.querySelector('[data-toggle="dashboard-chart-set-week"]').addEventListener("click",(()=>{a.data.labels=l.labels,a.data.datasets[0]=l.datasets[0],a.data.datasets[1]=l.datasets[1],a.update()})),document.querySelector('[data-toggle="dashboard-chart-set-month"]').addEventListener("click",(()=>{a.data.labels=e.labels,a.data.datasets[0]=e.datasets[0],a.data.datasets[1]=e.datasets[1],a.update()})),document.querySelector('[data-toggle="dashboard-chart-set-year"]').addEventListener("click",(()=>{a.data.labels=t.labels,a.data.datasets[0]=t.datasets[0],a.data.datasets[1]=t.datasets[1],a.update()}))}static init(){this.initChartsMain()}}jQuery((()=>{a.init()}))}();