//
// Dashmix - UI Framework
// --------------------------------------------------

// Bootstrap functions
@import 'bootstrap/functions';

// User variables (your own variable overrides)
@import 'custom/variables';

// Custom Bootstrap variables overrides
@import 'dashmix/variables-bootstrap';

// Bootstrap variables
@import 'bootstrap/variables';

// Bootstrap maps
@import 'bootstrap/maps';

// Bootstrap mixins
@import 'bootstrap/mixins';

// Custom mixins and Bootstrap overrides
@import 'dashmix/mixins';

// Bootstrap Utilities
@import 'bootstrap/utilities';

// Custom utilities and Bootstrap overrides
@import 'dashmix/utilities';

// Layout & components
@import 'bootstrap/root';
@import 'bootstrap/reboot';
@import 'bootstrap/type';
@import 'bootstrap/images';
@import 'bootstrap/containers';
@import 'bootstrap/grid';
@import 'bootstrap/tables';
@import 'bootstrap/forms';
@import 'bootstrap/buttons';
@import 'bootstrap/transitions';
@import 'bootstrap/dropdown';
@import 'bootstrap/button-group';
@import 'bootstrap/nav';
@import 'bootstrap/navbar';
@import 'bootstrap/card';
@import 'bootstrap/accordion';
@import 'bootstrap/breadcrumb';
@import 'bootstrap/pagination';
@import 'bootstrap/badge';
@import 'bootstrap/alert';
@import 'bootstrap/progress';
@import 'bootstrap/list-group';
@import 'bootstrap/close';
@import 'bootstrap/toasts';
@import 'bootstrap/modal';
@import 'bootstrap/tooltip';
@import 'bootstrap/popover';
@import 'bootstrap/carousel';
@import 'bootstrap/spinners';
@import 'bootstrap/offcanvas';
@import 'bootstrap/placeholders';

// Bootstrap Helpers
@import 'bootstrap/helpers';

// Bootstrap Utilities
@import 'bootstrap/utilities/api';

// Custom variables
@import 'dashmix/variables';
@import 'dashmix/variables-themes';

// Extend Bootstrap styles and override the ones..
// ..we can't alter with the provided variables
@import "dashmix/fonts";
@import 'dashmix/reboot';
@import 'dashmix/type';
@import 'dashmix/grid';
@import 'dashmix/tables';
@import 'dashmix/forms';
@import 'dashmix/buttons';
@import 'dashmix/transitions';
@import 'dashmix/dropdown';
@import 'dashmix/nav';
@import 'dashmix/card';
@import 'dashmix/breadcrumb';
@import 'dashmix/pagination';
@import 'dashmix/modal';
@import 'dashmix/print';

// Custom layout
@import 'dashmix/layout';
@import 'dashmix/header';
@import 'dashmix/sidebar';
@import 'dashmix/side-overlay';
@import 'dashmix/layout-variations';
@import 'dashmix/hero';
@import 'dashmix/block';

// Custom components
@import 'dashmix/page-loader';
@import 'dashmix/nav-main';
@import 'dashmix/images';
@import 'dashmix/lists';
@import 'dashmix/item';
@import 'dashmix/overlay';
@import 'dashmix/timeline';
@import 'dashmix/ribbon';

// Helpers
@import 'dashmix/helpers';

// Core third party components
@import 'vendor/animate';
@import 'vendor/fontawesome/fontawesome';
@import 'vendor/fontawesome/regular';
@import 'vendor/fontawesome/solid';
@import 'vendor/fontawesome/brands';
@import 'vendor/simple-line-icons';
@import 'vendor/simplebar';

// Optional third party plugins (style overrides)
@import 'vendor/bootstrap-datepicker';
@import 'vendor/ckeditor';
@import 'vendor/dropzone';
@import 'vendor/datatables';
@import 'vendor/easy-pie-chart';
@import 'vendor/fullcalendar';
@import 'vendor/ion-range-slider';
@import 'vendor/jquery-sparkline';
@import 'vendor/jvector-map';
@import 'vendor/pw-strength';
@import 'vendor/select2';
@import 'vendor/simplemde';
@import 'vendor/slick';
@import 'vendor/nestable2';
@import 'vendor/flatpickr';

// RTL Support
@import 'dashmix/rtl-support';

// Dark mode
@import 'dashmix/dark-mode';

// User styles (your own styles/overrides)
@import 'custom/main';
