//
// Timeline
// --------------------------------------------------

.timeline {
  position: relative;
  padding: 1rem 0;
  @include list-unstyled();

  &::before {
    position: absolute;
    top: 0;
    left: 1.375rem;
    height: 100%;
    width: .25rem;
    background-color: $body-bg-dark;
    content: "";
    border-radius: $border-radius;
  }
}

.timeline-event {
  position: relative;

  &:not(:last-child) {
    margin-bottom: $space-base;
  }
}

.timeline-event-icon {
  position: absolute;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $white;
  border-radius: 1.5rem;
  border: .25rem solid $white;
  z-index: 5;
  box-shadow: 0 .375rem 1.5rem darken($light, 8%);

  &::before {
    position:absolute;
    top: 50%;
    right: -.625rem;
    width: 0;
    height: 0;
    border-top: .625rem solid transparent;
    border-bottom: .625rem solid transparent;
    border-left: .625rem solid $white;
    content: "";
    transform: translateY(-50%);
  }
}

.timeline-event-block {
  margin-left: 4rem;
  margin-bottom: 0 !important;
}

// Centered Timeline in larger screens
@include media-breakpoint-up(xl) {
  .timeline-centered {
    &::before {
      left: 50%;
      margin-left: -.125rem;
    }

    .timeline-event-icon {
      left: 50%;
      margin-left: -1.5rem;

      &::before {
        right: auto;
        left: -.625rem;
        border-right: .625rem solid $white;
        border-left: none;
      }
    }

    .timeline-event-time {
      position: absolute;
      display: inline-block;
      top: 1.5rem;
      left: 50%;
      padding: .5rem .5rem .5rem 2rem;
      width: auto;
      border-radius: $border-radius;
      background-color: $white;
      transform: translateY(-50%);
    }

    .timeline-event-block {
      width: 46%;
      margin-left: 0;
    }

    .timeline-event:not(:last-child) {
      margin-bottom: $space-base * 2;
    }

    &.timeline-alt .timeline-event:nth-child(even),
    .timeline-event.timeline-event-alt {
      .timeline-event-icon {
        left: 50%;
        margin-left: -1.5rem;

        &::before {
          right: -.625rem;
          left: auto;
          border-right: none;
          border-left: .625rem solid $white;
        }
      }

      .timeline-event-block {
        margin-left: auto;
      }

      .timeline-event-time {
        padding: .5rem 2rem .5rem .5rem;
        right: 50%;
        left: auto;
        text-align: right;
      }
    }
  }
}
