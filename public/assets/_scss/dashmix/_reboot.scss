//
// Reboot
// --------------------------------------------------

// Emphasis
b,
strong {
  font-weight: $font-weight-bold;
}

// Links
a {
  transition: color .12s ease-out;

  @include media-breakpoint-up(sm) {
    &.link-fx {
      position: relative;

      &::before {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        content: '';
        background-color: $link-color;
        visibility: hidden;
        transform: scaleX(0);
        transform-origin: 0 50%;
        transition: transform .2s ease-out;
      }
    }
  }

  &.link-fx:hover::before,
  &.link-fx:focus::before {
    visibility: visible;
    transform: scaleX(1);
  }
}

// Paragraphs
p {
  line-height: $paragraph-line-height;
}

.story p,
p.story {
  line-height: $paragraph-line-height;
  font-size: $font-size-lg;
  color: $gray-700;

  @include media-breakpoint-up(sm) {
    line-height: $paragraph-line-height-lg;
  }
}

.story {
  h2,
  h3,
  h4 {
    margin-top: 3rem;
  }
}
