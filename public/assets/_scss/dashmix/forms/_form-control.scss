//
// Textual form controls
//

.form-control {
  font-family: $font-family-base;

  // Alternative Style
  &.form-control-alt {
    border-color: $light;
    background-color: $light;
    transition: none;

    &:focus {
      border-color: $body-bg-dark;
      background-color: $body-bg-dark;
      box-shadow: none;
    }

    &.is-valid {
      border-color: $success-light;
      background-color: $success-light;

      &:focus {
        border-color: darken($success-light, 5%);
        background-color: darken($success-light, 5%);
      }
    }

    &.is-invalid {
      border-color: $danger-light;
      background-color: $danger-light;


      &:focus {
        border-color: darken($danger-light, 5%);
        background-color: darken($danger-light, 5%);
      }
    }
  }
}
