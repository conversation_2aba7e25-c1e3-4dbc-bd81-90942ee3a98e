//
// Theme
// --------------------------------------------------

@mixin color-theme(
  $primary,
  $primary-light,
  $primary-lighter,
  $primary-dark,
  $primary-darker,
  $body-bg,
  $body-bg-dark,
  $body-bg-light,
  $body-color,
  $body-color-dark,
  $body-color-light,
  $link-color,
  $link-hover-color,
  $input-btn-focus-color,
  $input-btn-focus-box-shadow,
  $input-bg,
  $input-color,
  $input-border-color,
  $input-focus-bg,
  $input-focus-color,
  $input-focus-border-color,
  $header-bg,
  $header-dark-bg,
  $sidebar-bg,
  $sidebar-dark-bg,
  $side-overlay-bg,
  $main-nav-link-icon-color,
  $main-nav-link-hover-bg,
  $main-nav-submenu-bg,
  $main-nav-link-icon-dark-color,
  $main-nav-link-dark-hover-bg,
  $main-nav-submenu-dark-bg
) {
  // Scaffolding
  body {
    color: $body-color;
    background-color: $light;
  }

  // Links
  a {
    color: $link-color;

    &.link-fx::before {
      background-color: $link-color;
    }

    &:hover {
      color: $link-hover-color;
    }
  }

  // Typography
  .content-heading {
    border-bottom-color: $body-bg-dark;
  }

  hr {
    border-top-color: $body-bg-dark;
  }

  // Contextual text colors
  @include text-emphasis-variant('.text-primary', $primary);
  @include text-emphasis-variant('.text-primary-dark', $primary-dark);
  @include text-emphasis-variant('.text-primary-darker', $primary-darker);
  @include text-emphasis-variant('.text-primary-light', $primary-light);
  @include text-emphasis-variant('.text-primary-lighter', $primary-lighter);

  @include text-emphasis-variant('.text-body-bg', $light);
  @include text-emphasis-variant('.text-body-bg-light', $body-bg-light);
  @include text-emphasis-variant('.text-body-bg-dark', $body-bg-dark);

  @include text-emphasis-variant('.text-body-color', $body-color);
  @include text-emphasis-variant('.text-body-color-dark', $body-color-dark);
  @include text-emphasis-variant('.text-body-color-light', $body-color-light);

  // Contextual dual text colors (for dark header/sidebar)
  @include text-emphasis-variant('.text-dual', $primary-dark);

  .page-header-dark #page-header,
  .sidebar-dark #sidebar {
    @include text-emphasis-variant('.text-dual', $body-color-light);
  }

  // Contextual background colors
  @include bg-variant('.bg-primary', $primary);
  @include bg-variant('.bg-primary-op', rgba($primary, .75));
  @include bg-variant('.bg-primary-dark', $primary-dark);
  @include bg-variant('.bg-primary-dark-op', rgba($primary-dark, .8));
  @include bg-variant('.bg-primary-darker', $primary-darker);
  @include bg-variant('.bg-primary-light', $primary-light);
  @include bg-variant('.bg-primary-lighter', $primary-lighter);
  @include bg-variant('.bg-body', $light);
  @include bg-variant('.bg-body-light', $body-bg-light);
  @include bg-variant('.bg-body-dark', $body-bg-dark);

  // Elements
  @include bg-variant('.bg-header-light', $header-bg);
  @include bg-variant('.bg-header-dark', $header-dark-bg);

  @include bg-variant('.bg-sidebar-light', $sidebar-bg);
  @include bg-variant('.bg-sidebar-dark', $sidebar-dark-bg);

  // Gradients
  @include bg-gradient-variant-linear('.bg-gd-primary', 135deg, $primary, lighten($primary, 15%));

  // Buttons
  .btn-link {
    color: $link-color;

    &:hover {
      color: $link-hover-color;
    }
  }

  .btn-primary {
    @include button-variant($primary, $primary);
  }

  .btn-outline-primary {
    @include button-outline-variant($primary);
  }

  .btn-alt-primary {
    @include button-variant(tint-color($primary, 75%), tint-color($primary, 75%), shade-color($primary, 40%), tint-color($primary, 50%), tint-color($primary, 50%), shade-color($primary, 60%));
  }
  
  .btn-alt-secondary {
    @include button-variant($body-bg, $body-bg, $body-color-dark, shade-color($body-bg, 10%), shade-color($body-bg, 10%), shade-color($body-color-dark, 15%));
  }

  // Used for buttons that adapt to light/dark header and sidebar variations
  .page-header-dark #page-header .btn-alt-secondary,
  .sidebar-dark #sidebar .btn-alt-secondary,
  #sidebar .bg-header-dark .content-header .btn-alt-secondary,
  .page-header-dark.page-header-glass:not(.page-header-scroll) #page-header .btn-alt-secondary {
    @include button-variant(tint-color($header-dark-bg, 15%), tint-color($header-dark-bg, 15%), $white, tint-color($header-dark-bg, 25%), tint-color($header-dark-bg, 25%), $white);
  }

  // Alerts
  .alert-primary {
    @include alert-variant($primary-lighter, $primary-lighter, $primary-dark);
  }

  // Progress Bars
  .progress-bar {
    background-color: $primary;
  }

  // Nav links
  .nav-link {
    &:hover,
    &:focus {
      color: $primary;
    }
  }

  // Nav Pills
  .nav-pills {
    .nav-link {
      color: $primary;
      
      &:hover,
      &:focus {
        background-color: $light;
      }
    }

    .nav-link.active,
    .show > .nav-link {
      background-color: $primary;
    }
  }

  // Nav Tabs
  .nav-tabs {
    border-bottom-color: $body-bg-dark;

    .nav-link {
      color: $primary;

      &:hover,
      &:focus {
        border-color: $body-bg-dark $body-bg-dark $body-bg-dark;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      border-color: $body-bg-dark $body-bg-dark $white;
    }
  }

  // Tabs block variation
  .nav-tabs-block {
    background-color: $body-bg-light;

    .nav-link {
      border-color: transparent;

      &:hover,
      &:focus {
        color: $primary;
        background-color: $light;
        border-color: transparent;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      color: $body-color;
      background-color: $white;
      border-color: transparent;
    }
  }

  // Tabs block alternative variation
  .nav-tabs-alt {
    border-bottom-color: $body-bg-dark;

    .nav-link {
      background-color: transparent;
      border-color: transparent;

      &:hover,
      &:focus {
        color: $primary;
        background-color: transparent;
        border-color: transparent;
        box-shadow: inset 0 -3px $primary;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      color: $body-color;
      background-color: transparent;
      border-color: transparent;
      box-shadow: inset 0 -3px $primary;
    }
  }

  // Various Items Navigation
  .nav-items {
    a {
      border-bottom-color: $light;

      &:hover {
        background-color: $body-bg-light;
      }

      &:active {
        background-color: $light;
      }
    }

    > li:last-child > a {
      border-bottom: none;
    }
  }

  // Cards
  .card {
    &.card-borderless {
      box-shadow: 0 1px 2px rgba(darken($body-bg-dark, 2.5%), .5), 0 1px 2px rgba(darken($body-bg-dark, 2.5%), .5);
    }

    > .card-header:not(.bg-transparent),
    > .card-footer:not(.bg-transparent) {
      background-color: $body-bg-light;
    }

    > .card-header:not(.border-bottom-0),
    > .card-footer:not(.border-top-0) {
      border-color: $body-bg-dark;
    }

    &:not(.card-borderless) {
      &,
      > .card-header {
        border-color: $body-bg-dark;
      }
    }
  }

  // Pagination
  .page-item {
    &.active .page-link {
      background-color: $primary;
      border-color: $primary;
    }
  }

  .page-link {
    color: $body-color;
    background-color: $light;
    border-color: $light;

    &:hover {
      color: $body-color;
      background-color: darken($body-bg-dark, 10%);
      border-color: darken($body-bg-dark, 10%);
    }

    &:focus {
      background-color: $body-bg-dark;
      border-color: $body-bg-dark;
    }
  }

  // List Group
  .list-group-item-action {
    color: $body-color;

    &:hover,
    &:focus {
      color: $body-color;
      background-color: $body-bg-light;
    }

    &:active {
      color: $body-color;
      background-color: $body-bg-dark;
    }
  }

  .list-group-item {
    border-color: $body-bg-dark;

    &.active {
      color: $white;
      background-color: $primary;
      border-color: $primary;
    }
  }

  // Popovers
  .popover {
    border-color: $body-bg-dark;
  }

  .bs-popover-top {
    .popover-arrow::before {
      border-top-color: fade-in($body-bg-dark, .05);
    }

    .popover-arrow::after {
      border-top-color: $white;
    }
  }

  .bs-popover-end {
    .popover-arrow::before {
      border-right-color: fade-in($body-bg-dark, .05);
    }

    .popover-arrow::after {
      border-right-color: $white;
    }
  }

  .bs-popover-bottom {
    .popover-arrow::before {
      border-bottom-color: fade-in($body-bg-dark, .05);
    }

    .popover-arrow::after {
      border-bottom-color: $white;
    }
  }

  .bs-popover-start {
    .popover-arrow::before {
      border-left-color: fade-in($body-bg-dark, .05);
    }

    .popover-arrow::after {
      border-left-color: $white;
    }
  }

  .bs-popover-auto {
    &[x-placement^="top"] {
      @extend .bs-popover-top;
    }
    &[x-placement^="right"] {
      @extend .bs-popover-end;
    }
    &[x-placement^="bottom"] {
      @extend .bs-popover-bottom;
    }
    &[x-placement^="left"] {
      @extend .bs-popover-start;
    }
  }

  // Modals
  .modal-header {
    border-bottom-color: $body-bg-dark;
  }

  .modal-footer {
    border-top-color: $body-bg-dark;
  }

  // Dropdowns
  .dropdown-menu {
    border-color: $body-bg-dark;
  }

  .dropdown-divider {
    border-top-color: $light;
  }

  .dropdown-item {
    color: $body-color;

    &:hover,
    &:focus {
      color: $body-color-dark;
      background-color: $light;
    }

    &.active,
    &:active {
      color: $white;
      background-color: $primary;
    }
  }

  // Tables
  .table {
    --#{$variable-prefix}table-striped-bg: #{lighten($body-bg, 1.5%)};
    --#{$variable-prefix}table-active-bg: #{$body-bg};
    --#{$variable-prefix}table-hover-bg: #{darken($body-bg, 1.5%)};
    
    border-color: $body-bg-dark;
  }

  .table > :not(:last-child) > :last-child > * {
    border-bottom-color: $body-bg-dark;
  }

  @include table-variant("primary", $primary-lighter);

  // Forms
  .form-control,
  .form-select {
    color: $input-color;
    background-color: $input-bg;
    border-color: $input-border-color;

    &:focus {
      color: $input-focus-color;
      background-color: $input-focus-bg;
      border-color: $input-focus-border-color;
      box-shadow: $input-btn-focus-box-shadow;
    }

    &:disabled {
      background-color: $input-disabled-bg;
    }
  }

  .form-select {
    &:focus::-ms-value {
      color: $input-color;
      background-color: $input-bg;
    }
  }

  .form-control.form-control-alt,
  .form-select.form-control-alt {
    border-color: $light;
    background-color: $light;

    &:focus {
      border-color: $body-bg-dark;
      background-color: $body-bg-dark;
      box-shadow: none;
    }
  }

  @include form-validation-state("valid", $success, $form-feedback-icon-valid);
  @include form-validation-state("invalid", $danger, $form-feedback-icon-invalid);

  .input-group-text {
    color: $input-color;
    background-color: $light;
    border-color: $input-border-color;
  }

  .input-group-text.input-group-text-alt {
    background-color: $body-bg-dark;
    border-color: $body-bg-dark;
  }

  .form-check-input {
    border-color: darken($body-bg, 10%);

    &:focus {
      border-color: $primary;
      box-shadow: 0 0 0 0.25rem rgba($primary, .25);
    }

    &:checked {
      background-color: $primary;
      border-color: $primary;
    }
  }

  .form-switch .form-check-input {
    background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{darken($body-bg, 10%)}'/></svg>"));

    &:focus {
      background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$primary}'/></svg>"));
    }

    &:checked {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
    }
  }

  .form-block {
    .form-check-label {
      border-color: $body-bg-dark;
  
      &:hover {
        border-color: darken($body-bg-dark, 5%);
      }
  
      &::before {
        background-color: $primary;
      }
    }
  
    .form-check-input:checked ~ .form-check-label {
      border-color: $primary;
    }
  
    .form-check-input:focus ~ .form-check-label {
      border-color: $primary;
      box-shadow: 0 0 0 0.25rem rgba($primary, .25);
    }
  
    .form-check-input:disabled:not([checked]) + .form-check-label:hover,
    .form-check-input[readonly]:not([checked]) + .form-check-label:hover { 
      border-color: $body-bg-dark;
    }
  }

  // Border
  .border {
    border-color: $body-bg-dark !important;
  }
  .border-top {
    border-top-color: $body-bg-dark !important;
  }
  .border-end {
    border-right-color: $body-bg-dark !important;
  }
  .border-bottom {
    border-bottom-color: $body-bg-dark !important;
  }
  .border-start {
    border-left-color: $body-bg-dark !important;
  }

  @each $color, $value in $theme-colors {
    .border-#{$color} {
      border-color: $value !important;
    }
  }

  .border-primary {
    border-color: $primary !important;
  }
  .border-white {
    border-color: $white !important;
  }
  .border-white-op {
    border-color: rgba($white, .1) !important;
  }
  .border-black-op {
    border-color: rgba($black, .1) !important;
  }

  // Main Structure
  #page-header {
    background-color: $header-bg;
  }
  #sidebar {
    background-color: $sidebar-bg;
  }
  #side-overlay {
    background-color: $side-overlay-bg;
  }

  // Layout/Style variations based on #page-container classes
  #page-container {
    background-color: $body-bg;
    
    // Page header
    &.page-header-dark #page-header {
      color: darken($body-color-light, 8%);
      background-color: $header-dark-bg;
    }

    &.page-header-glass {
      #page-header {
        background-color: transparent;
      }

      &.page-header-fixed {
        &.page-header-scroll #page-header {
          background-color: rgba($header-bg, .9);
        }

        &.page-header-scroll.page-header-dark #page-header {
          background-color: rgba($header-dark-bg, .9);
        }
      }
    }

    // Sidebar and Side Overlay
    &.sidebar-dark #sidebar {
      color: $body-color-light;
      background-color: $sidebar-dark-bg;
    }
  }

  // Sidebar
  #sidebar.with-mini-nav {
    .sidebar-mini-nav {
      color: $body-color-light;
      background-color: lighten($sidebar-dark-bg, 3%);
    }
  }

  // Blocks
  .block {
    box-shadow: 0 1px 3px rgba(darken($body-bg-dark, 2.5%), .5), 0 1px 2px rgba(darken($body-bg-dark, 2.5%), .5);
  }
  
  .block-header-default {
    background-color: $body-bg-light;
  }

  // Block Variations
  .block {
    &.block-bordered {
      border-color: $body-bg-dark;
    }

    &.block-themed > .block-header {
      background-color: $primary;
    }
  }

  // Block Modes
  .block {
    &.block-mode-loading {
      &::after {
        color: $primary;
      }

      &.block-mode-loading-dark::after {
        background-color: $primary-dark;
      }
    }
  }

  // Block Links
  a.block {
    color: $body-color;

    &:hover {
      color: $body-color;
    }

    &.block-link-pop {
      &:hover {
        box-shadow: 0 .5rem 2rem darken($body-bg, 8%);
      }

      &:active {
        box-shadow: 0 .25rem .75rem darken($body-bg, 1%);
      }
    }

    &.block-link-shadow {
      &:hover {
        box-shadow: 0 0 2.25rem darken($body-bg, 8%);
      }

      &:active {
        box-shadow: 0 0 1.125rem darken($body-bg, 4%);
      }
    }
  }

  // Block Effects
  .block {
    &.block-fx-shadow {
      box-shadow: 0 0 2.25rem darken($body-bg, 8%);
    }

    &.block-fx-pop {
      box-shadow: 0 .5rem 2rem darken($body-bg, 8%);
    }
  }

  // Block Options
  .btn-block-option {
    color: $primary;

    &:hover {
      color: $primary-light;
    }

    @at-root {
      a#{&}:focus,
      .active > a#{&},
      .show > button#{&} {
        color: $primary-light;
      }
    }

    &:active {
      color: $primary-lighter;
    }
  }

  // Page Loader
  #page-loader {
    background-color: $primary;
  }

  // Main Navigation
  // Headings
  .nav-main-heading {
    color: lighten($body-color, 25%);
  }

  // Default links
  .nav-main-link {
    color: lighten($body-color, 5%);

    .nav-main-link-icon {
      color: $main-nav-link-icon-color;
    }

    &:hover,
    &.active {
      color: $black;
      background-color: $main-nav-link-hover-bg;
    }
  }

  // Sub menus
  .nav-main-submenu {
    background-color: $main-nav-submenu-bg;

    .nav-main-link {
      color: lighten($body-color, 20%);

      &:hover,
      &.active {
        color: darken($body-color, 7.5%);
        background-color: transparent;
      }
    }
  }

  // Active sub menu
  .nav-main-item.open {
    > .nav-main-link-submenu {
      color: $black;
      background-color: $main-nav-link-hover-bg;
    }
  }

  .nav-main-submenu .nav-main-item.open .nav-main-link {
    background-color: transparent;
  }

  // Nav Main Horizontal
  @include media-breakpoint-up(lg) {
    .nav-main-horizontal {
      // Menu variations
      &.nav-main-hover {
        .nav-main-item:hover {
          > .nav-main-link-submenu {
            color: $black;
            background-color: $main-nav-link-hover-bg;
          }
        }
      }
    }
  }

  // Dark Variation
  .nav-main-dark,
  .sidebar-dark #sidebar,
  .page-header-dark #page-header,
  .dark-mode #side-overlay,
  .dark-mode #main-container {
    // Headings
    .nav-main-heading {
      color: lighten($sidebar-dark-bg, 30%);
    }

    // Default links
    .nav-main-link {
      color: lighten($sidebar-dark-bg, 55%);

      > .nav-main-link-icon {
        color: $main-nav-link-icon-dark-color;
      }

      &:hover,
      &.active {
        color: $white;
        background-color: $main-nav-link-dark-hover-bg;
      }
    }

    // Sub menus
    .nav-main-submenu {
      background-color: $main-nav-submenu-dark-bg;

      .nav-main-link {
        color: lighten($sidebar-dark-bg, 45%);

        &:hover,
          &.active {
          color: $white;
          background-color: transparent;
        }
      }
    }

    // Active sub menu
    .nav-main-item.open {
      > .nav-main-link-submenu {
        color: $white;
        background-color: $main-nav-link-dark-hover-bg;
      }

      > .nav-main-submenu {
        background-color: $main-nav-submenu-dark-bg;
      }
    }

    .nav-main-submenu .nav-main-item.open .nav-main-link {
      background-color: transparent;
    }
  }

  @include media-breakpoint-up(lg) {
    .nav-main-dark.nav-main-horizontal,
    .sidebar-dark #sidebar .nav-main-horizontal,
    .page-header-dark #page-header .nav-main-horizontal,
    .dark-mode #main-container .nav-main-horizontal {
      .nav-main-heading {
        color: rgba($white, .5);
      }
  
      .nav-main-link {
        color: rgba($white, .75);
  
        > .nav-main-link-icon {
          color: rgba($white, .4);
        }
  
        &:hover,
        &.active {
          color: $white;
          background-color: darken($sidebar-dark-bg, 5%);
        }
      }
  
      .nav-main-item.open,
      .nav-main-item:hover {
        > .nav-main-link-submenu {
          color: $white;
          background-color: darken($sidebar-dark-bg, 5%);
        }
  
        > .nav-main-submenu {
          background-color: darken($sidebar-dark-bg, 5%);
        }
      }
  
      .nav-main-submenu .nav-main-item:hover .nav-main-link {
        background-color: transparent;
      }
    }

    .page-header-dark #page-header .nav-main-horizontal {
      .nav-main-link {
        &:hover,
        &.active {
          background-color: darken($header-dark-bg, 5%);
        }
      }
  
      .nav-main-item.open,
      .nav-main-item:hover {
        > .nav-main-link-submenu {
          background-color: darken($header-dark-bg, 5%);
        }
  
        > .nav-main-submenu {
          background-color: darken($header-dark-bg, 5%);
        }
      }
  
      .nav-main-submenu .nav-main-item:hover .nav-main-link {
        background-color: transparent;
      }
    }
  }

  // Various Items Navigation
  .nav-items {
    a {
      border-bottom-color: $light;

      &:hover {
        background-color: $body-bg-light;
      }
    }
  }
  
  // Sidebar Mini Nav
  .mini-nav-item {
    color: darken($body-bg-dark, 15%);

    &.active {
      background-color: lighten($sidebar-dark-bg, 6%);
      color: $white;
    }

    &:hover {
      color: $white;
      background-color: lighten($sidebar-dark-bg, 6%);
    }

    &:active {
      color: darken($body-bg-dark, 15%);
    }
  }

  // Activity
  .list-activity {
    > li {
      border-bottom-color: $light;
    }
  }

  // Timeline
  .timeline-event-icon {
    box-shadow: 0 .375rem 1.5rem darken($body-bg, 8%);
  }

  // Ribbons
  .ribbon-light {
    @include ribbon-variation($body-bg-dark, $body-color);
  }

  .ribbon-primary {
    @include ribbon-variation($primary, $white);
  }

  // Bootstrap Datepicker
  .datepicker table tr td.active:hover,
  .datepicker table tr td.active:hover:hover,
  .datepicker table tr td.active.disabled:hover,
  .datepicker table tr td.active.disabled:hover:hover,
  .datepicker table tr td.active:focus,
  .datepicker table tr td.active:hover:focus,
  .datepicker table tr td.active.disabled:focus,
  .datepicker table tr td.active.disabled:hover:focus,
  .datepicker table tr td.active:active,
  .datepicker table tr td.active:hover:active,
  .datepicker table tr td.active.disabled:active,
  .datepicker table tr td.active.disabled:hover:active,
  .datepicker table tr td.active.active,
  .datepicker table tr td.active:hover.active,
  .datepicker table tr td.active.disabled.active,
  .datepicker table tr td.active.disabled:hover.active,
  .open .dropdown-toggle.datepicker table tr td.active,
  .open .dropdown-toggle.datepicker table tr td.active:hover,
  .open .dropdown-toggle.datepicker table tr td.active.disabled,
  .open .dropdown-toggle.datepicker table tr td.active.disabled:hover,
  .datepicker table tr td span.active:hover,
  .datepicker table tr td span.active:hover:hover,
  .datepicker table tr td span.active.disabled:hover,
  .datepicker table tr td span.active.disabled:hover:hover,
  .datepicker table tr td span.active:focus,
  .datepicker table tr td span.active:hover:focus,
  .datepicker table tr td span.active.disabled:focus,
  .datepicker table tr td span.active.disabled:hover:focus,
  .datepicker table tr td span.active:active,
  .datepicker table tr td span.active:hover:active,
  .datepicker table tr td span.active.disabled:active,
  .datepicker table tr td span.active.disabled:hover:active,
  .datepicker table tr td span.active.active,
  .datepicker table tr td span.active:hover.active,
  .datepicker table tr td span.active.disabled.active,
  .datepicker table tr td span.active.disabled:hover.active,
  .open .dropdown-toggle.datepicker table tr td span.active,
  .open .dropdown-toggle.datepicker table tr td span.active:hover,
  .open .dropdown-toggle.datepicker table tr td span.active.disabled,
  .open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
    background-color: $primary;
    border-color: $primary;
  }

  // CKEditor
  .cke_chrome,
  .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
  .ck.ck-toolbar {
    border-color: $body-bg-dark !important;
  }

  .cke_top,
  .ck.ck-toolbar {
    border-bottom-color: $body-bg-dark !important;
    background: $body-bg-light !important;
  }

  .ck.ck-toolbar .ck.ck-toolbar__separator {
    background: $body-bg-dark !important;
  }

  .cke_bottom {
    border-top-color: $body-bg-dark !important;
    background: $body-bg-light !important;
  }

  // DropzoneJS
  .dropzone {
    background-color: $body-bg-light;
    border-color: $input-border-color;

    .dz-message {
      color: $body-color;
    }

    &:hover {
      background-color: $white;
      border-color: $primary;

      .dz-message {
        color: $primary;
      }
    }
  }

  // FullCalendar
  .fc.fc-theme-standard {
    a {
      color: $body-color;
    }

    .fc-button-primary {
      color: $body-color;
      background-color: $body-bg-dark;
      border-color: $body-bg-dark;

      &:not(:disabled):hover {
        color: $body-color;
        background-color: $light;
        border-color: $light;
      }

      &:not(:disabled).fc-button-active,
      &:not(:disabled):active {
        color: $body-color;
        background-color: $body-bg-light;
        border-color: $body-bg-light;
      }

      &:focus,
      &:not(:disabled).fc-button-active:focus,
      &:not(:disabled):active:focus {
        box-shadow: 0 0 0 .2rem rgba($primary, .4)
      }
    }

    th,
    td,
    .fc-scrollgrid,
    .fc-list {
      border-color: $body-bg-dark;
    }

    .fc-h-event {
      background-color: $primary;
      border: $primary;
    }

    .fc-col-header-cell,
    .fc-list-day-cushion {
      background-color: $body-bg-light;
    }
  }

  // Ion Range Slider
  .irs.irs--round {
    .irs-min,
    .irs-max,
    .irs-line,
    .irs-grid-pol {
      background: $light;
    }

    .irs-handle {
      border-color: $primary;
    }

    .irs-from:before,
      .irs-to:before,
      .irs-single:before {
      border-top-color: $primary;
    }

    .irs-bar,
    .irs-from,
    .irs-to,
    .irs-single {
      background: $primary;
    }
  }

  // Select2
  .select2-container--default {
    .select2-selection--single {
      border-color: $input-border-color;
    }

    .select2-selection--multiple {
      border-color: $input-border-color;
    }

    &.select2-container--focus .select2-selection--multiple,
    &.select2-container--focus .select2-selection--single,
    &.select2-container--open .select2-selection--multiple,
    &.select2-container--open .select2-selection--single {
      border-color: $input-focus-border-color;
      box-shadow: $input-btn-focus-box-shadow;
    }

    .select2-selection--multiple {
      .select2-selection__choice {
        background-color: $primary;
      }
    }

    .select2-search--dropdown .select2-search__field {
      border-color: $input-border-color;
    }

    .select2-results__option--highlighted[aria-selected] {
      background-color: $primary;
    }

    .select2-dropdown .select2-search__field:focus {
      border-color: $input-focus-border-color;
      box-shadow: $input-btn-focus-box-shadow;
    }
  }

  // Simple Bar
  .simplebar-scrollbar::before {
    background: rgba(darken($primary-darker, 10%), .75);
  }

  // Slick
  .slick-slider {
    .slick-prev,
    .slick-next {
      &::before {
        color: $primary-dark;
      }
    }
  }

  // Simple MDE
  .editor-toolbar {
    border-color: $body-bg-dark;
    background-color: $body-bg-light;
  }

  .CodeMirror {
    border-color: $body-bg-dark;
  }

  // Nestable2
  .dd-handle {
    color: $body-color;
    background: $body-bg-light;
    border-color: $body-bg-dark;

    &:hover {
      color: $body-color-dark;
    }
  }

  .dd-empty,
  .dd-placeholder {
    border-color: $primary-darker;
    background: $primary-lighter;
  }

  // Flatpickr
  .flatpickr-day.selected,
  .flatpickr-day.startRange,
  .flatpickr-day.endRange,
  .flatpickr-day.selected.inRange,
  .flatpickr-day.startRange.inRange,
  .flatpickr-day.endRange.inRange,
  .flatpickr-day.selected:focus,
  .flatpickr-day.startRange:focus,
  .flatpickr-day.endRange:focus,
  .flatpickr-day.selected:hover,
  .flatpickr-day.startRange:hover,
  .flatpickr-day.endRange:hover,
  .flatpickr-day.selected.prevMonthDay,
  .flatpickr-day.startRange.prevMonthDay,
  .flatpickr-day.endRange.prevMonthDay,
  .flatpickr-day.selected.nextMonthDay,
  .flatpickr-day.startRange.nextMonthDay,
  .flatpickr-day.endRange.nextMonthDay {
    border-color: $primary;
    background: $primary;
  }

  .flatpickr-months .flatpickr-prev-month:hover svg,
  .flatpickr-months .flatpickr-next-month:hover svg {
    fill: $primary;
  }

  // jVectorMap
  .jvectormap-tip {
    background: $primary-dark;
  }
  
  .jvectormap-zoomin,
  .jvectormap-zoomout,
  .jvectormap-goback {
    background: $primary-dark;
  }

  // DataTables
  table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
    box-shadow: inset 0 0 0 9999px lighten($body-bg, 1.5%);
  }
}
