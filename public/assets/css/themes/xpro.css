/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
body {
  color: #343a40;
  background-color: #edf0f7;
}

a {
  color: #4954cb;
}
a.link-fx::before {
  background-color: #4954cb;
}
a:hover {
  color: #272f87;
}

.content-heading {
  border-bottom-color: #dbe0e7;
}

hr {
  border-top-color: #dbe0e7;
}

.text-primary {
  color: #4954cb !important;
}

a.text-primary.link-fx::before {
  background-color: #4954cb !important;
}
a.text-primary:hover, a.text-primary:focus {
  color: #323daf !important;
}

.text-primary-dark {
  color: #394263 !important;
}

a.text-primary-dark.link-fx::before {
  background-color: #394263 !important;
}
a.text-primary-dark:hover, a.text-primary-dark:focus {
  color: #262c43 !important;
}

.text-primary-darker {
  color: #282e45 !important;
}

a.text-primary-darker.link-fx::before {
  background-color: #282e45 !important;
}
a.text-primary-darker:hover, a.text-primary-darker:focus {
  color: #151825 !important;
}

.text-primary-light {
  color: #858cdc !important;
}

a.text-primary-light.link-fx::before {
  background-color: #858cdc !important;
}
a.text-primary-light:hover, a.text-primary-light:focus {
  color: #5d67d1 !important;
}

.text-primary-lighter {
  color: #b0b5e8 !important;
}

a.text-primary-lighter.link-fx::before {
  background-color: #b0b5e8 !important;
}
a.text-primary-lighter:hover, a.text-primary-lighter:focus {
  color: #8890dd !important;
}

.text-body-bg {
  color: #edf0f7 !important;
}

a.text-body-bg.link-fx::before {
  background-color: #edf0f7 !important;
}
a.text-body-bg:hover, a.text-body-bg:focus {
  color: #cad3e7 !important;
}

.text-body-bg-light {
  color: #f3f5f7 !important;
}

a.text-body-bg-light.link-fx::before {
  background-color: #f3f5f7 !important;
}
a.text-body-bg-light:hover, a.text-body-bg-light:focus {
  color: #d5dbe3 !important;
}

.text-body-bg-dark {
  color: #dbe0e7 !important;
}

a.text-body-bg-dark.link-fx::before {
  background-color: #dbe0e7 !important;
}
a.text-body-bg-dark:hover, a.text-body-bg-dark:focus {
  color: #bcc6d2 !important;
}

.text-body-color {
  color: #343a40 !important;
}

a.text-body-color.link-fx::before {
  background-color: #343a40 !important;
}
a.text-body-color:hover, a.text-body-color:focus {
  color: #1d2124 !important;
}

.text-body-color-dark {
  color: #212529 !important;
}

a.text-body-color-dark.link-fx::before {
  background-color: #212529 !important;
}
a.text-body-color-dark:hover, a.text-body-color-dark:focus {
  color: #0a0c0d !important;
}

.text-body-color-light {
  color: #dbe0e7 !important;
}

a.text-body-color-light.link-fx::before {
  background-color: #dbe0e7 !important;
}
a.text-body-color-light:hover, a.text-body-color-light:focus {
  color: #bcc6d2 !important;
}

.text-dual {
  color: #394263 !important;
}

a.text-dual.link-fx::before {
  background-color: #394263 !important;
}
a.text-dual:hover, a.text-dual:focus {
  color: #262c43 !important;
}

.page-header-dark #page-header .text-dual,
.sidebar-dark #sidebar .text-dual {
  color: #dbe0e7 !important;
}
.page-header-dark #page-header a.text-dual.link-fx::before,
.sidebar-dark #sidebar a.text-dual.link-fx::before {
  background-color: #dbe0e7 !important;
}
.page-header-dark #page-header a.text-dual:hover, .page-header-dark #page-header a.text-dual:focus,
.sidebar-dark #sidebar a.text-dual:hover,
.sidebar-dark #sidebar a.text-dual:focus {
  color: #bcc6d2 !important;
}

.bg-primary {
  background-color: #4954cb !important;
}

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #323daf !important;
}

.bg-primary-op {
  background-color: rgba(73, 84, 203, 0.75) !important;
}

a.bg-primary-op:hover, a.bg-primary-op:focus,
button.bg-primary-op:hover,
button.bg-primary-op:focus {
  background-color: rgba(50, 61, 175, 0.75) !important;
}

.bg-primary-dark {
  background-color: #394263 !important;
}

a.bg-primary-dark:hover, a.bg-primary-dark:focus,
button.bg-primary-dark:hover,
button.bg-primary-dark:focus {
  background-color: #262c43 !important;
}

.bg-primary-dark-op {
  background-color: rgba(57, 66, 99, 0.8) !important;
}

a.bg-primary-dark-op:hover, a.bg-primary-dark-op:focus,
button.bg-primary-dark-op:hover,
button.bg-primary-dark-op:focus {
  background-color: rgba(38, 44, 67, 0.8) !important;
}

.bg-primary-darker {
  background-color: #282e45 !important;
}

a.bg-primary-darker:hover, a.bg-primary-darker:focus,
button.bg-primary-darker:hover,
button.bg-primary-darker:focus {
  background-color: #151825 !important;
}

.bg-primary-light {
  background-color: #858cdc !important;
}

a.bg-primary-light:hover, a.bg-primary-light:focus,
button.bg-primary-light:hover,
button.bg-primary-light:focus {
  background-color: #5d67d1 !important;
}

.bg-primary-lighter {
  background-color: #b0b5e8 !important;
}

a.bg-primary-lighter:hover, a.bg-primary-lighter:focus,
button.bg-primary-lighter:hover,
button.bg-primary-lighter:focus {
  background-color: #8890dd !important;
}

.bg-body {
  background-color: #edf0f7 !important;
}

a.bg-body:hover, a.bg-body:focus,
button.bg-body:hover,
button.bg-body:focus {
  background-color: #cad3e7 !important;
}

.bg-body-light {
  background-color: #f3f5f7 !important;
}

a.bg-body-light:hover, a.bg-body-light:focus,
button.bg-body-light:hover,
button.bg-body-light:focus {
  background-color: #d5dbe3 !important;
}

.bg-body-dark {
  background-color: #dbe0e7 !important;
}

a.bg-body-dark:hover, a.bg-body-dark:focus,
button.bg-body-dark:hover,
button.bg-body-dark:focus {
  background-color: #bcc6d2 !important;
}

.bg-header-light {
  background-color: #fff !important;
}

a.bg-header-light:hover, a.bg-header-light:focus,
button.bg-header-light:hover,
button.bg-header-light:focus {
  background-color: #e6e6e6 !important;
}

.bg-header-dark {
  background-color: #424cb7 !important;
}

a.bg-header-dark:hover, a.bg-header-dark:focus,
button.bg-header-dark:hover,
button.bg-header-dark:focus {
  background-color: #343c92 !important;
}

.bg-sidebar-light {
  background-color: #fff !important;
}

a.bg-sidebar-light:hover, a.bg-sidebar-light:focus,
button.bg-sidebar-light:hover,
button.bg-sidebar-light:focus {
  background-color: #e6e6e6 !important;
}

.bg-sidebar-dark {
  background-color: #3b3946 !important;
}

a.bg-sidebar-dark:hover, a.bg-sidebar-dark:focus,
button.bg-sidebar-dark:hover,
button.bg-sidebar-dark:focus {
  background-color: #23222a !important;
}

.bg-gd-primary {
  background: #4954cb linear-gradient(135deg, #4954cb 0%, #858cdc 100%) !important;
}

.btn-link {
  color: #4954cb;
}
.btn-link:hover {
  color: #272f87;
}

.btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #4954cb;
  --bs-btn-border-color: #4954cb;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #3a43a2;
  --bs-btn-hover-border-color: #373f98;
  --bs-btn-focus-shadow-rgb: 100, 110, 211;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #3a43a2;
  --bs-btn-active-border-color: #373f98;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #4954cb;
  --bs-btn-disabled-border-color: #4954cb;
}

.btn-outline-primary {
  --bs-btn-color: #4954cb;
  --bs-btn-border-color: #4954cb;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #4954cb;
  --bs-btn-hover-border-color: #4954cb;
  --bs-btn-focus-shadow-rgb: 73, 84, 203;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #4954cb;
  --bs-btn-active-border-color: #4954cb;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #4954cb;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #4954cb;
  --bs-gradient: none;
}

.btn-alt-primary {
  --bs-btn-color: #2c327a;
  --bs-btn-bg: #d2d4f2;
  --bs-btn-border-color: #d2d4f2;
  --bs-btn-hover-color: #1d2251;
  --bs-btn-hover-bg: #a4aae5;
  --bs-btn-hover-border-color: #a4aae5;
  --bs-btn-focus-shadow-rgb: 185, 188, 224;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #dbddf5;
  --bs-btn-active-border-color: #d7d8f3;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #d2d4f2;
  --bs-btn-disabled-border-color: #d2d4f2;
}

.btn-alt-secondary {
  --bs-btn-color: #212529;
  --bs-btn-bg: #eaedf1;
  --bs-btn-border-color: #eaedf1;
  --bs-btn-hover-color: #1c1f23;
  --bs-btn-hover-bg: #d3d5d9;
  --bs-btn-hover-border-color: #d3d5d9;
  --bs-btn-focus-shadow-rgb: 204, 207, 211;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #eef1f4;
  --bs-btn-active-border-color: #eceff2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #eaedf1;
  --bs-btn-disabled-border-color: #eaedf1;
}

.page-header-dark #page-header .btn-alt-secondary,
.sidebar-dark #sidebar .btn-alt-secondary,
#sidebar .bg-header-dark .content-header .btn-alt-secondary,
.page-header-dark.page-header-glass:not(.page-header-scroll) #page-header .btn-alt-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #5e67c2;
  --bs-btn-border-color: #5e67c2;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #7179c9;
  --bs-btn-hover-border-color: #7179c9;
  --bs-btn-focus-shadow-rgb: 118, 126, 203;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #4b529b;
  --bs-btn-active-border-color: #474d92;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #5e67c2;
  --bs-btn-disabled-border-color: #5e67c2;
}

.alert-primary {
  --bs-alert-color: #394263;
  --bs-alert-bg: #b0b5e8;
  --bs-alert-border-color: #b0b5e8;
  --bs-alert-link-color: #2e354f;
}
.alert-primary .alert-link {
  color: var(--bs-alert-link-color);
}

.progress-bar {
  background-color: #4954cb;
}

.nav-link:hover, .nav-link:focus {
  color: #4954cb;
}

.nav-pills .nav-link {
  color: #4954cb;
}
.nav-pills .nav-link:hover, .nav-pills .nav-link:focus {
  background-color: #edf0f7;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: #4954cb;
}

.nav-tabs {
  border-bottom-color: #dbe0e7;
}
.nav-tabs .nav-link {
  color: #4954cb;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #dbe0e7 #dbe0e7 #dbe0e7;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  border-color: #dbe0e7 #dbe0e7 #fff;
}

.nav-tabs-block {
  background-color: #f3f5f7;
}
.nav-tabs-block .nav-link {
  border-color: transparent;
}
.nav-tabs-block .nav-link:hover, .nav-tabs-block .nav-link:focus {
  color: #4954cb;
  background-color: #edf0f7;
  border-color: transparent;
}
.nav-tabs-block .nav-link.active,
.nav-tabs-block .nav-item.show .nav-link {
  color: #343a40;
  background-color: #fff;
  border-color: transparent;
}

.nav-tabs-alt {
  border-bottom-color: #dbe0e7;
}
.nav-tabs-alt .nav-link {
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs-alt .nav-link:hover, .nav-tabs-alt .nav-link:focus {
  color: #4954cb;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #4954cb;
}
.nav-tabs-alt .nav-link.active,
.nav-tabs-alt .nav-item.show .nav-link {
  color: #343a40;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #4954cb;
}

.nav-items a {
  border-bottom-color: #edf0f7;
}
.nav-items a:hover {
  background-color: #f3f5f7;
}
.nav-items a:active {
  background-color: #edf0f7;
}
.nav-items > li:last-child > a {
  border-bottom: none;
}

.card.card-borderless {
  box-shadow: 0 1px 2px rgba(211, 217, 226, 0.5), 0 1px 2px rgba(211, 217, 226, 0.5);
}
.card > .card-header:not(.bg-transparent),
.card > .card-footer:not(.bg-transparent) {
  background-color: #f3f5f7;
}
.card > .card-header:not(.border-bottom-0),
.card > .card-footer:not(.border-top-0) {
  border-color: #dbe0e7;
}
.card:not(.card-borderless),
.card:not(.card-borderless) > .card-header {
  border-color: #dbe0e7;
}

.page-item.active .page-link {
  background-color: #4954cb;
  border-color: #4954cb;
}

.page-link {
  color: #343a40;
  background-color: #edf0f7;
  border-color: #edf0f7;
}
.page-link:hover {
  color: #343a40;
  background-color: #bcc6d2;
  border-color: #bcc6d2;
}
.page-link:focus {
  background-color: #dbe0e7;
  border-color: #dbe0e7;
}

.list-group-item-action {
  color: #343a40;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  color: #343a40;
  background-color: #f3f5f7;
}
.list-group-item-action:active {
  color: #343a40;
  background-color: #dbe0e7;
}

.list-group-item {
  border-color: #dbe0e7;
}
.list-group-item.active {
  color: #fff;
  background-color: #4954cb;
  border-color: #4954cb;
}

.popover {
  border-color: #dbe0e7;
}

.bs-popover-top .popover-arrow::before, .bs-popover-auto[x-placement^=top] .popover-arrow::before {
  border-top-color: #dbe0e7;
}
.bs-popover-top .popover-arrow::after, .bs-popover-auto[x-placement^=top] .popover-arrow::after {
  border-top-color: #fff;
}

.bs-popover-end .popover-arrow::before, .bs-popover-auto[x-placement^=right] .popover-arrow::before {
  border-right-color: #dbe0e7;
}
.bs-popover-end .popover-arrow::after, .bs-popover-auto[x-placement^=right] .popover-arrow::after {
  border-right-color: #fff;
}

.bs-popover-bottom .popover-arrow::before, .bs-popover-auto[x-placement^=bottom] .popover-arrow::before {
  border-bottom-color: #dbe0e7;
}
.bs-popover-bottom .popover-arrow::after, .bs-popover-auto[x-placement^=bottom] .popover-arrow::after {
  border-bottom-color: #fff;
}

.bs-popover-start .popover-arrow::before, .bs-popover-auto[x-placement^=left] .popover-arrow::before {
  border-left-color: #dbe0e7;
}
.bs-popover-start .popover-arrow::after, .bs-popover-auto[x-placement^=left] .popover-arrow::after {
  border-left-color: #fff;
}

.modal-header {
  border-bottom-color: #dbe0e7;
}

.modal-footer {
  border-top-color: #dbe0e7;
}

.dropdown-menu {
  border-color: #dbe0e7;
}

.dropdown-divider {
  border-top-color: #edf0f7;
}

.dropdown-item {
  color: #343a40;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: #212529;
  background-color: #edf0f7;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  background-color: #4954cb;
}

.table {
  --bs-table-striped-bg: #eff1f4;
  --bs-table-active-bg: #eaedf1;
  --bs-table-hover-bg: #e5e9ee;
  border-color: #dbe0e7;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #dbe0e7;
}

.table-primary {
  --bs-table-color: #000;
  --bs-table-bg: #b0b5e8;
  --bs-table-border-color: #8d91ba;
  --bs-table-striped-bg: #a7acdc;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #9ea3d1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #a3a7d7;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.form-control,
.form-select {
  color: #343a40;
  background-color: #fff;
  border-color: #d2d8e1;
}
.form-control:focus,
.form-select:focus {
  color: #212529;
  background-color: #fff;
  border-color: #acb1e7;
  box-shadow: 0 0 0 0.25rem rgba(73, 84, 203, 0.25);
}
.form-control:disabled,
.form-select:disabled {
  background-color: var(--bs-secondary-bg);
}

.form-select:focus::-ms-value {
  color: #343a40;
  background-color: #fff;
}

.form-control.form-control-alt,
.form-select.form-control-alt {
  border-color: #edf0f7;
  background-color: #edf0f7;
}
.form-control.form-control-alt:focus,
.form-select.form-control-alt:focus {
  border-color: #dbe0e7;
  background-color: #dbe0e7;
  box-shadow: none;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #6f9c40;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(111, 156, 64, 0.9);
  border-radius: var(--bs-border-radius);
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #6f9c40;
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #6f9c40;
  box-shadow: 0 0 0 0.25rem rgba(111, 156, 64, 0.25);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #6f9c40;
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #6f9c40;
  box-shadow: 0 0 0 0.25rem rgba(111, 156, 64, 0.25);
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #6f9c40;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #6f9c40;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(111, 156, 64, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #6f9c40;
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #e04f1a;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(224, 79, 26, 0.9);
  border-radius: var(--bs-border-radius);
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #e04f1a;
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #e04f1a;
  box-shadow: 0 0 0 0.25rem rgba(224, 79, 26, 0.25);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #e04f1a;
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #e04f1a;
  box-shadow: 0 0 0 0.25rem rgba(224, 79, 26, 0.25);
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #e04f1a;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #e04f1a;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(224, 79, 26, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #e04f1a;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.input-group-text {
  color: #343a40;
  background-color: #edf0f7;
  border-color: #d2d8e1;
}

.input-group-text.input-group-text-alt {
  background-color: #dbe0e7;
  border-color: #dbe0e7;
}

.form-check-input {
  border-color: #cbd3dd;
}
.form-check-input:focus {
  border-color: #4954cb;
  box-shadow: 0 0 0 0.25rem rgba(73, 84, 203, 0.25);
}
.form-check-input:checked {
  background-color: #4954cb;
  border-color: #4954cb;
}

.form-switch .form-check-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23cbd3dd'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%234954cb'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-block .form-check-label {
  border-color: #dbe0e7;
}
.form-block .form-check-label:hover {
  border-color: #cbd3dd;
}
.form-block .form-check-label::before {
  background-color: #4954cb;
}
.form-block .form-check-input:checked ~ .form-check-label {
  border-color: #4954cb;
}
.form-block .form-check-input:focus ~ .form-check-label {
  border-color: #4954cb;
  box-shadow: 0 0 0 0.25rem rgba(73, 84, 203, 0.25);
}
.form-block .form-check-input:disabled:not([checked]) + .form-check-label:hover,
.form-block .form-check-input[readonly]:not([checked]) + .form-check-label:hover {
  border-color: #dbe0e7;
}

.border {
  border-color: #dbe0e7 !important;
}

.border-top {
  border-top-color: #dbe0e7 !important;
}

.border-end {
  border-right-color: #dbe0e7 !important;
}

.border-bottom {
  border-bottom-color: #dbe0e7 !important;
}

.border-start {
  border-left-color: #dbe0e7 !important;
}

.border-primary {
  border-color: #0665d0 !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: #6f9c40 !important;
}

.border-info {
  border-color: #3c90df !important;
}

.border-warning {
  border-color: #e69f17 !important;
}

.border-danger {
  border-color: #e04f1a !important;
}

.border-light {
  border-color: #edf0f7 !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-primary {
  border-color: #4954cb !important;
}

.border-white {
  border-color: #fff !important;
}

.border-white-op {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.border-black-op {
  border-color: rgba(0, 0, 0, 0.1) !important;
}

#page-header {
  background-color: #fff;
}

#sidebar {
  background-color: #fff;
}

#side-overlay {
  background-color: #fff;
}

#page-container {
  background-color: #eaedf1;
}
#page-container.page-header-dark #page-header {
  color: #c2cbd6;
  background-color: #424cb7;
}
#page-container.page-header-glass #page-header {
  background-color: transparent;
}
#page-container.page-header-glass.page-header-fixed.page-header-scroll #page-header {
  background-color: rgba(255, 255, 255, 0.9);
}
#page-container.page-header-glass.page-header-fixed.page-header-scroll.page-header-dark #page-header {
  background-color: rgba(66, 76, 183, 0.9);
}
#page-container.sidebar-dark #sidebar {
  color: #dbe0e7;
  background-color: #3b3946;
}

#sidebar.with-mini-nav .sidebar-mini-nav {
  color: #dbe0e7;
  background-color: #42404e;
}

.block {
  box-shadow: 0 1px 3px rgba(211, 217, 226, 0.5), 0 1px 2px rgba(211, 217, 226, 0.5);
}

.block-header-default {
  background-color: #f3f5f7;
}

.block.block-bordered {
  border-color: #dbe0e7;
}
.block.block-themed > .block-header {
  background-color: #4954cb;
}

.block.block-mode-loading::after {
  color: #4954cb;
}
.block.block-mode-loading.block-mode-loading-dark::after {
  background-color: #394263;
}

a.block {
  color: #343a40;
}
a.block:hover {
  color: #343a40;
}
a.block.block-link-pop:hover {
  box-shadow: 0 0.5rem 2rem #d2d8e1;
}
a.block.block-link-pop:active {
  box-shadow: 0 0.25rem 0.75rem #e7eaef;
}
a.block.block-link-shadow:hover {
  box-shadow: 0 0 2.25rem #d2d8e1;
}
a.block.block-link-shadow:active {
  box-shadow: 0 0 1.125rem #dee3e9;
}

.block.block-fx-shadow {
  box-shadow: 0 0 2.25rem #d2d8e1;
}
.block.block-fx-pop {
  box-shadow: 0 0.5rem 2rem #d2d8e1;
}

.btn-block-option {
  color: #4954cb;
}
.btn-block-option:hover {
  color: #858cdc;
}
a.btn-block-option:focus,
.active > a.btn-block-option,
.show > button.btn-block-option {
  color: #858cdc;
}

.btn-block-option:active {
  color: #b0b5e8;
}

#page-loader {
  background-color: #4954cb;
}

.nav-main-heading {
  color: #6d7a86;
}

.nav-main-link {
  color: #3f474e;
}
.nav-main-link .nav-main-link-icon {
  color: rgba(73, 84, 203, 0.7);
}
.nav-main-link:hover, .nav-main-link.active {
  color: #000;
  background-color: #e8e9f8;
}

.nav-main-submenu {
  background-color: #f4f4fc;
}
.nav-main-submenu .nav-main-link {
  color: #626d78;
}
.nav-main-submenu .nav-main-link:hover, .nav-main-submenu .nav-main-link.active {
  color: #23272b;
  background-color: transparent;
}

.nav-main-item.open > .nav-main-link-submenu {
  color: #000;
  background-color: #e8e9f8;
}

.nav-main-submenu .nav-main-item.open .nav-main-link {
  background-color: transparent;
}

@media (min-width: 992px) {
  .nav-main-horizontal.nav-main-hover .nav-main-item:hover > .nav-main-link-submenu {
    color: #000;
    background-color: #e8e9f8;
  }
}
.nav-main-dark .nav-main-heading,
.sidebar-dark #sidebar .nav-main-heading,
.page-header-dark #page-header .nav-main-heading,
.dark-mode #side-overlay .nav-main-heading,
.dark-mode #main-container .nav-main-heading {
  color: #848098;
}
.nav-main-dark .nav-main-link,
.sidebar-dark #sidebar .nav-main-link,
.page-header-dark #page-header .nav-main-link,
.dark-mode #side-overlay .nav-main-link,
.dark-mode #main-container .nav-main-link {
  color: #c8c7d1;
}
.nav-main-dark .nav-main-link > .nav-main-link-icon,
.sidebar-dark #sidebar .nav-main-link > .nav-main-link-icon,
.page-header-dark #page-header .nav-main-link > .nav-main-link-icon,
.dark-mode #side-overlay .nav-main-link > .nav-main-link-icon,
.dark-mode #main-container .nav-main-link > .nav-main-link-icon {
  color: #6a677e;
}
.nav-main-dark .nav-main-link:hover, .nav-main-dark .nav-main-link.active,
.sidebar-dark #sidebar .nav-main-link:hover,
.sidebar-dark #sidebar .nav-main-link.active,
.page-header-dark #page-header .nav-main-link:hover,
.page-header-dark #page-header .nav-main-link.active,
.dark-mode #side-overlay .nav-main-link:hover,
.dark-mode #side-overlay .nav-main-link.active,
.dark-mode #main-container .nav-main-link:hover,
.dark-mode #main-container .nav-main-link.active {
  color: #fff;
  background-color: #302f39;
}
.nav-main-dark .nav-main-submenu,
.sidebar-dark #sidebar .nav-main-submenu,
.page-header-dark #page-header .nav-main-submenu,
.dark-mode #side-overlay .nav-main-submenu,
.dark-mode #main-container .nav-main-submenu {
  background-color: #34323e;
}
.nav-main-dark .nav-main-submenu .nav-main-link,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-link,
.page-header-dark #page-header .nav-main-submenu .nav-main-link,
.dark-mode #side-overlay .nav-main-submenu .nav-main-link,
.dark-mode #main-container .nav-main-submenu .nav-main-link {
  color: #adaaba;
}
.nav-main-dark .nav-main-submenu .nav-main-link:hover, .nav-main-dark .nav-main-submenu .nav-main-link.active,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-link:hover,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-link.active,
.page-header-dark #page-header .nav-main-submenu .nav-main-link:hover,
.page-header-dark #page-header .nav-main-submenu .nav-main-link.active,
.dark-mode #side-overlay .nav-main-submenu .nav-main-link:hover,
.dark-mode #side-overlay .nav-main-submenu .nav-main-link.active,
.dark-mode #main-container .nav-main-submenu .nav-main-link:hover,
.dark-mode #main-container .nav-main-submenu .nav-main-link.active {
  color: #fff;
  background-color: transparent;
}
.nav-main-dark .nav-main-item.open > .nav-main-link-submenu,
.sidebar-dark #sidebar .nav-main-item.open > .nav-main-link-submenu,
.page-header-dark #page-header .nav-main-item.open > .nav-main-link-submenu,
.dark-mode #side-overlay .nav-main-item.open > .nav-main-link-submenu,
.dark-mode #main-container .nav-main-item.open > .nav-main-link-submenu {
  color: #fff;
  background-color: #302f39;
}
.nav-main-dark .nav-main-item.open > .nav-main-submenu,
.sidebar-dark #sidebar .nav-main-item.open > .nav-main-submenu,
.page-header-dark #page-header .nav-main-item.open > .nav-main-submenu,
.dark-mode #side-overlay .nav-main-item.open > .nav-main-submenu,
.dark-mode #main-container .nav-main-item.open > .nav-main-submenu {
  background-color: #34323e;
}
.nav-main-dark .nav-main-submenu .nav-main-item.open .nav-main-link,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-item.open .nav-main-link,
.page-header-dark #page-header .nav-main-submenu .nav-main-item.open .nav-main-link,
.dark-mode #side-overlay .nav-main-submenu .nav-main-item.open .nav-main-link,
.dark-mode #main-container .nav-main-submenu .nav-main-item.open .nav-main-link {
  background-color: transparent;
}

@media (min-width: 992px) {
  .nav-main-dark.nav-main-horizontal .nav-main-heading,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-heading,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-heading,
  .dark-mode #main-container .nav-main-horizontal .nav-main-heading {
    color: rgba(255, 255, 255, 0.5);
  }
  .nav-main-dark.nav-main-horizontal .nav-main-link,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link {
    color: rgba(255, 255, 255, 0.75);
  }
  .nav-main-dark.nav-main-horizontal .nav-main-link > .nav-main-link-icon,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link > .nav-main-link-icon,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link > .nav-main-link-icon,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link > .nav-main-link-icon {
    color: rgba(255, 255, 255, 0.4);
  }
  .nav-main-dark.nav-main-horizontal .nav-main-link:hover, .nav-main-dark.nav-main-horizontal .nav-main-link.active,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link:hover,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link.active,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link:hover,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link.active,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link:hover,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link.active {
    color: #fff;
    background-color: #2f2e38;
  }
  .nav-main-dark.nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .nav-main-dark.nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu {
    color: #fff;
    background-color: #2f2e38;
  }
  .nav-main-dark.nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .nav-main-dark.nav-main-horizontal .nav-main-item:hover > .nav-main-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu {
    background-color: #2f2e38;
  }
  .nav-main-dark.nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link,
  .dark-mode #main-container .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link {
    background-color: transparent;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link:hover, .page-header-dark #page-header .nav-main-horizontal .nav-main-link.active {
    background-color: #3b44a4;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu {
    background-color: #3b44a4;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu {
    background-color: #3b44a4;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link {
    background-color: transparent;
  }
}
.nav-items a {
  border-bottom-color: #edf0f7;
}
.nav-items a:hover {
  background-color: #f3f5f7;
}

.mini-nav-item {
  color: #adb9c8;
}
.mini-nav-item.active {
  background-color: #494757;
  color: #fff;
}
.mini-nav-item:hover {
  color: #fff;
  background-color: #494757;
}
.mini-nav-item:active {
  color: #adb9c8;
}

.list-activity > li {
  border-bottom-color: #edf0f7;
}

.timeline-event-icon {
  box-shadow: 0 0.375rem 1.5rem #d2d8e1;
}

.ribbon-light .ribbon-box {
  color: #343a40;
  background-color: #dbe0e7;
}
.ribbon-light.ribbon-bookmark .ribbon-box::before {
  border-color: #dbe0e7;
  border-left-color: transparent;
}
.ribbon-light.ribbon-bookmark.ribbon-left .ribbon-box::before {
  border-color: #dbe0e7;
  border-right-color: transparent;
}

.ribbon-primary .ribbon-box {
  color: #fff;
  background-color: #4954cb;
}
.ribbon-primary.ribbon-bookmark .ribbon-box::before {
  border-color: #4954cb;
  border-left-color: transparent;
}
.ribbon-primary.ribbon-bookmark.ribbon-left .ribbon-box::before {
  border-color: #4954cb;
  border-right-color: transparent;
}

.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:focus,
.datepicker table tr td.active:hover:focus,
.datepicker table tr td.active.disabled:focus,
.datepicker table tr td.active.disabled:hover:focus,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.active,
.open .dropdown-toggle.datepicker table tr td.active:hover,
.open .dropdown-toggle.datepicker table tr td.active.disabled,
.open .dropdown-toggle.datepicker table tr td.active.disabled:hover,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:focus,
.datepicker table tr td span.active:hover:focus,
.datepicker table tr td span.active.disabled:focus,
.datepicker table tr td span.active.disabled:hover:focus,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td span.active,
.open .dropdown-toggle.datepicker table tr td span.active:hover,
.open .dropdown-toggle.datepicker table tr td span.active.disabled,
.open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
  background-color: #4954cb;
  border-color: #4954cb;
}

.cke_chrome,
.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.ck.ck-toolbar {
  border-color: #dbe0e7 !important;
}

.cke_top,
.ck.ck-toolbar {
  border-bottom-color: #dbe0e7 !important;
  background: #f3f5f7 !important;
}

.ck.ck-toolbar .ck.ck-toolbar__separator {
  background: #dbe0e7 !important;
}

.cke_bottom {
  border-top-color: #dbe0e7 !important;
  background: #f3f5f7 !important;
}

.dropzone {
  background-color: #f3f5f7;
  border-color: #d2d8e1;
}
.dropzone .dz-message {
  color: #343a40;
}
.dropzone:hover {
  background-color: #fff;
  border-color: #4954cb;
}
.dropzone:hover .dz-message {
  color: #4954cb;
}

.fc.fc-theme-standard a {
  color: #343a40;
}
.fc.fc-theme-standard .fc-button-primary {
  color: #343a40;
  background-color: #dbe0e7;
  border-color: #dbe0e7;
}
.fc.fc-theme-standard .fc-button-primary:not(:disabled):hover {
  color: #343a40;
  background-color: #edf0f7;
  border-color: #edf0f7;
}
.fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active, .fc.fc-theme-standard .fc-button-primary:not(:disabled):active {
  color: #343a40;
  background-color: #f3f5f7;
  border-color: #f3f5f7;
}
.fc.fc-theme-standard .fc-button-primary:focus, .fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active:focus, .fc.fc-theme-standard .fc-button-primary:not(:disabled):active:focus {
  box-shadow: 0 0 0 0.2rem rgba(73, 84, 203, 0.4);
}
.fc.fc-theme-standard th,
.fc.fc-theme-standard td,
.fc.fc-theme-standard .fc-scrollgrid,
.fc.fc-theme-standard .fc-list {
  border-color: #dbe0e7;
}
.fc.fc-theme-standard .fc-h-event {
  background-color: #4954cb;
  border: #4954cb;
}
.fc.fc-theme-standard .fc-col-header-cell,
.fc.fc-theme-standard .fc-list-day-cushion {
  background-color: #f3f5f7;
}

.irs.irs--round .irs-min,
.irs.irs--round .irs-max,
.irs.irs--round .irs-line,
.irs.irs--round .irs-grid-pol {
  background: #edf0f7;
}
.irs.irs--round .irs-handle {
  border-color: #4954cb;
}
.irs.irs--round .irs-from:before,
.irs.irs--round .irs-to:before,
.irs.irs--round .irs-single:before {
  border-top-color: #4954cb;
}
.irs.irs--round .irs-bar,
.irs.irs--round .irs-from,
.irs.irs--round .irs-to,
.irs.irs--round .irs-single {
  background: #4954cb;
}

.select2-container--default .select2-selection--single {
  border-color: #d2d8e1;
}
.select2-container--default .select2-selection--multiple {
  border-color: #d2d8e1;
}
.select2-container--default.select2-container--focus .select2-selection--multiple, .select2-container--default.select2-container--focus .select2-selection--single, .select2-container--default.select2-container--open .select2-selection--multiple, .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #acb1e7;
  box-shadow: 0 0 0 0.25rem rgba(73, 84, 203, 0.25);
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #4954cb;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #d2d8e1;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #4954cb;
}
.select2-container--default .select2-dropdown .select2-search__field:focus {
  border-color: #acb1e7;
  box-shadow: 0 0 0 0.25rem rgba(73, 84, 203, 0.25);
}

.simplebar-scrollbar::before {
  background: rgba(21, 24, 37, 0.75);
}

.slick-slider .slick-prev::before,
.slick-slider .slick-next::before {
  color: #394263;
}

.editor-toolbar {
  border-color: #dbe0e7;
  background-color: #f3f5f7;
}

.CodeMirror {
  border-color: #dbe0e7;
}

.dd-handle {
  color: #343a40;
  background: #f3f5f7;
  border-color: #dbe0e7;
}
.dd-handle:hover {
  color: #212529;
}

.dd-empty,
.dd-placeholder {
  border-color: #282e45;
  background: #b0b5e8;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  border-color: #4954cb;
  background: #4954cb;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: #4954cb;
}

.jvectormap-tip {
  background: #394263;
}

.jvectormap-zoomin,
.jvectormap-zoomout,
.jvectormap-goback {
  background: #394263;
}

table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
  box-shadow: inset 0 0 0 9999px #eff1f4;
}

#page-container.dark-mode {
  background-color: #282730;
}

.dark-mode,
.dark-mode #side-overlay,
.dark-mode #page-loader {
  background-color: #282730;
  color: #adaaba;
}
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6,
.dark-mode .h1,
.dark-mode .h2,
.dark-mode .h3,
.dark-mode .h4,
.dark-mode .h5,
.dark-mode .h6 {
  color: #dbe0e7;
}
.dark-mode .content-heading {
  border-bottom-color: #3d3b49;
}
.dark-mode hr {
  border-top-color: #3d3b49;
}
.dark-mode code {
  color: #e685b5;
}
.dark-mode .story p,
.dark-mode p.story {
  color: #928ea3;
}
.dark-mode a:not(.mini-nav-item):not(.badge):not(.btn):not(.btn-block-option):not(.block):not(.dropdown-item):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(.list-group-item-action):not(.close):not(.fc-event):not(.text-success-light):not(.text-danger-light):not(.text-warning-light):not(.text-info-light) {
  color: #7981d9;
}
.dark-mode a:not(.mini-nav-item):not(.badge):not(.btn):not(.btn-block-option):not(.block):not(.dropdown-item):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(.list-group-item-action):not(.close):not(.fc-event):not(.text-success-light):not(.text-danger-light):not(.text-warning-light):not(.text-info-light).link-fx::before {
  background-color: #7981d9;
}
.dark-mode a:not(.mini-nav-item):not(.badge):not(.btn):not(.btn-block-option):not(.block):not(.dropdown-item):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(.list-group-item-action):not(.close):not(.fc-event):not(.text-success-light):not(.text-danger-light):not(.text-warning-light):not(.text-info-light):hover {
  color: #4954cb;
}
.dark-mode .bg-body {
  background-color: #282730 !important;
}
.dark-mode a.bg-body:hover, .dark-mode a.bg-body:focus,
.dark-mode button.bg-body:hover,
.dark-mode button.bg-body:focus {
  background-color: #101013 !important;
}
.dark-mode .bg-body-light {
  background-color: #363440 !important;
}
.dark-mode a.bg-body-light:hover, .dark-mode a.bg-body-light:focus,
.dark-mode button.bg-body-light:hover,
.dark-mode button.bg-body-light:focus {
  background-color: #1f1e24 !important;
}
.dark-mode .bg-body-dark {
  background-color: #24232b !important;
}
.dark-mode a.bg-body-dark:hover, .dark-mode a.bg-body-dark:focus,
.dark-mode button.bg-body-dark:hover,
.dark-mode button.bg-body-dark:focus {
  background-color: #0d0c0f !important;
}
.dark-mode .bg-body-extra-light {
  background-color: #2f2e38 !important;
}
.dark-mode a.bg-body-extra-light:hover, .dark-mode a.bg-body-extra-light:focus,
.dark-mode button.bg-body-extra-light:hover,
.dark-mode button.bg-body-extra-light:focus {
  background-color: #17171c !important;
}
.dark-mode .bg-muted {
  background-color: #97a6ba !important;
}
.dark-mode a.bg-muted:hover, .dark-mode a.bg-muted:focus,
.dark-mode button.bg-muted:hover,
.dark-mode button.bg-muted:focus {
  background-color: #798ca6 !important;
}
.dark-mode .text-primary {
  color: #7981d9 !important;
}
.dark-mode a.text-primary.link-fx::before {
  background-color: #7981d9 !important;
}
.dark-mode a.text-primary:hover, .dark-mode a.text-primary:focus {
  color: #515bcd !important;
}
.dark-mode .text-success {
  color: #7cae47 !important;
}
.dark-mode a.text-success.link-fx::before {
  background-color: #7cae47 !important;
}
.dark-mode a.text-success:hover, .dark-mode a.text-success:focus {
  color: #628a39 !important;
}
.dark-mode .text-warning {
  color: #eaa92d !important;
}
.dark-mode a.text-warning.link-fx::before {
  background-color: #eaa92d !important;
}
.dark-mode a.text-warning:hover, .dark-mode a.text-warning:focus {
  color: #cf8f15 !important;
}
.dark-mode .text-info {
  color: #529ce3 !important;
}
.dark-mode a.text-info.link-fx::before {
  background-color: #529ce3 !important;
}
.dark-mode a.text-info:hover, .dark-mode a.text-info:focus {
  color: #2684db !important;
}
.dark-mode .text-danger {
  color: #e75f2d !important;
}
.dark-mode a.text-danger.link-fx::before {
  background-color: #e75f2d !important;
}
.dark-mode a.text-danger:hover, .dark-mode a.text-danger:focus {
  color: #c94717 !important;
}
.dark-mode .text-body-bg {
  color: #282730 !important;
}
.dark-mode a.text-body-bg.link-fx::before {
  background-color: #282730 !important;
}
.dark-mode a.text-body-bg:hover, .dark-mode a.text-body-bg:focus {
  color: #101013 !important;
}
.dark-mode .text-body-bg-dark {
  color: #212027 !important;
}
.dark-mode a.text-body-bg-dark.link-fx::before {
  background-color: #212027 !important;
}
.dark-mode a.text-body-bg-dark:hover, .dark-mode a.text-body-bg-dark:focus {
  color: #09090b !important;
}
.dark-mode .text-body-bg-light {
  color: #363440 !important;
}
.dark-mode a.text-body-bg-light.link-fx::before {
  background-color: #363440 !important;
}
.dark-mode a.text-body-bg-light:hover, .dark-mode a.text-body-bg-light:focus {
  color: #1f1e24 !important;
}
.dark-mode .text-body-color {
  color: #adaaba !important;
}
.dark-mode a.text-body-color.link-fx::before {
  background-color: #adaaba !important;
}
.dark-mode a.text-body-color:hover, .dark-mode a.text-body-color:focus {
  color: #928ea3 !important;
}
.dark-mode .text-body-color-dark {
  color: #848098 !important;
}
.dark-mode a.text-body-color-dark.link-fx::before {
  background-color: #848098 !important;
}
.dark-mode a.text-body-color-dark:hover, .dark-mode a.text-body-color-dark:focus {
  color: #6a677e !important;
}
.dark-mode .text-body-color-light {
  color: #d6d5dc !important;
}
.dark-mode a.text-body-color-light.link-fx::before {
  background-color: #d6d5dc !important;
}
.dark-mode a.text-body-color-light:hover, .dark-mode a.text-body-color-light:focus {
  color: #bab8c6 !important;
}
.dark-mode .text-dark {
  color: #9eabbe !important;
}
.dark-mode a.text-dark.link-fx::before {
  background-color: #9eabbe !important;
}
.dark-mode a.text-dark:hover, .dark-mode a.text-dark:focus {
  color: #7f91aa !important;
}
.dark-mode .text-muted {
  color: #928ea3 !important;
}
.dark-mode a.text-muted.link-fx::before {
  background-color: #928ea3 !important;
}
.dark-mode a.text-muted:hover, .dark-mode a.text-muted:focus {
  color: #76728c !important;
}
.dark-mode .btn-secondary,
.dark-mode .btn-alt-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #3b3946;
  --bs-btn-border-color: #3b3946;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #23222a;
  --bs-btn-hover-border-color: #23222a;
  --bs-btn-focus-shadow-rgb: 88, 87, 98;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #2f2e38;
  --bs-btn-active-border-color: #2c2b35;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #3b3946;
  --bs-btn-disabled-border-color: #3b3946;
}
.dark-mode .nav-link {
  color: #bcc6d2;
}
.dark-mode .nav-link:hover, .dark-mode .nav-link:focus {
  color: #4954cb;
}
.dark-mode .nav-pills .nav-link {
  color: #7f91aa;
}
.dark-mode .nav-pills .nav-link:hover, .dark-mode .nav-pills .nav-link:focus {
  background-color: #42404e;
}
.dark-mode .nav-pills .nav-link.active,
.dark-mode .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #4954cb;
}
.dark-mode .nav-tabs {
  border-bottom-color: #3d3b49;
}
.dark-mode .nav-tabs .nav-link:hover, .dark-mode .nav-tabs .nav-link:focus {
  border-color: #3d3b49 #3d3b49 #3d3b49;
}
.dark-mode .nav-tabs .nav-link.active,
.dark-mode .nav-tabs .nav-item.show .nav-link {
  color: #bcc6d2;
  background-color: transparent;
  border-color: #3d3b49 #3d3b49 #2f2e38;
}
.dark-mode .nav-tabs-block {
  background-color: #3d3b49;
}
.dark-mode .nav-tabs-block .nav-link {
  border-color: transparent;
  color: #bcc6d2;
}
.dark-mode .nav-tabs-block .nav-link:hover, .dark-mode .nav-tabs-block .nav-link:focus {
  color: #4954cb;
  background-color: #363440;
  border-color: transparent;
}
.dark-mode .nav-tabs-block .nav-link.active,
.dark-mode .nav-tabs-block .nav-item.show .nav-link {
  color: #bcc6d2;
  background-color: #2f2e38;
  border-color: transparent;
}
.dark-mode .nav-tabs-alt {
  border-bottom-color: #363440;
}
.dark-mode .nav-tabs-alt .nav-link {
  color: #bcc6d2;
  background-color: transparent;
  border-color: transparent;
}
.dark-mode .nav-tabs-alt .nav-link:hover, .dark-mode .nav-tabs-alt .nav-link:focus {
  color: #4954cb;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #4954cb;
}
.dark-mode .nav-tabs-alt .nav-link.active,
.dark-mode .nav-tabs-alt .nav-item.show .nav-link {
  color: #bcc6d2;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #4954cb;
}
.dark-mode .nav-items a:hover {
  background-color: #42404e;
}
.dark-mode .nav-items a:active {
  background-color: transparent;
}
.dark-mode .nav-items > li:not(:last-child) > a {
  border-bottom-color: #3d3b49;
}
.dark-mode .card {
  background-color: #2f2e38;
}
.dark-mode .card.card-borderless {
  box-shadow: 0 1px 2px rgba(38, 36, 45, 0.5), 0 1px 2px rgba(38, 36, 45, 0.5);
}
.dark-mode .card > .card-header:not(.bg-transparent),
.dark-mode .card > .card-footer:not(.bg-transparent) {
  background-color: #3d3b49;
}
.dark-mode .card > .card-header:not(.border-bottom-0),
.dark-mode .card > .card-footer:not(.border-top-0) {
  border-color: #3d3b49;
}
.dark-mode .card:not(.card-borderless),
.dark-mode .card:not(.card-borderless) > .card-header {
  border-color: #3d3b49;
}
.dark-mode .card > .card-header .block-title small {
  color: #97a6ba;
}
.dark-mode .page-link {
  color: #7f91aa;
  background-color: #282730;
  border-color: #282730;
}
.dark-mode .page-link:hover {
  color: #7f91aa;
  background-color: #212027;
  border-color: #212027;
}
.dark-mode .page-link:focus {
  background-color: #212027;
  border-color: #212027;
}
.dark-mode .page-item.active .page-link {
  background-color: #4954cb;
  border-color: #4954cb;
}
.dark-mode .page-item.disabled .page-link {
  color: #4e5e75;
  background-color: transparent;
  border-color: transparent;
}
.dark-mode .list-group-item-action {
  color: #bcc6d2;
}
.dark-mode .list-group-item-action:hover, .dark-mode .list-group-item-action:focus {
  color: #bcc6d2;
  background-color: #3a3845;
}
.dark-mode .list-group-item-action:active {
  color: #bcc6d2;
  background-color: #282730;
}
.dark-mode .list-group-item-action.disabled {
  color: #627793;
}
.dark-mode .list-group-item {
  color: #bcc6d2;
  background-color: #363440;
  border-color: #24232b;
}
.dark-mode .list-group-item.active {
  color: #fff;
  background-color: #4954cb;
  border-color: #4954cb;
}
.dark-mode .popover {
  border-color: #24232b;
  background-color: #363440;
}
.dark-mode .bs-popover-top .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=top] .popover-arrow::before {
  border-top-color: #24232b;
}
.dark-mode .bs-popover-top .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=top] .popover-arrow::after {
  border-top-color: #363440;
}
.dark-mode .bs-popover-end .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=right] .popover-arrow::before {
  border-right-color: #24232b;
}
.dark-mode .bs-popover-end .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=right] .popover-arrow::after {
  border-right-color: #363440;
}
.dark-mode .bs-popover-bottom .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=bottom] .popover-arrow::before {
  border-bottom-color: #24232b;
}
.dark-mode .bs-popover-bottom .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=bottom] .popover-arrow::after {
  border-bottom-color: #363440;
}
.dark-mode .bs-popover-start .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=left] .popover-arrow::before {
  border-left-color: #24232b;
}
.dark-mode .bs-popover-start .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=left] .popover-arrow::after {
  border-left-color: #363440;
}
.dark-mode .popover-header {
  color: #dbe0e7;
  background-color: #363440;
  border-bottom-color: #24232b;
}
.dark-mode .popover-body {
  color: #bcc6d2;
  background-color: #363440;
}
.dark-mode .dropdown-menu {
  color: #bcc6d2;
  background-color: #363440;
  border-color: #363440;
  box-shadow: 0 0.25rem 2rem rgba(0, 0, 0, 0.25);
}
.dark-mode .dropdown-menu .dropdown-item {
  color: #adb9c8;
}
.dark-mode .dropdown-menu .dropdown-item:hover, .dark-mode .dropdown-menu .dropdown-item:focus {
  color: #bcc6d2;
  background-color: #42404e;
}
.dark-mode .dropdown-menu .dropdown-item.active, .dark-mode .dropdown-menu .dropdown-item:active {
  color: #cbd3dd;
  background-color: #4c495a;
}
.dark-mode .dropdown-menu .dropdown-item.disabled, .dark-mode .dropdown-menu .dropdown-item:disabled {
  color: #4e5e75;
}
.dark-mode .dropdown-menu .dropdown-divider {
  border-color: #474454;
}
.dark-mode .dropdown-menu .dropdown-item-text {
  color: #bcc6d2;
}
.dark-mode .dropdown-menu .dropdown-header {
  color: #8e9eb4 !important;
}
.dark-mode .table {
  --bs-table-color: #adaaba;
  --bs-table-bg: #2f2e38;
  --bs-table-striped-color: #f3f5f7;
  --bs-table-striped-bg: #2c2a34;
  --bs-table-active-color: #f3f5f7;
  --bs-table-active-bg: #3d3b49;
  --bs-table-hover-color: #f3f5f7;
  --bs-table-hover-bg: #292831;
  color: #f3f5f7;
  border-color: #212027;
}
.dark-mode .table > :not(:last-child) > :last-child > * {
  border-bottom-color: #212027;
}
.dark-mode .table-dark {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(36, 35, 43, 0.75);
  --bs-table-border-color: rgba(100, 100, 105, 0.8);
  --bs-table-striped-bg: rgba(54, 53, 60, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(70, 69, 76, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(62, 61, 68, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-primary {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(58, 67, 162, 0.75);
  --bs-table-border-color: rgba(116, 122, 189, 0.8);
  --bs-table-striped-bg: rgba(74, 82, 170, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(89, 96, 177, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(81, 89, 173, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-info {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(30, 58, 138, 0.75);
  --bs-table-border-color: rgba(96, 116, 172, 0.8);
  --bs-table-striped-bg: rgba(48, 74, 147, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(65, 89, 156, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(57, 81, 152, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-success {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(20, 83, 45, 0.75);
  --bs-table-border-color: rgba(89, 134, 107, 0.8);
  --bs-table-striped-bg: rgba(39, 97, 62, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(57, 110, 78, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(48, 103, 70, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-danger {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(127, 29, 29, 0.75);
  --bs-table-border-color: rgba(165, 95, 95, 0.8);
  --bs-table-striped-bg: rgba(137, 47, 47, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(147, 64, 64, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(142, 56, 56, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-warning {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(113, 63, 18, 0.75);
  --bs-table-border-color: rgba(155, 119, 88, 0.8);
  --bs-table-striped-bg: rgba(124, 78, 37, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(135, 93, 55, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(130, 86, 46, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .form-text {
  color: #848098;
}
.dark-mode .form-control::-moz-placeholder {
  color: #848098;
}
.dark-mode .form-control::placeholder {
  color: #848098;
}
.dark-mode .form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.dark-mode .form-floating > .form-control::placeholder {
  color: transparent;
}
.dark-mode .form-control,
.dark-mode .form-select {
  color: #bcc6d2;
  background-color: #24232b;
  border-color: #42404e;
}
.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
  color: #fff;
  background-color: #24232b;
  border-color: #4954cb;
}
.dark-mode .form-control:disabled,
.dark-mode .form-select:disabled {
  background-color: #34323e;
  border-color: #34323e;
}
.dark-mode .form-select:focus::-ms-value {
  color: #bcc6d2;
  background-color: #24232b;
}
.dark-mode .form-control-plaintext {
  color: #bcc6d2;
}
.dark-mode .valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #89b956;
}
.dark-mode .valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000;
  background-color: rgba(137, 185, 86, 0.9);
  border-radius: var(--bs-border-radius);
}
.was-validated .dark-mode:valid ~ .valid-feedback,
.was-validated .dark-mode:valid ~ .valid-tooltip, .dark-mode.is-valid ~ .valid-feedback,
.dark-mode.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .dark-mode .form-control:valid, .dark-mode .form-control.is-valid {
  border-color: #89b956;
}
.was-validated .dark-mode .form-control:valid:focus, .dark-mode .form-control.is-valid:focus {
  border-color: #89b956;
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.was-validated .dark-mode .form-select:valid, .dark-mode .form-select.is-valid {
  border-color: #89b956;
}
.was-validated .dark-mode .form-select:valid:focus, .dark-mode .form-select.is-valid:focus {
  border-color: #89b956;
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.was-validated .dark-mode .form-check-input:valid, .dark-mode .form-check-input.is-valid {
  border-color: #89b956;
}
.was-validated .dark-mode .form-check-input:valid:checked, .dark-mode .form-check-input.is-valid:checked {
  background-color: #89b956;
}
.was-validated .dark-mode .form-check-input:valid:focus, .dark-mode .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.was-validated .dark-mode .form-check-input:valid ~ .form-check-label, .dark-mode .form-check-input.is-valid ~ .form-check-label {
  color: #89b956;
}
.dark-mode .form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}
.was-validated .dark-mode .input-group > .form-control:not(:focus):valid, .dark-mode .input-group > .form-control:not(:focus).is-valid,
.was-validated .dark-mode .input-group > .form-select:not(:focus):valid,
.dark-mode .input-group > .form-select:not(:focus).is-valid,
.was-validated .dark-mode .input-group > .form-floating:not(:focus-within):valid,
.dark-mode .input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}
.dark-mode .invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #ec815b;
}
.dark-mode .invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000;
  background-color: rgba(236, 129, 91, 0.9);
  border-radius: var(--bs-border-radius);
}
.was-validated .dark-mode:invalid ~ .invalid-feedback,
.was-validated .dark-mode:invalid ~ .invalid-tooltip, .dark-mode.is-invalid ~ .invalid-feedback,
.dark-mode.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .dark-mode .form-control:invalid, .dark-mode .form-control.is-invalid {
  border-color: #ec815b;
}
.was-validated .dark-mode .form-control:invalid:focus, .dark-mode .form-control.is-invalid:focus {
  border-color: #ec815b;
  box-shadow: 0 0 0 0.25rem rgba(236, 129, 91, 0.25);
}
.was-validated .dark-mode .form-select:invalid, .dark-mode .form-select.is-invalid {
  border-color: #ec815b;
}
.was-validated .dark-mode .form-select:invalid:focus, .dark-mode .form-select.is-invalid:focus {
  border-color: #ec815b;
  box-shadow: 0 0 0 0.25rem rgba(236, 129, 91, 0.25);
}
.was-validated .dark-mode .form-check-input:invalid, .dark-mode .form-check-input.is-invalid {
  border-color: #ec815b;
}
.was-validated .dark-mode .form-check-input:invalid:checked, .dark-mode .form-check-input.is-invalid:checked {
  background-color: #ec815b;
}
.was-validated .dark-mode .form-check-input:invalid:focus, .dark-mode .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(236, 129, 91, 0.25);
}
.was-validated .dark-mode .form-check-input:invalid ~ .form-check-label, .dark-mode .form-check-input.is-invalid ~ .form-check-label {
  color: #ec815b;
}
.dark-mode .form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}
.was-validated .dark-mode .input-group > .form-control:not(:focus):invalid, .dark-mode .input-group > .form-control:not(:focus).is-invalid,
.was-validated .dark-mode .input-group > .form-select:not(:focus):invalid,
.dark-mode .input-group > .form-select:not(:focus).is-invalid,
.was-validated .dark-mode .input-group > .form-floating:not(:focus-within):invalid,
.dark-mode .input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}
.dark-mode .form-control.form-control-alt {
  color: #bcc6d2;
  border-color: #282730;
  background-color: #282730;
}
.dark-mode .form-control.form-control-alt:focus {
  color: #fff;
  border-color: #24232b;
  background-color: #24232b;
  box-shadow: none;
}
.dark-mode .form-control.form-control-alt.is-valid {
  border-color: rgba(20, 83, 45, 0.85);
  background-color: rgba(20, 83, 45, 0.85);
}
.dark-mode .form-control.form-control-alt.is-valid::-moz-placeholder {
  color: #848098;
}
.dark-mode .form-control.form-control-alt.is-valid::placeholder {
  color: #848098;
}
.dark-mode .form-control.form-control-alt.is-valid:focus {
  border-color: #14532d;
  background-color: #14532d;
}
.dark-mode .form-control.form-control-alt.is-invalid {
  border-color: rgba(127, 29, 29, 0.85);
  background-color: rgba(127, 29, 29, 0.85);
}
.dark-mode .form-control.form-control-alt.is-invalid::-moz-placeholder {
  color: #848098;
}
.dark-mode .form-control.form-control-alt.is-invalid::placeholder {
  color: #848098;
}
.dark-mode .form-control.form-control-alt.is-invalid:focus {
  border-color: #7f1d1d;
  background-color: #7f1d1d;
}
.dark-mode .input-group-text {
  color: #bcc6d2;
  background-color: #282730;
  border-color: #42404e;
}
.dark-mode .input-group-text.input-group-text-alt {
  background-color: #24232b;
  border-color: #24232b;
}
.dark-mode .is-valid ~ .valid-feedback,
.dark-mode .is-valid ~ .valid-tooltip,
.dark-mode .was-validated :valid ~ .valid-feedback,
.dark-mode .was-validated :valid ~ .valid-tooltip,
.dark-mode .is-invalid ~ .invalid-feedback,
.dark-mode .is-invalid ~ .invalid-tooltip,
.dark-mode .was-validated :invalid ~ .invalid-feedback,
.dark-mode .was-validated :invalid ~ .invalid-tooltip {
  display: block;
}
.dark-mode .form-check-input {
  background-color: #282730;
  border-color: #42404e;
}
.dark-mode .form-check-input:focus {
  border-color: #4954cb;
}
.dark-mode .form-check-input:checked {
  background-color: #4954cb;
  border-color: #4954cb;
}
.dark-mode .form-block .form-check-label {
  border-color: #42404e;
}
.dark-mode .form-block .form-check-label:hover {
  border-color: #464353;
}
.dark-mode .form-block .form-check-label::before {
  background-color: #4954cb;
}
.dark-mode .form-block .form-check-input:checked ~ .form-check-label {
  border-color: #4954cb;
}
.dark-mode .form-block .form-check-input:focus ~ .form-check-label {
  border-color: #4954cb;
  box-shadow: 0 0 0 0.25rem rgba(73, 84, 203, 0.25);
}
.dark-mode .form-block .form-check-input:disabled:not([checked]) + .form-check-label:hover,
.dark-mode .form-block .form-check-input[readonly]:not([checked]) + .form-check-label:hover {
  border-color: #24232b;
}
.dark-mode .breadcrumb-item + .breadcrumb-item::before {
  color: rgba(255, 255, 255, 0.15);
}
.dark-mode .breadcrumb.breadcrumb-alt .breadcrumb-item + .breadcrumb-item::before {
  color: rgba(255, 255, 255, 0.15);
}
.dark-mode .breadcrumb-item.active {
  color: #fff;
}
.dark-mode .alert-primary {
  --bs-alert-color: #fff;
  --bs-alert-bg: #4954cb;
  --bs-alert-border-color: #4954cb;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-primary .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-secondary {
  --bs-alert-color: #fff;
  --bs-alert-bg: #3d3b49;
  --bs-alert-border-color: #3d3b49;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-secondary .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-success {
  --bs-alert-color: #fff;
  --bs-alert-bg: #537530;
  --bs-alert-border-color: #537530;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-success .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-info {
  --bs-alert-color: #fff;
  --bs-alert-bg: #2d6ca7;
  --bs-alert-border-color: #2d6ca7;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-info .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-warning {
  --bs-alert-color: #fff;
  --bs-alert-bg: #ad7711;
  --bs-alert-border-color: #ad7711;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-warning .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-danger {
  --bs-alert-color: #fff;
  --bs-alert-bg: #a83b14;
  --bs-alert-border-color: #a83b14;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-danger .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-dark {
  --bs-alert-color: #fff;
  --bs-alert-bg: #212027;
  --bs-alert-border-color: #212027;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-dark .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-light {
  --bs-alert-color: #fff;
  --bs-alert-bg: #4c495a;
  --bs-alert-border-color: #4c495a;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-light .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}
.dark-mode .progress {
  background-color: #24232b;
}
.dark-mode .list-activity > li:not(:last-child) {
  border-bottom-color: #24232b;
}
.dark-mode .modal-header {
  border-bottom-color: #3d3b49;
}
.dark-mode .modal-content {
  background: #2f2e38;
}
.dark-mode .modal-footer {
  border-top-color: #3d3b49;
}
.dark-mode .toast {
  background-color: #2f2e38;
}
.dark-mode .toast-header {
  color: #bcc6d2;
  background-color: #3d3b49;
}
.dark-mode .border {
  border-color: #3d3b49 !important;
}
.dark-mode .border-top {
  border-top-color: #3d3b49 !important;
}
.dark-mode .border-end {
  border-right-color: #3d3b49 !important;
}
.dark-mode .border-bottom {
  border-bottom-color: #3d3b49 !important;
}
.dark-mode .border-start {
  border-left-color: #3d3b49 !important;
}
.dark-mode .border-primary {
  border-color: #4954cb !important;
}
.dark-mode .border-white {
  border-color: #fff !important;
}
.dark-mode .border-white-op {
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.dark-mode .border-black-op {
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.dark-mode .block {
  background-color: #2f2e38;
  box-shadow: 0 1px 2px rgba(38, 36, 45, 0.5), 0 1px 2px rgba(38, 36, 45, 0.5);
}
.dark-mode .block.block-bordered {
  border: 1px solid #3d3b49;
  box-shadow: none;
}
.dark-mode .block .block-header-default {
  background-color: #3d3b49 !important;
}
.dark-mode .block .block-title small {
  color: #97a6ba;
}
.dark-mode .block.block-mode-loading::before {
  background-color: rgba(61, 59, 73, 0.85);
}
.dark-mode .block.block-mode-loading::after {
  color: #fff;
}
.dark-mode .block.block-transparent {
  background-color: transparent;
  box-shadow: none;
}
.dark-mode .block.block-mode-fullscreen.block-transparent {
  background-color: #2f2e38;
}
.dark-mode .block .block,
.dark-mode .content-side .block {
  box-shadow: none;
}
.dark-mode a.block {
  color: #bcc6d2;
}
.dark-mode a.block.block-link-pop:hover {
  box-shadow: 0 0.5rem 2.5rem #1c1b21;
}
.dark-mode a.block.block-link-pop:active {
  box-shadow: 0 0.375rem 0.55rem #2d2b35;
}
.dark-mode a.block.block-link-shadow:hover {
  box-shadow: 0 0 1.5rem #1c1b21;
}
.dark-mode a.block.block-link-shadow:active {
  box-shadow: 0 0 0.75rem #23222a;
}
.dark-mode .btn-block-option {
  color: #97a6ba;
}
.block-header-default .dark-mode .btn-block-option {
  color: #8899b0;
}
.dark-mode .btn-block-option:hover, .dark-mode .btn-block-option:focus {
  color: #798ca6;
}
.dark-mode .btn-block-option:active {
  color: #97a6ba;
}
.dark-mode a.btn-block-option:focus,
.dark-mode .active > a.btn-block-option,
.dark-mode .show > button.btn-block-option {
  color: #798ca6;
}
.dark-mode .block.block-themed .btn-block-option,
.dark-mode .block.block-themed .btn-block-option:hover,
.dark-mode .block.block-themed .btn-block-option:focus,
.dark-mode .block.block-themed .btn-block-option:active,
.dark-mode .block.block-themed a.btn-block-option:focus,
.dark-mode .block.block-themed .active > a.btn-block-option,
.dark-mode .block.block-themed .show > button.btn-block-option {
  color: #fff;
}
.dark-mode .timeline::before {
  background-color: #3d3b49;
}
.dark-mode .timeline-event-icon {
  border-color: #3d3b49;
  box-shadow: 0 0.375rem 1.5rem #2f2e38;
}
.dark-mode .timeline-event-icon::before {
  border-left-color: #3d3b49;
}
@media (min-width: 1200px) {
  .dark-mode .timeline-centered .timeline-event-time {
    background-color: #3d3b49;
  }
  .dark-mode .timeline-centered .timeline-event-icon::before {
    border-right-color: #3d3b49;
  }
  .dark-mode .timeline-centered.timeline-alt .timeline-event:nth-child(even) .timeline-event-icon::before,
  .dark-mode .timeline-centered .timeline-event.timeline-event-alt .timeline-event-icon::before {
    border-left-color: #3d3b49;
  }
}
.dark-mode .img-thumb {
  background-color: #212027;
}
.dark-mode .swal2-popup {
  background-color: #2f2e38;
}
.dark-mode .swal2-html-container {
  color: #bcc6d2;
}
.dark-mode .jvectormap-container {
  background-color: #2f2e38 !important;
}
.dark-mode .ck.ck-editor {
  color: #343a40;
}
.dark-mode .dropzone {
  background-color: #363440;
  border-color: #3d3b49;
}
.dark-mode .dropzone .dz-message {
  color: #bcc6d2;
}
.dark-mode .dropzone:hover {
  background-color: #3d3b49;
  border-color: #4954cb;
}
.dark-mode .dropzone:hover .dz-message {
  color: #4954cb;
}
.dark-mode .dropzone .dz-preview.dz-image-preview {
  background-color: transparent;
}
.dark-mode .fc.fc-theme-standard a {
  color: #adb9c8 !important;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary {
  color: #adb9c8;
  background-color: #24232b;
  border-color: #24232b;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled):hover {
  color: #bcc6d2;
  background-color: #363440;
  border-color: #363440;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active, .dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled):active {
  color: #bcc6d2;
  background-color: #363440;
  border-color: #363440;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary:focus, .dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active:focus, .dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled):active:focus {
  box-shadow: 0 0 0 0.2rem rgba(73, 84, 203, 0.4);
}
.dark-mode .fc.fc-theme-standard .fc-list, .dark-mode .fc.fc-theme-standard .fc-scrollgrid,
.dark-mode .fc.fc-theme-standard th,
.dark-mode .fc.fc-theme-standard td {
  border-color: #212027;
}
.dark-mode .fc.fc-theme-standard .fc-list-day-cushion,
.dark-mode .fc.fc-theme-standard .fc-col-header-cell {
  background-color: #363440;
}
.dark-mode .fc.fc-theme-standard .fc-list-event:hover td {
  background-color: #292831;
}
.dark-mode .irs,
.dark-mode .irs--round .irs-grid-text {
  color: #bcc6d2;
}
.dark-mode .irs.irs--round .irs-min,
.dark-mode .irs.irs--round .irs-max,
.dark-mode .irs.irs--round .irs-line,
.dark-mode .irs.irs--round .irs-grid-pol,
.dark-mode .irs.irs--round .irs-handle {
  color: #bcc6d2;
  background: #282730;
}
.dark-mode .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #848098;
}
.dark-mode .select2-container--default .select2-selection--single,
.dark-mode .select2-container--default .select2-selection--multiple {
  background-color: #24232b;
  border-color: #42404e;
}
.dark-mode .select2-container--default.select2-container--focus .select2-selection--multiple, .dark-mode .select2-container--default.select2-container--focus .select2-selection--single, .dark-mode .select2-container--default.select2-container--open .select2-selection--multiple, .dark-mode .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #4954cb;
}
.dark-mode .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #bcc6d2;
}
.dark-mode .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #42404e;
}
.dark-mode .select2-container--default .select2-dropdown .select2-search__field:focus {
  border-color: #4954cb;
}
.dark-mode .select2-container--default .select2-dropdown {
  background-color: #24232b;
  border-color: #42404e;
}
.dark-mode .select2-container--default .select2-search--dropdown .select2-search__field {
  color: #bcc6d2;
  background-color: #24232b;
  border-color: #42404e;
}
.dark-mode .select2-container--default .select2-results__option[aria-selected=true] {
  color: #fff;
  background-color: #4954cb;
}
.dark-mode .select2-container--default .select2-search__field::-moz-placeholder {
  color: #848098;
}
.dark-mode .select2-container--default .select2-search__field::placeholder {
  color: #848098;
}
.dark-mode .is-valid + .select2-container--default .select2-selection--single,
.dark-mode .is-valid + .select2-container--default .select2-selection--multiple,
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #89b956;
}
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--multiple,
.dark-mode .is-valid + .select2-container--default.select2-container--open .select2-selection--single,
.dark-mode .is-valid + .select2-container--default.select2-container--open .select2-selection--multiple {
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.dark-mode .is-invalid + .select2-container--default .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default .select2-selection--multiple,
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #e97044;
}
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--multiple,
.dark-mode .is-invalid + .select2-container--default.select2-container--open .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default.select2-container--open .select2-selection--multiple {
  box-shadow: 0 0 0 0.25rem rgba(233, 112, 68, 0.25);
}
.dark-mode .datepicker .datepicker-switch:hover,
.dark-mode .datepicker .next:hover,
.dark-mode .datepicker .prev:hover,
.dark-mode .datepicker tfoot tr th:hover,
.dark-mode .datepicker table tr td.day:hover,
.dark-mode .datepicker table tr td.focused {
  background-color: #24232b;
}
.dark-mode .datepicker table tr td.selected,
.dark-mode .datepicker table tr td.selected.highlighted {
  color: #fff;
  background-color: #24232b;
  border-color: #24232b;
}
.dark-mode .datepicker table tr td.range {
  color: #bcc6d2;
  background-color: #24232b;
  border-color: #24232b;
}
.dark-mode .datepicker table tr td.active:hover,
.dark-mode .datepicker table tr td.active:hover:hover,
.dark-mode .datepicker table tr td.active.disabled:hover,
.dark-mode .datepicker table tr td.active.disabled:hover:hover,
.dark-mode .datepicker table tr td.active:focus,
.dark-mode .datepicker table tr td.active:hover:focus,
.dark-mode .datepicker table tr td.active.disabled:focus,
.dark-mode .datepicker table tr td.active.disabled:hover:focus,
.dark-mode .datepicker table tr td.active:active,
.dark-mode .datepicker table tr td.active:hover:active,
.dark-mode .datepicker table tr td.active.disabled:active,
.dark-mode .datepicker table tr td.active.disabled:hover:active,
.dark-mode .datepicker table tr td.active.active,
.dark-mode .datepicker table tr td.active:hover.active,
.dark-mode .datepicker table tr td.active.disabled.active,
.dark-mode .datepicker table tr td.active.disabled:hover.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active:hover,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active.disabled,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active.disabled:hover,
.dark-mode .datepicker table tr td span.active:hover,
.dark-mode .datepicker table tr td span.active:hover:hover,
.dark-mode .datepicker table tr td span.active.disabled:hover,
.dark-mode .datepicker table tr td span.active.disabled:hover:hover,
.dark-mode .datepicker table tr td span.active:focus,
.dark-mode .datepicker table tr td span.active:hover:focus,
.dark-mode .datepicker table tr td span.active.disabled:focus,
.dark-mode .datepicker table tr td span.active.disabled:hover:focus,
.dark-mode .datepicker table tr td span.active:active,
.dark-mode .datepicker table tr td span.active:hover:active,
.dark-mode .datepicker table tr td span.active.disabled:active,
.dark-mode .datepicker table tr td span.active.disabled:hover:active,
.dark-mode .datepicker table tr td span.active.active,
.dark-mode .datepicker table tr td span.active:hover.active,
.dark-mode .datepicker table tr td span.active.disabled.active,
.dark-mode .datepicker table tr td span.active.disabled:hover.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active:hover,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active.disabled,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
  background-color: #4954cb;
  border-color: #4954cb;
}
.dark-mode .flatpickr-input.form-control:disabled,
.dark-mode .flatpickr-input.form-control[readonly],
.dark-mode .input.form-control:disabled,
.dark-mode .input.form-control[readonly] {
  color: #bcc6d2;
  background-color: #24232b;
  border-color: #42404e;
}
.dark-mode .flatpickr-day.selected,
.dark-mode .flatpickr-day.startRange,
.dark-mode .flatpickr-day.endRange,
.dark-mode .flatpickr-day.selected.inRange,
.dark-mode .flatpickr-day.startRange.inRange,
.dark-mode .flatpickr-day.endRange.inRange,
.dark-mode .flatpickr-day.selected:focus,
.dark-mode .flatpickr-day.startRange:focus,
.dark-mode .flatpickr-day.endRange:focus,
.dark-mode .flatpickr-day.selected:hover,
.dark-mode .flatpickr-day.startRange:hover,
.dark-mode .flatpickr-day.endRange:hover,
.dark-mode .flatpickr-day.selected.prevMonthDay,
.dark-mode .flatpickr-day.startRange.prevMonthDay,
.dark-mode .flatpickr-day.endRange.prevMonthDay,
.dark-mode .flatpickr-day.selected.nextMonthDay,
.dark-mode .flatpickr-day.startRange.nextMonthDay,
.dark-mode .flatpickr-day.endRange.nextMonthDay {
  border-color: #4954cb;
  background: #4954cb;
}
.dark-mode .flatpickr-months .flatpickr-prev-month:hover svg,
.dark-mode .flatpickr-months .flatpickr-next-month:hover svg {
  fill: #4954cb;
}
.dark-mode .dd-handle {
  color: #bcc6d2;
  background: #363440;
  border-color: #24232b;
}
.dark-mode .dd-handle:hover {
  color: #9eabbe;
}
.dark-mode .dd-empty,
.dark-mode .dd-placeholder {
  border-color: #b0b5e8;
  background: #2f2e38;
}
.dark-mode table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
  box-shadow: inset 0 0 0 9999px #2c2a34;
}