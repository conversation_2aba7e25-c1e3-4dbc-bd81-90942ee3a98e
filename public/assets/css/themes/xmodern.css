/*!
 * dashmix - v5.9.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2024
 */
body {
  color: #343a40;
  background-color: #edf0f7;
}

a {
  color: #45619d;
}
a.link-fx::before {
  background-color: #45619d;
}
a:hover {
  color: #263556;
}

.content-heading {
  border-bottom-color: #e5e7ef;
}

hr {
  border-top-color: #e5e7ef;
}

.text-primary {
  color: #45619d !important;
}

a.text-primary.link-fx::before {
  background-color: #45619d !important;
}
a.text-primary:hover, a.text-primary:focus {
  color: #354b7a !important;
}

.text-primary-dark {
  color: #354b7a !important;
}

a.text-primary-dark.link-fx::before {
  background-color: #354b7a !important;
}
a.text-primary-dark:hover, a.text-primary-dark:focus {
  color: #263556 !important;
}

.text-primary-darker {
  color: #263556 !important;
}

a.text-primary-darker.link-fx::before {
  background-color: #263556 !important;
}
a.text-primary-darker:hover, a.text-primary-darker:focus {
  color: #161f33 !important;
}

.text-primary-light {
  color: #6f89c0 !important;
}

a.text-primary-light.link-fx::before {
  background-color: #6f89c0 !important;
}
a.text-primary-light:hover, a.text-primary-light:focus {
  color: #4d6caf !important;
}

.text-primary-lighter {
  color: #b6c3df !important;
}

a.text-primary-lighter.link-fx::before {
  background-color: #b6c3df !important;
}
a.text-primary-lighter:hover, a.text-primary-lighter:focus {
  color: #92a6cf !important;
}

.text-body-bg {
  color: #edf0f7 !important;
}

a.text-body-bg.link-fx::before {
  background-color: #edf0f7 !important;
}
a.text-body-bg:hover, a.text-body-bg:focus {
  color: #cad3e7 !important;
}

.text-body-bg-light {
  color: #f8f9fb !important;
}

a.text-body-bg-light.link-fx::before {
  background-color: #f8f9fb !important;
}
a.text-body-bg-light:hover, a.text-body-bg-light:focus {
  color: #d9dce7 !important;
}

.text-body-bg-dark {
  color: #e5e7ef !important;
}

a.text-body-bg-dark.link-fx::before {
  background-color: #e5e7ef !important;
}
a.text-body-bg-dark:hover, a.text-body-bg-dark:focus {
  color: #c6cadc !important;
}

.text-body-color {
  color: #343a40 !important;
}

a.text-body-color.link-fx::before {
  background-color: #343a40 !important;
}
a.text-body-color:hover, a.text-body-color:focus {
  color: #1d2124 !important;
}

.text-body-color-dark {
  color: #212529 !important;
}

a.text-body-color-dark.link-fx::before {
  background-color: #212529 !important;
}
a.text-body-color-dark:hover, a.text-body-color-dark:focus {
  color: #0a0c0d !important;
}

.text-body-color-light {
  color: #e5e7ef !important;
}

a.text-body-color-light.link-fx::before {
  background-color: #e5e7ef !important;
}
a.text-body-color-light:hover, a.text-body-color-light:focus {
  color: #c6cadc !important;
}

.text-dual {
  color: #354b7a !important;
}

a.text-dual.link-fx::before {
  background-color: #354b7a !important;
}
a.text-dual:hover, a.text-dual:focus {
  color: #263556 !important;
}

.page-header-dark #page-header .text-dual,
.sidebar-dark #sidebar .text-dual {
  color: #e5e7ef !important;
}
.page-header-dark #page-header a.text-dual.link-fx::before,
.sidebar-dark #sidebar a.text-dual.link-fx::before {
  background-color: #e5e7ef !important;
}
.page-header-dark #page-header a.text-dual:hover, .page-header-dark #page-header a.text-dual:focus,
.sidebar-dark #sidebar a.text-dual:hover,
.sidebar-dark #sidebar a.text-dual:focus {
  color: #c6cadc !important;
}

.bg-primary {
  background-color: #45619d !important;
}

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #354b7a !important;
}

.bg-primary-op {
  background-color: rgba(69, 97, 157, 0.75) !important;
}

a.bg-primary-op:hover, a.bg-primary-op:focus,
button.bg-primary-op:hover,
button.bg-primary-op:focus {
  background-color: rgba(53, 75, 122, 0.75) !important;
}

.bg-primary-dark {
  background-color: #354b7a !important;
}

a.bg-primary-dark:hover, a.bg-primary-dark:focus,
button.bg-primary-dark:hover,
button.bg-primary-dark:focus {
  background-color: #263556 !important;
}

.bg-primary-dark-op {
  background-color: rgba(53, 75, 122, 0.8) !important;
}

a.bg-primary-dark-op:hover, a.bg-primary-dark-op:focus,
button.bg-primary-dark-op:hover,
button.bg-primary-dark-op:focus {
  background-color: rgba(38, 53, 86, 0.8) !important;
}

.bg-primary-darker {
  background-color: #263556 !important;
}

a.bg-primary-darker:hover, a.bg-primary-darker:focus,
button.bg-primary-darker:hover,
button.bg-primary-darker:focus {
  background-color: #161f33 !important;
}

.bg-primary-light {
  background-color: #6f89c0 !important;
}

a.bg-primary-light:hover, a.bg-primary-light:focus,
button.bg-primary-light:hover,
button.bg-primary-light:focus {
  background-color: #4d6caf !important;
}

.bg-primary-lighter {
  background-color: #b6c3df !important;
}

a.bg-primary-lighter:hover, a.bg-primary-lighter:focus,
button.bg-primary-lighter:hover,
button.bg-primary-lighter:focus {
  background-color: #92a6cf !important;
}

.bg-body {
  background-color: #edf0f7 !important;
}

a.bg-body:hover, a.bg-body:focus,
button.bg-body:hover,
button.bg-body:focus {
  background-color: #cad3e7 !important;
}

.bg-body-light {
  background-color: #f8f9fb !important;
}

a.bg-body-light:hover, a.bg-body-light:focus,
button.bg-body-light:hover,
button.bg-body-light:focus {
  background-color: #d9dce7 !important;
}

.bg-body-dark {
  background-color: #e5e7ef !important;
}

a.bg-body-dark:hover, a.bg-body-dark:focus,
button.bg-body-dark:hover,
button.bg-body-dark:focus {
  background-color: #c6cadc !important;
}

.bg-header-light {
  background-color: #fff !important;
}

a.bg-header-light:hover, a.bg-header-light:focus,
button.bg-header-light:hover,
button.bg-header-light:focus {
  background-color: #e6e6e6 !important;
}

.bg-header-dark {
  background-color: #3e578d !important;
}

a.bg-header-dark:hover, a.bg-header-dark:focus,
button.bg-header-dark:hover,
button.bg-header-dark:focus {
  background-color: #2e416a !important;
}

.bg-sidebar-light {
  background-color: #fff !important;
}

a.bg-sidebar-light:hover, a.bg-sidebar-light:focus,
button.bg-sidebar-light:hover,
button.bg-sidebar-light:focus {
  background-color: #e6e6e6 !important;
}

.bg-sidebar-dark {
  background-color: #353743 !important;
}

a.bg-sidebar-dark:hover, a.bg-sidebar-dark:focus,
button.bg-sidebar-dark:hover,
button.bg-sidebar-dark:focus {
  background-color: #1e2027 !important;
}

.bg-gd-primary {
  background: #45619d linear-gradient(135deg, #45619d 0%, #6f89c0 100%) !important;
}

.btn-link {
  color: #45619d;
}
.btn-link:hover {
  color: #263556;
}

.btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #45619d;
  --bs-btn-border-color: #45619d;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #374e7e;
  --bs-btn-hover-border-color: #344976;
  --bs-btn-focus-shadow-rgb: 97, 121, 172;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #374e7e;
  --bs-btn-active-border-color: #344976;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #45619d;
  --bs-btn-disabled-border-color: #45619d;
}

.btn-outline-primary {
  --bs-btn-color: #45619d;
  --bs-btn-border-color: #45619d;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #45619d;
  --bs-btn-hover-border-color: #45619d;
  --bs-btn-focus-shadow-rgb: 69, 97, 157;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #45619d;
  --bs-btn-active-border-color: #45619d;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #45619d;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #45619d;
  --bs-gradient: none;
}

.btn-alt-primary {
  --bs-btn-color: #293a5e;
  --bs-btn-bg: #d1d8e7;
  --bs-btn-border-color: #d1d8e7;
  --bs-btn-hover-color: #1c273f;
  --bs-btn-hover-bg: #a2b0ce;
  --bs-btn-hover-border-color: #a2b0ce;
  --bs-btn-focus-shadow-rgb: 184, 192, 210;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #dae0ec;
  --bs-btn-active-border-color: #d6dce9;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #d1d8e7;
  --bs-btn-disabled-border-color: #d1d8e7;
}

.btn-alt-secondary {
  --bs-btn-color: #212529;
  --bs-btn-bg: #f2f3f7;
  --bs-btn-border-color: #f2f3f7;
  --bs-btn-hover-color: #1c1f23;
  --bs-btn-hover-bg: #dadbde;
  --bs-btn-hover-border-color: #dadbde;
  --bs-btn-focus-shadow-rgb: 211, 212, 216;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #f5f5f9;
  --bs-btn-active-border-color: #f3f4f8;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #f2f3f7;
  --bs-btn-disabled-border-color: #f2f3f7;
}

.page-header-dark #page-header .btn-alt-secondary,
.sidebar-dark #sidebar .btn-alt-secondary,
#sidebar .bg-header-dark .content-header .btn-alt-secondary,
.page-header-dark.page-header-glass:not(.page-header-scroll) #page-header .btn-alt-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #5b709e;
  --bs-btn-border-color: #5b709e;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #6e81aa;
  --bs-btn-hover-border-color: #6e81aa;
  --bs-btn-focus-shadow-rgb: 116, 133, 173;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #495a7e;
  --bs-btn-active-border-color: #445477;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #5b709e;
  --bs-btn-disabled-border-color: #5b709e;
}

.alert-primary {
  --bs-alert-color: #354b7a;
  --bs-alert-bg: #b6c3df;
  --bs-alert-border-color: #b6c3df;
  --bs-alert-link-color: #2a3c62;
}
.alert-primary .alert-link {
  color: var(--bs-alert-link-color);
}

.progress-bar {
  background-color: #45619d;
}

.nav-link:hover, .nav-link:focus {
  color: #45619d;
}

.nav-pills .nav-link {
  color: #45619d;
}
.nav-pills .nav-link:hover, .nav-pills .nav-link:focus {
  background-color: #edf0f7;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: #45619d;
}

.nav-tabs {
  border-bottom-color: #e5e7ef;
}
.nav-tabs .nav-link {
  color: #45619d;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e5e7ef #e5e7ef #e5e7ef;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  border-color: #e5e7ef #e5e7ef #fff;
}

.nav-tabs-block {
  background-color: #f8f9fb;
}
.nav-tabs-block .nav-link {
  border-color: transparent;
}
.nav-tabs-block .nav-link:hover, .nav-tabs-block .nav-link:focus {
  color: #45619d;
  background-color: #edf0f7;
  border-color: transparent;
}
.nav-tabs-block .nav-link.active,
.nav-tabs-block .nav-item.show .nav-link {
  color: #343a40;
  background-color: #fff;
  border-color: transparent;
}

.nav-tabs-alt {
  border-bottom-color: #e5e7ef;
}
.nav-tabs-alt .nav-link {
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs-alt .nav-link:hover, .nav-tabs-alt .nav-link:focus {
  color: #45619d;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #45619d;
}
.nav-tabs-alt .nav-link.active,
.nav-tabs-alt .nav-item.show .nav-link {
  color: #343a40;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #45619d;
}

.nav-items a {
  border-bottom-color: #edf0f7;
}
.nav-items a:hover {
  background-color: #f8f9fb;
}
.nav-items a:active {
  background-color: #edf0f7;
}
.nav-items > li:last-child > a {
  border-bottom: none;
}

.card.card-borderless {
  box-shadow: 0 1px 2px rgba(221, 224, 234, 0.5), 0 1px 2px rgba(221, 224, 234, 0.5);
}
.card > .card-header:not(.bg-transparent),
.card > .card-footer:not(.bg-transparent) {
  background-color: #f8f9fb;
}
.card > .card-header:not(.border-bottom-0),
.card > .card-footer:not(.border-top-0) {
  border-color: #e5e7ef;
}
.card:not(.card-borderless),
.card:not(.card-borderless) > .card-header {
  border-color: #e5e7ef;
}

.page-item.active .page-link {
  background-color: #45619d;
  border-color: #45619d;
}

.page-link {
  color: #343a40;
  background-color: #edf0f7;
  border-color: #edf0f7;
}
.page-link:hover {
  color: #343a40;
  background-color: #c6cadc;
  border-color: #c6cadc;
}
.page-link:focus {
  background-color: #e5e7ef;
  border-color: #e5e7ef;
}

.list-group-item-action {
  color: #343a40;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  color: #343a40;
  background-color: #f8f9fb;
}
.list-group-item-action:active {
  color: #343a40;
  background-color: #e5e7ef;
}

.list-group-item {
  border-color: #e5e7ef;
}
.list-group-item.active {
  color: #fff;
  background-color: #45619d;
  border-color: #45619d;
}

.popover {
  border-color: #e5e7ef;
}

.bs-popover-top .popover-arrow::before, .bs-popover-auto[x-placement^=top] .popover-arrow::before {
  border-top-color: #e5e7ef;
}
.bs-popover-top .popover-arrow::after, .bs-popover-auto[x-placement^=top] .popover-arrow::after {
  border-top-color: #fff;
}

.bs-popover-end .popover-arrow::before, .bs-popover-auto[x-placement^=right] .popover-arrow::before {
  border-right-color: #e5e7ef;
}
.bs-popover-end .popover-arrow::after, .bs-popover-auto[x-placement^=right] .popover-arrow::after {
  border-right-color: #fff;
}

.bs-popover-bottom .popover-arrow::before, .bs-popover-auto[x-placement^=bottom] .popover-arrow::before {
  border-bottom-color: #e5e7ef;
}
.bs-popover-bottom .popover-arrow::after, .bs-popover-auto[x-placement^=bottom] .popover-arrow::after {
  border-bottom-color: #fff;
}

.bs-popover-start .popover-arrow::before, .bs-popover-auto[x-placement^=left] .popover-arrow::before {
  border-left-color: #e5e7ef;
}
.bs-popover-start .popover-arrow::after, .bs-popover-auto[x-placement^=left] .popover-arrow::after {
  border-left-color: #fff;
}

.modal-header {
  border-bottom-color: #e5e7ef;
}

.modal-footer {
  border-top-color: #e5e7ef;
}

.dropdown-menu {
  border-color: #e5e7ef;
}

.dropdown-divider {
  border-top-color: #edf0f7;
}

.dropdown-item {
  color: #343a40;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: #212529;
  background-color: #edf0f7;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  background-color: #45619d;
}

.table {
  --bs-table-striped-bg: #f7f7fa;
  --bs-table-active-bg: #f2f3f7;
  --bs-table-hover-bg: #edeff4;
  border-color: #e5e7ef;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #e5e7ef;
}

.table-primary {
  --bs-table-color: #000;
  --bs-table-bg: #b6c3df;
  --bs-table-border-color: #929cb2;
  --bs-table-striped-bg: #adb9d4;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #a4b0c9;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #a8b4ce;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.form-control,
.form-select {
  color: #343a40;
  background-color: #fff;
  border-color: #d9dce7;
}
.form-control:focus,
.form-select:focus {
  color: #212529;
  background-color: #fff;
  border-color: #92a6cf;
  box-shadow: 0 0 0 0.25rem rgba(69, 97, 157, 0.25);
}
.form-control:disabled,
.form-select:disabled {
  background-color: var(--bs-secondary-bg);
}

.form-select:focus::-ms-value {
  color: #343a40;
  background-color: #fff;
}

.form-control.form-control-alt,
.form-select.form-control-alt {
  border-color: #edf0f7;
  background-color: #edf0f7;
}
.form-control.form-control-alt:focus,
.form-select.form-control-alt:focus {
  border-color: #e5e7ef;
  background-color: #e5e7ef;
  box-shadow: none;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #6f9c40;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(111, 156, 64, 0.9);
  border-radius: var(--bs-border-radius);
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #6f9c40;
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #6f9c40;
  box-shadow: 0 0 0 0.25rem rgba(111, 156, 64, 0.25);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #6f9c40;
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #6f9c40;
  box-shadow: 0 0 0 0.25rem rgba(111, 156, 64, 0.25);
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #6f9c40;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #6f9c40;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(111, 156, 64, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #6f9c40;
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #e04f1a;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(224, 79, 26, 0.9);
  border-radius: var(--bs-border-radius);
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #e04f1a;
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #e04f1a;
  box-shadow: 0 0 0 0.25rem rgba(224, 79, 26, 0.25);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #e04f1a;
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #e04f1a;
  box-shadow: 0 0 0 0.25rem rgba(224, 79, 26, 0.25);
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #e04f1a;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #e04f1a;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(224, 79, 26, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #e04f1a;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.input-group-text {
  color: #343a40;
  background-color: #edf0f7;
  border-color: #d9dce7;
}

.input-group-text.input-group-text-alt {
  background-color: #e5e7ef;
  border-color: #e5e7ef;
}

.form-check-input {
  border-color: #d2d6e4;
}
.form-check-input:focus {
  border-color: #45619d;
  box-shadow: 0 0 0 0.25rem rgba(69, 97, 157, 0.25);
}
.form-check-input:checked {
  background-color: #45619d;
  border-color: #45619d;
}

.form-switch .form-check-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23d2d6e4'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2345619d'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-block .form-check-label {
  border-color: #e5e7ef;
}
.form-block .form-check-label:hover {
  border-color: #d6d9e6;
}
.form-block .form-check-label::before {
  background-color: #45619d;
}
.form-block .form-check-input:checked ~ .form-check-label {
  border-color: #45619d;
}
.form-block .form-check-input:focus ~ .form-check-label {
  border-color: #45619d;
  box-shadow: 0 0 0 0.25rem rgba(69, 97, 157, 0.25);
}
.form-block .form-check-input:disabled:not([checked]) + .form-check-label:hover,
.form-block .form-check-input[readonly]:not([checked]) + .form-check-label:hover {
  border-color: #e5e7ef;
}

.border {
  border-color: #e5e7ef !important;
}

.border-top {
  border-top-color: #e5e7ef !important;
}

.border-end {
  border-right-color: #e5e7ef !important;
}

.border-bottom {
  border-bottom-color: #e5e7ef !important;
}

.border-start {
  border-left-color: #e5e7ef !important;
}

.border-primary {
  border-color: #0665d0 !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: #6f9c40 !important;
}

.border-info {
  border-color: #3c90df !important;
}

.border-warning {
  border-color: #e69f17 !important;
}

.border-danger {
  border-color: #e04f1a !important;
}

.border-light {
  border-color: #edf0f7 !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-primary {
  border-color: #45619d !important;
}

.border-white {
  border-color: #fff !important;
}

.border-white-op {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.border-black-op {
  border-color: rgba(0, 0, 0, 0.1) !important;
}

#page-header {
  background-color: #fff;
}

#sidebar {
  background-color: #fff;
}

#side-overlay {
  background-color: #fff;
}

#page-container {
  background-color: #f2f3f7;
}
#page-container.page-header-dark #page-header {
  color: #ccd0e0;
  background-color: #3e578d;
}
#page-container.page-header-glass #page-header {
  background-color: transparent;
}
#page-container.page-header-glass.page-header-fixed.page-header-scroll #page-header {
  background-color: rgba(255, 255, 255, 0.9);
}
#page-container.page-header-glass.page-header-fixed.page-header-scroll.page-header-dark #page-header {
  background-color: rgba(62, 87, 141, 0.9);
}
#page-container.sidebar-dark #sidebar {
  color: #e5e7ef;
  background-color: #353743;
}

#sidebar.with-mini-nav .sidebar-mini-nav {
  color: #e5e7ef;
  background-color: #3c3e4c;
}

.block {
  box-shadow: 0 1px 3px rgba(221, 224, 234, 0.5), 0 1px 2px rgba(221, 224, 234, 0.5);
}

.block-header-default {
  background-color: #f8f9fb;
}

.block.block-bordered {
  border-color: #e5e7ef;
}
.block.block-themed > .block-header {
  background-color: #45619d;
}

.block.block-mode-loading::after {
  color: #45619d;
}
.block.block-mode-loading.block-mode-loading-dark::after {
  background-color: #354b7a;
}

a.block {
  color: #343a40;
}
a.block:hover {
  color: #343a40;
}
a.block.block-link-pop:hover {
  box-shadow: 0 0.5rem 2rem #d9dce7;
}
a.block.block-link-pop:active {
  box-shadow: 0 0.25rem 0.75rem #eff0f5;
}
a.block.block-link-shadow:hover {
  box-shadow: 0 0 2.25rem #d9dce7;
}
a.block.block-link-shadow:active {
  box-shadow: 0 0 1.125rem #e5e7ef;
}

.block.block-fx-shadow {
  box-shadow: 0 0 2.25rem #d9dce7;
}
.block.block-fx-pop {
  box-shadow: 0 0.5rem 2rem #d9dce7;
}

.btn-block-option {
  color: #45619d;
}
.btn-block-option:hover {
  color: #6f89c0;
}
a.btn-block-option:focus,
.active > a.btn-block-option,
.show > button.btn-block-option {
  color: #6f89c0;
}

.btn-block-option:active {
  color: #b6c3df;
}

#page-loader {
  background-color: #45619d;
}

.nav-main-heading {
  color: #6d7a86;
}

.nav-main-link {
  color: #3f474e;
}
.nav-main-link .nav-main-link-icon {
  color: rgba(69, 97, 157, 0.7);
}
.nav-main-link:hover, .nav-main-link.active {
  color: #000;
  background-color: #e4e9f3;
}

.nav-main-submenu {
  background-color: #f2f4f9;
}
.nav-main-submenu .nav-main-link {
  color: #626d78;
}
.nav-main-submenu .nav-main-link:hover, .nav-main-submenu .nav-main-link.active {
  color: #23272b;
  background-color: transparent;
}

.nav-main-item.open > .nav-main-link-submenu {
  color: #000;
  background-color: #e4e9f3;
}

.nav-main-submenu .nav-main-item.open .nav-main-link {
  background-color: transparent;
}

@media (min-width: 992px) {
  .nav-main-horizontal.nav-main-hover .nav-main-item:hover > .nav-main-link-submenu {
    color: #000;
    background-color: #e4e9f3;
  }
}
.nav-main-dark .nav-main-heading,
.sidebar-dark #sidebar .nav-main-heading,
.page-header-dark #page-header .nav-main-heading,
.dark-mode #side-overlay .nav-main-heading,
.dark-mode #main-container .nav-main-heading {
  color: #7b7f96;
}
.nav-main-dark .nav-main-link,
.sidebar-dark #sidebar .nav-main-link,
.page-header-dark #page-header .nav-main-link,
.dark-mode #side-overlay .nav-main-link,
.dark-mode #main-container .nav-main-link {
  color: #c2c4cf;
}
.nav-main-dark .nav-main-link > .nav-main-link-icon,
.sidebar-dark #sidebar .nav-main-link > .nav-main-link-icon,
.page-header-dark #page-header .nav-main-link > .nav-main-link-icon,
.dark-mode #side-overlay .nav-main-link > .nav-main-link-icon,
.dark-mode #main-container .nav-main-link > .nav-main-link-icon {
  color: #62667c;
}
.nav-main-dark .nav-main-link:hover, .nav-main-dark .nav-main-link.active,
.sidebar-dark #sidebar .nav-main-link:hover,
.sidebar-dark #sidebar .nav-main-link.active,
.page-header-dark #page-header .nav-main-link:hover,
.page-header-dark #page-header .nav-main-link.active,
.dark-mode #side-overlay .nav-main-link:hover,
.dark-mode #side-overlay .nav-main-link.active,
.dark-mode #main-container .nav-main-link:hover,
.dark-mode #main-container .nav-main-link.active {
  color: #fff;
  background-color: #2b2c36;
}
.nav-main-dark .nav-main-submenu,
.sidebar-dark #sidebar .nav-main-submenu,
.page-header-dark #page-header .nav-main-submenu,
.dark-mode #side-overlay .nav-main-submenu,
.dark-mode #main-container .nav-main-submenu {
  background-color: #2e303a;
}
.nav-main-dark .nav-main-submenu .nav-main-link,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-link,
.page-header-dark #page-header .nav-main-submenu .nav-main-link,
.dark-mode #side-overlay .nav-main-submenu .nav-main-link,
.dark-mode #main-container .nav-main-submenu .nav-main-link {
  color: #a5a8b8;
}
.nav-main-dark .nav-main-submenu .nav-main-link:hover, .nav-main-dark .nav-main-submenu .nav-main-link.active,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-link:hover,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-link.active,
.page-header-dark #page-header .nav-main-submenu .nav-main-link:hover,
.page-header-dark #page-header .nav-main-submenu .nav-main-link.active,
.dark-mode #side-overlay .nav-main-submenu .nav-main-link:hover,
.dark-mode #side-overlay .nav-main-submenu .nav-main-link.active,
.dark-mode #main-container .nav-main-submenu .nav-main-link:hover,
.dark-mode #main-container .nav-main-submenu .nav-main-link.active {
  color: #fff;
  background-color: transparent;
}
.nav-main-dark .nav-main-item.open > .nav-main-link-submenu,
.sidebar-dark #sidebar .nav-main-item.open > .nav-main-link-submenu,
.page-header-dark #page-header .nav-main-item.open > .nav-main-link-submenu,
.dark-mode #side-overlay .nav-main-item.open > .nav-main-link-submenu,
.dark-mode #main-container .nav-main-item.open > .nav-main-link-submenu {
  color: #fff;
  background-color: #2b2c36;
}
.nav-main-dark .nav-main-item.open > .nav-main-submenu,
.sidebar-dark #sidebar .nav-main-item.open > .nav-main-submenu,
.page-header-dark #page-header .nav-main-item.open > .nav-main-submenu,
.dark-mode #side-overlay .nav-main-item.open > .nav-main-submenu,
.dark-mode #main-container .nav-main-item.open > .nav-main-submenu {
  background-color: #2e303a;
}
.nav-main-dark .nav-main-submenu .nav-main-item.open .nav-main-link,
.sidebar-dark #sidebar .nav-main-submenu .nav-main-item.open .nav-main-link,
.page-header-dark #page-header .nav-main-submenu .nav-main-item.open .nav-main-link,
.dark-mode #side-overlay .nav-main-submenu .nav-main-item.open .nav-main-link,
.dark-mode #main-container .nav-main-submenu .nav-main-item.open .nav-main-link {
  background-color: transparent;
}

@media (min-width: 992px) {
  .nav-main-dark.nav-main-horizontal .nav-main-heading,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-heading,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-heading,
  .dark-mode #main-container .nav-main-horizontal .nav-main-heading {
    color: rgba(255, 255, 255, 0.5);
  }
  .nav-main-dark.nav-main-horizontal .nav-main-link,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link {
    color: rgba(255, 255, 255, 0.75);
  }
  .nav-main-dark.nav-main-horizontal .nav-main-link > .nav-main-link-icon,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link > .nav-main-link-icon,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link > .nav-main-link-icon,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link > .nav-main-link-icon {
    color: rgba(255, 255, 255, 0.4);
  }
  .nav-main-dark.nav-main-horizontal .nav-main-link:hover, .nav-main-dark.nav-main-horizontal .nav-main-link.active,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link:hover,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-link.active,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link:hover,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link.active,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link:hover,
  .dark-mode #main-container .nav-main-horizontal .nav-main-link.active {
    color: #fff;
    background-color: #2a2b35;
  }
  .nav-main-dark.nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .nav-main-dark.nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu {
    color: #fff;
    background-color: #2a2b35;
  }
  .nav-main-dark.nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .nav-main-dark.nav-main-horizontal .nav-main-item:hover > .nav-main-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .dark-mode #main-container .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu {
    background-color: #2a2b35;
  }
  .nav-main-dark.nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link,
  .sidebar-dark #sidebar .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link,
  .dark-mode #main-container .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link {
    background-color: transparent;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-link:hover, .page-header-dark #page-header .nav-main-horizontal .nav-main-link.active {
    background-color: #364c7b;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-link-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-link-submenu {
    background-color: #364c7b;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item.open > .nav-main-submenu,
  .page-header-dark #page-header .nav-main-horizontal .nav-main-item:hover > .nav-main-submenu {
    background-color: #364c7b;
  }
  .page-header-dark #page-header .nav-main-horizontal .nav-main-submenu .nav-main-item:hover .nav-main-link {
    background-color: transparent;
  }
}
.nav-items a {
  border-bottom-color: #edf0f7;
}
.nav-items a:hover {
  background-color: #f8f9fb;
}

.mini-nav-item {
  color: #b6bcd2;
}
.mini-nav-item.active {
  background-color: #434554;
  color: #fff;
}
.mini-nav-item:hover {
  color: #fff;
  background-color: #434554;
}
.mini-nav-item:active {
  color: #b6bcd2;
}

.list-activity > li {
  border-bottom-color: #edf0f7;
}

.timeline-event-icon {
  box-shadow: 0 0.375rem 1.5rem #d9dce7;
}

.ribbon-light .ribbon-box {
  color: #343a40;
  background-color: #e5e7ef;
}
.ribbon-light.ribbon-bookmark .ribbon-box::before {
  border-color: #e5e7ef;
  border-left-color: transparent;
}
.ribbon-light.ribbon-bookmark.ribbon-left .ribbon-box::before {
  border-color: #e5e7ef;
  border-right-color: transparent;
}

.ribbon-primary .ribbon-box {
  color: #fff;
  background-color: #45619d;
}
.ribbon-primary.ribbon-bookmark .ribbon-box::before {
  border-color: #45619d;
  border-left-color: transparent;
}
.ribbon-primary.ribbon-bookmark.ribbon-left .ribbon-box::before {
  border-color: #45619d;
  border-right-color: transparent;
}

.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:focus,
.datepicker table tr td.active:hover:focus,
.datepicker table tr td.active.disabled:focus,
.datepicker table tr td.active.disabled:hover:focus,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td.active,
.open .dropdown-toggle.datepicker table tr td.active:hover,
.open .dropdown-toggle.datepicker table tr td.active.disabled,
.open .dropdown-toggle.datepicker table tr td.active.disabled:hover,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:focus,
.datepicker table tr td span.active:hover:focus,
.datepicker table tr td span.active.disabled:focus,
.datepicker table tr td span.active.disabled:hover:focus,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.open .dropdown-toggle.datepicker table tr td span.active,
.open .dropdown-toggle.datepicker table tr td span.active:hover,
.open .dropdown-toggle.datepicker table tr td span.active.disabled,
.open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
  background-color: #45619d;
  border-color: #45619d;
}

.cke_chrome,
.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.ck.ck-toolbar {
  border-color: #e5e7ef !important;
}

.cke_top,
.ck.ck-toolbar {
  border-bottom-color: #e5e7ef !important;
  background: #f8f9fb !important;
}

.ck.ck-toolbar .ck.ck-toolbar__separator {
  background: #e5e7ef !important;
}

.cke_bottom {
  border-top-color: #e5e7ef !important;
  background: #f8f9fb !important;
}

.dropzone {
  background-color: #f8f9fb;
  border-color: #d9dce7;
}
.dropzone .dz-message {
  color: #343a40;
}
.dropzone:hover {
  background-color: #fff;
  border-color: #45619d;
}
.dropzone:hover .dz-message {
  color: #45619d;
}

.fc.fc-theme-standard a {
  color: #343a40;
}
.fc.fc-theme-standard .fc-button-primary {
  color: #343a40;
  background-color: #e5e7ef;
  border-color: #e5e7ef;
}
.fc.fc-theme-standard .fc-button-primary:not(:disabled):hover {
  color: #343a40;
  background-color: #edf0f7;
  border-color: #edf0f7;
}
.fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active, .fc.fc-theme-standard .fc-button-primary:not(:disabled):active {
  color: #343a40;
  background-color: #f8f9fb;
  border-color: #f8f9fb;
}
.fc.fc-theme-standard .fc-button-primary:focus, .fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active:focus, .fc.fc-theme-standard .fc-button-primary:not(:disabled):active:focus {
  box-shadow: 0 0 0 0.2rem rgba(69, 97, 157, 0.4);
}
.fc.fc-theme-standard th,
.fc.fc-theme-standard td,
.fc.fc-theme-standard .fc-scrollgrid,
.fc.fc-theme-standard .fc-list {
  border-color: #e5e7ef;
}
.fc.fc-theme-standard .fc-h-event {
  background-color: #45619d;
  border: #45619d;
}
.fc.fc-theme-standard .fc-col-header-cell,
.fc.fc-theme-standard .fc-list-day-cushion {
  background-color: #f8f9fb;
}

.irs.irs--round .irs-min,
.irs.irs--round .irs-max,
.irs.irs--round .irs-line,
.irs.irs--round .irs-grid-pol {
  background: #edf0f7;
}
.irs.irs--round .irs-handle {
  border-color: #45619d;
}
.irs.irs--round .irs-from:before,
.irs.irs--round .irs-to:before,
.irs.irs--round .irs-single:before {
  border-top-color: #45619d;
}
.irs.irs--round .irs-bar,
.irs.irs--round .irs-from,
.irs.irs--round .irs-to,
.irs.irs--round .irs-single {
  background: #45619d;
}

.select2-container--default .select2-selection--single {
  border-color: #d9dce7;
}
.select2-container--default .select2-selection--multiple {
  border-color: #d9dce7;
}
.select2-container--default.select2-container--focus .select2-selection--multiple, .select2-container--default.select2-container--focus .select2-selection--single, .select2-container--default.select2-container--open .select2-selection--multiple, .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #92a6cf;
  box-shadow: 0 0 0 0.25rem rgba(69, 97, 157, 0.25);
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #45619d;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #d9dce7;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #45619d;
}
.select2-container--default .select2-dropdown .select2-search__field:focus {
  border-color: #92a6cf;
  box-shadow: 0 0 0 0.25rem rgba(69, 97, 157, 0.25);
}

.simplebar-scrollbar::before {
  background: rgba(22, 31, 51, 0.75);
}

.slick-slider .slick-prev::before,
.slick-slider .slick-next::before {
  color: #354b7a;
}

.editor-toolbar {
  border-color: #e5e7ef;
  background-color: #f8f9fb;
}

.CodeMirror {
  border-color: #e5e7ef;
}

.dd-handle {
  color: #343a40;
  background: #f8f9fb;
  border-color: #e5e7ef;
}
.dd-handle:hover {
  color: #212529;
}

.dd-empty,
.dd-placeholder {
  border-color: #263556;
  background: #b6c3df;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  border-color: #45619d;
  background: #45619d;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: #45619d;
}

.jvectormap-tip {
  background: #354b7a;
}

.jvectormap-zoomin,
.jvectormap-zoomout,
.jvectormap-goback {
  background: #354b7a;
}

table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
  box-shadow: inset 0 0 0 9999px #f7f7fa;
}

#page-container.dark-mode {
  background-color: #23242c;
}

.dark-mode,
.dark-mode #side-overlay,
.dark-mode #page-loader {
  background-color: #23242c;
  color: #a5a8b8;
}
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6,
.dark-mode .h1,
.dark-mode .h2,
.dark-mode .h3,
.dark-mode .h4,
.dark-mode .h5,
.dark-mode .h6 {
  color: #e5e7ef;
}
.dark-mode .content-heading {
  border-bottom-color: #373946;
}
.dark-mode hr {
  border-top-color: #373946;
}
.dark-mode code {
  color: #e685b5;
}
.dark-mode .story p,
.dark-mode p.story {
  color: #898ca2;
}
.dark-mode a:not(.mini-nav-item):not(.badge):not(.btn):not(.btn-block-option):not(.block):not(.dropdown-item):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(.list-group-item-action):not(.close):not(.fc-event):not(.text-success-light):not(.text-danger-light):not(.text-warning-light):not(.text-info-light) {
  color: #6480bb;
}
.dark-mode a:not(.mini-nav-item):not(.badge):not(.btn):not(.btn-block-option):not(.block):not(.dropdown-item):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(.list-group-item-action):not(.close):not(.fc-event):not(.text-success-light):not(.text-danger-light):not(.text-warning-light):not(.text-info-light).link-fx::before {
  background-color: #6480bb;
}
.dark-mode a:not(.mini-nav-item):not(.badge):not(.btn):not(.btn-block-option):not(.block):not(.dropdown-item):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(.list-group-item-action):not(.close):not(.fc-event):not(.text-success-light):not(.text-danger-light):not(.text-warning-light):not(.text-info-light):hover {
  color: #45619d;
}
.dark-mode .bg-body {
  background-color: #23242c !important;
}
.dark-mode a.bg-body:hover, .dark-mode a.bg-body:focus,
.dark-mode button.bg-body:hover,
.dark-mode button.bg-body:focus {
  background-color: #0c0d10 !important;
}
.dark-mode .bg-body-light {
  background-color: #30323d !important;
}
.dark-mode a.bg-body-light:hover, .dark-mode a.bg-body-light:focus,
.dark-mode button.bg-body-light:hover,
.dark-mode button.bg-body-light:focus {
  background-color: #1a1b21 !important;
}
.dark-mode .bg-body-dark {
  background-color: #202128 !important;
}
.dark-mode a.bg-body-dark:hover, .dark-mode a.bg-body-dark:focus,
.dark-mode button.bg-body-dark:hover,
.dark-mode button.bg-body-dark:focus {
  background-color: #09090b !important;
}
.dark-mode .bg-body-extra-light {
  background-color: #2a2b35 !important;
}
.dark-mode a.bg-body-extra-light:hover, .dark-mode a.bg-body-extra-light:focus,
.dark-mode button.bg-body-extra-light:hover,
.dark-mode button.bg-body-extra-light:focus {
  background-color: #131418 !important;
}
.dark-mode .bg-muted {
  background-color: #9aa1c1 !important;
}
.dark-mode a.bg-muted:hover, .dark-mode a.bg-muted:focus,
.dark-mode button.bg-muted:hover,
.dark-mode button.bg-muted:focus {
  background-color: #7a84ad !important;
}
.dark-mode .text-primary {
  color: #6480bb !important;
}
.dark-mode a.text-primary.link-fx::before {
  background-color: #6480bb !important;
}
.dark-mode a.text-primary:hover, .dark-mode a.text-primary:focus {
  color: #4865a4 !important;
}
.dark-mode .text-success {
  color: #7cae47 !important;
}
.dark-mode a.text-success.link-fx::before {
  background-color: #7cae47 !important;
}
.dark-mode a.text-success:hover, .dark-mode a.text-success:focus {
  color: #628a39 !important;
}
.dark-mode .text-warning {
  color: #eaa92d !important;
}
.dark-mode a.text-warning.link-fx::before {
  background-color: #eaa92d !important;
}
.dark-mode a.text-warning:hover, .dark-mode a.text-warning:focus {
  color: #cf8f15 !important;
}
.dark-mode .text-info {
  color: #529ce3 !important;
}
.dark-mode a.text-info.link-fx::before {
  background-color: #529ce3 !important;
}
.dark-mode a.text-info:hover, .dark-mode a.text-info:focus {
  color: #2684db !important;
}
.dark-mode .text-danger {
  color: #e75f2d !important;
}
.dark-mode a.text-danger.link-fx::before {
  background-color: #e75f2d !important;
}
.dark-mode a.text-danger:hover, .dark-mode a.text-danger:focus {
  color: #c94717 !important;
}
.dark-mode .text-body-bg {
  color: #23242c !important;
}
.dark-mode a.text-body-bg.link-fx::before {
  background-color: #23242c !important;
}
.dark-mode a.text-body-bg:hover, .dark-mode a.text-body-bg:focus {
  color: #0c0d10 !important;
}
.dark-mode .text-body-bg-dark {
  color: #1c1d24 !important;
}
.dark-mode a.text-body-bg-dark.link-fx::before {
  background-color: #1c1d24 !important;
}
.dark-mode a.text-body-bg-dark:hover, .dark-mode a.text-body-bg-dark:focus {
  color: #060607 !important;
}
.dark-mode .text-body-bg-light {
  color: #30323d !important;
}
.dark-mode a.text-body-bg-light.link-fx::before {
  background-color: #30323d !important;
}
.dark-mode a.text-body-bg-light:hover, .dark-mode a.text-body-bg-light:focus {
  color: #1a1b21 !important;
}
.dark-mode .text-body-color {
  color: #a5a8b8 !important;
}
.dark-mode a.text-body-color.link-fx::before {
  background-color: #a5a8b8 !important;
}
.dark-mode a.text-body-color:hover, .dark-mode a.text-body-color:focus {
  color: #898ca2 !important;
}
.dark-mode .text-body-color-dark {
  color: #7b7f96 !important;
}
.dark-mode a.text-body-color-dark.link-fx::before {
  background-color: #7b7f96 !important;
}
.dark-mode a.text-body-color-dark:hover, .dark-mode a.text-body-color-dark:focus {
  color: #62667c !important;
}
.dark-mode .text-body-color-light {
  color: #d0d2da !important;
}
.dark-mode a.text-body-color-light.link-fx::before {
  background-color: #d0d2da !important;
}
.dark-mode a.text-body-color-light:hover, .dark-mode a.text-body-color-light:focus {
  color: #b4b6c3 !important;
}
.dark-mode .text-dark {
  color: #a6adc8 !important;
}
.dark-mode a.text-dark.link-fx::before {
  background-color: #a6adc8 !important;
}
.dark-mode a.text-dark:hover, .dark-mode a.text-dark:focus {
  color: #8790b5 !important;
}
.dark-mode .text-muted {
  color: #898ca2 !important;
}
.dark-mode a.text-muted.link-fx::before {
  background-color: #898ca2 !important;
}
.dark-mode a.text-muted:hover, .dark-mode a.text-muted:focus {
  color: #6d718a !important;
}
.dark-mode .btn-secondary,
.dark-mode .btn-alt-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #353743;
  --bs-btn-border-color: #353743;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #1e2027;
  --bs-btn-hover-border-color: #1e2027;
  --bs-btn-focus-shadow-rgb: 83, 85, 95;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #2a2c36;
  --bs-btn-active-border-color: #282932;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #353743;
  --bs-btn-disabled-border-color: #353743;
}
.dark-mode .nav-link {
  color: #c6cadc;
}
.dark-mode .nav-link:hover, .dark-mode .nav-link:focus {
  color: #45619d;
}
.dark-mode .nav-pills .nav-link {
  color: #8790b5;
}
.dark-mode .nav-pills .nav-link:hover, .dark-mode .nav-pills .nav-link:focus {
  background-color: #3c3e4c;
}
.dark-mode .nav-pills .nav-link.active,
.dark-mode .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #45619d;
}
.dark-mode .nav-tabs {
  border-bottom-color: #373946;
}
.dark-mode .nav-tabs .nav-link:hover, .dark-mode .nav-tabs .nav-link:focus {
  border-color: #373946 #373946 #373946;
}
.dark-mode .nav-tabs .nav-link.active,
.dark-mode .nav-tabs .nav-item.show .nav-link {
  color: #c6cadc;
  background-color: transparent;
  border-color: #373946 #373946 #2a2b35;
}
.dark-mode .nav-tabs-block {
  background-color: #373946;
}
.dark-mode .nav-tabs-block .nav-link {
  border-color: transparent;
  color: #c6cadc;
}
.dark-mode .nav-tabs-block .nav-link:hover, .dark-mode .nav-tabs-block .nav-link:focus {
  color: #45619d;
  background-color: #30323d;
  border-color: transparent;
}
.dark-mode .nav-tabs-block .nav-link.active,
.dark-mode .nav-tabs-block .nav-item.show .nav-link {
  color: #c6cadc;
  background-color: #2a2b35;
  border-color: transparent;
}
.dark-mode .nav-tabs-alt {
  border-bottom-color: #30323d;
}
.dark-mode .nav-tabs-alt .nav-link {
  color: #c6cadc;
  background-color: transparent;
  border-color: transparent;
}
.dark-mode .nav-tabs-alt .nav-link:hover, .dark-mode .nav-tabs-alt .nav-link:focus {
  color: #45619d;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #45619d;
}
.dark-mode .nav-tabs-alt .nav-link.active,
.dark-mode .nav-tabs-alt .nav-item.show .nav-link {
  color: #c6cadc;
  background-color: transparent;
  border-color: transparent;
  box-shadow: inset 0 -3px #45619d;
}
.dark-mode .nav-items a:hover {
  background-color: #3c3e4c;
}
.dark-mode .nav-items a:active {
  background-color: transparent;
}
.dark-mode .nav-items > li:not(:last-child) > a {
  border-bottom-color: #373946;
}
.dark-mode .card {
  background-color: #2a2b35;
}
.dark-mode .card.card-borderless {
  box-shadow: 0 1px 2px rgba(33, 34, 41, 0.5), 0 1px 2px rgba(33, 34, 41, 0.5);
}
.dark-mode .card > .card-header:not(.bg-transparent),
.dark-mode .card > .card-footer:not(.bg-transparent) {
  background-color: #373946;
}
.dark-mode .card > .card-header:not(.border-bottom-0),
.dark-mode .card > .card-footer:not(.border-top-0) {
  border-color: #373946;
}
.dark-mode .card:not(.card-borderless),
.dark-mode .card:not(.card-borderless) > .card-header {
  border-color: #373946;
}
.dark-mode .card > .card-header .block-title small {
  color: #9aa1c1;
}
.dark-mode .page-link {
  color: #8790b5;
  background-color: #23242c;
  border-color: #23242c;
}
.dark-mode .page-link:hover {
  color: #8790b5;
  background-color: #1c1d24;
  border-color: #1c1d24;
}
.dark-mode .page-link:focus {
  background-color: #1c1d24;
  border-color: #1c1d24;
}
.dark-mode .page-item.active .page-link {
  background-color: #45619d;
  border-color: #45619d;
}
.dark-mode .page-item.disabled .page-link {
  color: #515c84;
  background-color: transparent;
  border-color: transparent;
}
.dark-mode .list-group-item-action {
  color: #c6cadc;
}
.dark-mode .list-group-item-action:hover, .dark-mode .list-group-item-action:focus {
  color: #c6cadc;
  background-color: #343642;
}
.dark-mode .list-group-item-action:active {
  color: #c6cadc;
  background-color: #23242c;
}
.dark-mode .list-group-item-action.disabled {
  color: #6773a2;
}
.dark-mode .list-group-item {
  color: #c6cadc;
  background-color: #30323d;
  border-color: #202128;
}
.dark-mode .list-group-item.active {
  color: #fff;
  background-color: #45619d;
  border-color: #45619d;
}
.dark-mode .popover {
  border-color: #202128;
  background-color: #30323d;
}
.dark-mode .bs-popover-top .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=top] .popover-arrow::before {
  border-top-color: #202128;
}
.dark-mode .bs-popover-top .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=top] .popover-arrow::after {
  border-top-color: #30323d;
}
.dark-mode .bs-popover-end .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=right] .popover-arrow::before {
  border-right-color: #202128;
}
.dark-mode .bs-popover-end .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=right] .popover-arrow::after {
  border-right-color: #30323d;
}
.dark-mode .bs-popover-bottom .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=bottom] .popover-arrow::before {
  border-bottom-color: #202128;
}
.dark-mode .bs-popover-bottom .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=bottom] .popover-arrow::after {
  border-bottom-color: #30323d;
}
.dark-mode .bs-popover-start .popover-arrow::before, .dark-mode .bs-popover-auto[x-placement^=left] .popover-arrow::before {
  border-left-color: #202128;
}
.dark-mode .bs-popover-start .popover-arrow::after, .dark-mode .bs-popover-auto[x-placement^=left] .popover-arrow::after {
  border-left-color: #30323d;
}
.dark-mode .popover-header {
  color: #e5e7ef;
  background-color: #30323d;
  border-bottom-color: #202128;
}
.dark-mode .popover-body {
  color: #c6cadc;
  background-color: #30323d;
}
.dark-mode .dropdown-menu {
  color: #c6cadc;
  background-color: #30323d;
  border-color: #30323d;
  box-shadow: 0 0.25rem 2rem rgba(0, 0, 0, 0.25);
}
.dark-mode .dropdown-menu .dropdown-item {
  color: #b6bcd2;
}
.dark-mode .dropdown-menu .dropdown-item:hover, .dark-mode .dropdown-menu .dropdown-item:focus {
  color: #c6cadc;
  background-color: #3c3e4c;
}
.dark-mode .dropdown-menu .dropdown-item.active, .dark-mode .dropdown-menu .dropdown-item:active {
  color: #d6d9e6;
  background-color: #454757;
}
.dark-mode .dropdown-menu .dropdown-item.disabled, .dark-mode .dropdown-menu .dropdown-item:disabled {
  color: #515c84;
}
.dark-mode .dropdown-menu .dropdown-divider {
  border-color: #404351;
}
.dark-mode .dropdown-menu .dropdown-item-text {
  color: #c6cadc;
}
.dark-mode .dropdown-menu .dropdown-header {
  color: #969ebf !important;
}
.dark-mode .table {
  --bs-table-color: #a5a8b8;
  --bs-table-bg: #2a2b35;
  --bs-table-striped-color: #f8f9fb;
  --bs-table-striped-bg: #262830;
  --bs-table-active-color: #f8f9fb;
  --bs-table-active-bg: #373946;
  --bs-table-hover-color: #f8f9fb;
  --bs-table-hover-bg: #24252e;
  color: #f8f9fb;
  border-color: #1c1d24;
}
.dark-mode .table > :not(:last-child) > :last-child > * {
  border-bottom-color: #1c1d24;
}
.dark-mode .table-dark {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(32, 33, 40, 0.75);
  --bs-table-border-color: rgba(98, 98, 103, 0.8);
  --bs-table-striped-bg: rgba(50, 51, 57, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(67, 68, 74, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(59, 59, 66, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-primary {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(55, 78, 126, 0.75);
  --bs-table-border-color: rgba(114, 130, 164, 0.8);
  --bs-table-striped-bg: rgba(71, 92, 136, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(86, 106, 146, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(79, 99, 141, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-info {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(30, 58, 138, 0.75);
  --bs-table-border-color: rgba(96, 116, 172, 0.8);
  --bs-table-striped-bg: rgba(48, 74, 147, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(65, 89, 156, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(57, 81, 152, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-success {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(20, 83, 45, 0.75);
  --bs-table-border-color: rgba(89, 134, 107, 0.8);
  --bs-table-striped-bg: rgba(39, 97, 62, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(57, 110, 78, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(48, 103, 70, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-danger {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(127, 29, 29, 0.75);
  --bs-table-border-color: rgba(165, 95, 95, 0.8);
  --bs-table-striped-bg: rgba(137, 47, 47, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(147, 64, 64, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(142, 56, 56, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .table-warning {
  --bs-table-color: #fff;
  --bs-table-bg: rgba(113, 63, 18, 0.75);
  --bs-table-border-color: rgba(155, 119, 88, 0.8);
  --bs-table-striped-bg: rgba(124, 78, 37, 0.7625);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgba(135, 93, 55, 0.775);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgba(130, 86, 46, 0.76875);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.dark-mode .form-text {
  color: #7b7f96;
}
.dark-mode .form-control::-moz-placeholder {
  color: #7b7f96;
}
.dark-mode .form-control::placeholder {
  color: #7b7f96;
}
.dark-mode .form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.dark-mode .form-floating > .form-control::placeholder {
  color: transparent;
}
.dark-mode .form-control,
.dark-mode .form-select {
  color: #c6cadc;
  background-color: #202128;
  border-color: #3c3e4c;
}
.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
  color: #fff;
  background-color: #202128;
  border-color: #45619d;
}
.dark-mode .form-control:disabled,
.dark-mode .form-select:disabled {
  background-color: #2e303a;
  border-color: #2e303a;
}
.dark-mode .form-select:focus::-ms-value {
  color: #c6cadc;
  background-color: #202128;
}
.dark-mode .form-control-plaintext {
  color: #c6cadc;
}
.dark-mode .valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #89b956;
}
.dark-mode .valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000;
  background-color: rgba(137, 185, 86, 0.9);
  border-radius: var(--bs-border-radius);
}
.was-validated .dark-mode:valid ~ .valid-feedback,
.was-validated .dark-mode:valid ~ .valid-tooltip, .dark-mode.is-valid ~ .valid-feedback,
.dark-mode.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .dark-mode .form-control:valid, .dark-mode .form-control.is-valid {
  border-color: #89b956;
}
.was-validated .dark-mode .form-control:valid:focus, .dark-mode .form-control.is-valid:focus {
  border-color: #89b956;
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.was-validated .dark-mode .form-select:valid, .dark-mode .form-select.is-valid {
  border-color: #89b956;
}
.was-validated .dark-mode .form-select:valid:focus, .dark-mode .form-select.is-valid:focus {
  border-color: #89b956;
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.was-validated .dark-mode .form-check-input:valid, .dark-mode .form-check-input.is-valid {
  border-color: #89b956;
}
.was-validated .dark-mode .form-check-input:valid:checked, .dark-mode .form-check-input.is-valid:checked {
  background-color: #89b956;
}
.was-validated .dark-mode .form-check-input:valid:focus, .dark-mode .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.was-validated .dark-mode .form-check-input:valid ~ .form-check-label, .dark-mode .form-check-input.is-valid ~ .form-check-label {
  color: #89b956;
}
.dark-mode .form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}
.was-validated .dark-mode .input-group > .form-control:not(:focus):valid, .dark-mode .input-group > .form-control:not(:focus).is-valid,
.was-validated .dark-mode .input-group > .form-select:not(:focus):valid,
.dark-mode .input-group > .form-select:not(:focus).is-valid,
.was-validated .dark-mode .input-group > .form-floating:not(:focus-within):valid,
.dark-mode .input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}
.dark-mode .invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #ec815b;
}
.dark-mode .invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000;
  background-color: rgba(236, 129, 91, 0.9);
  border-radius: var(--bs-border-radius);
}
.was-validated .dark-mode:invalid ~ .invalid-feedback,
.was-validated .dark-mode:invalid ~ .invalid-tooltip, .dark-mode.is-invalid ~ .invalid-feedback,
.dark-mode.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .dark-mode .form-control:invalid, .dark-mode .form-control.is-invalid {
  border-color: #ec815b;
}
.was-validated .dark-mode .form-control:invalid:focus, .dark-mode .form-control.is-invalid:focus {
  border-color: #ec815b;
  box-shadow: 0 0 0 0.25rem rgba(236, 129, 91, 0.25);
}
.was-validated .dark-mode .form-select:invalid, .dark-mode .form-select.is-invalid {
  border-color: #ec815b;
}
.was-validated .dark-mode .form-select:invalid:focus, .dark-mode .form-select.is-invalid:focus {
  border-color: #ec815b;
  box-shadow: 0 0 0 0.25rem rgba(236, 129, 91, 0.25);
}
.was-validated .dark-mode .form-check-input:invalid, .dark-mode .form-check-input.is-invalid {
  border-color: #ec815b;
}
.was-validated .dark-mode .form-check-input:invalid:checked, .dark-mode .form-check-input.is-invalid:checked {
  background-color: #ec815b;
}
.was-validated .dark-mode .form-check-input:invalid:focus, .dark-mode .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(236, 129, 91, 0.25);
}
.was-validated .dark-mode .form-check-input:invalid ~ .form-check-label, .dark-mode .form-check-input.is-invalid ~ .form-check-label {
  color: #ec815b;
}
.dark-mode .form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}
.was-validated .dark-mode .input-group > .form-control:not(:focus):invalid, .dark-mode .input-group > .form-control:not(:focus).is-invalid,
.was-validated .dark-mode .input-group > .form-select:not(:focus):invalid,
.dark-mode .input-group > .form-select:not(:focus).is-invalid,
.was-validated .dark-mode .input-group > .form-floating:not(:focus-within):invalid,
.dark-mode .input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}
.dark-mode .form-control.form-control-alt {
  color: #c6cadc;
  border-color: #23242c;
  background-color: #23242c;
}
.dark-mode .form-control.form-control-alt:focus {
  color: #fff;
  border-color: #202128;
  background-color: #202128;
  box-shadow: none;
}
.dark-mode .form-control.form-control-alt.is-valid {
  border-color: rgba(20, 83, 45, 0.85);
  background-color: rgba(20, 83, 45, 0.85);
}
.dark-mode .form-control.form-control-alt.is-valid::-moz-placeholder {
  color: #7b7f96;
}
.dark-mode .form-control.form-control-alt.is-valid::placeholder {
  color: #7b7f96;
}
.dark-mode .form-control.form-control-alt.is-valid:focus {
  border-color: #14532d;
  background-color: #14532d;
}
.dark-mode .form-control.form-control-alt.is-invalid {
  border-color: rgba(127, 29, 29, 0.85);
  background-color: rgba(127, 29, 29, 0.85);
}
.dark-mode .form-control.form-control-alt.is-invalid::-moz-placeholder {
  color: #7b7f96;
}
.dark-mode .form-control.form-control-alt.is-invalid::placeholder {
  color: #7b7f96;
}
.dark-mode .form-control.form-control-alt.is-invalid:focus {
  border-color: #7f1d1d;
  background-color: #7f1d1d;
}
.dark-mode .input-group-text {
  color: #c6cadc;
  background-color: #23242c;
  border-color: #3c3e4c;
}
.dark-mode .input-group-text.input-group-text-alt {
  background-color: #202128;
  border-color: #202128;
}
.dark-mode .is-valid ~ .valid-feedback,
.dark-mode .is-valid ~ .valid-tooltip,
.dark-mode .was-validated :valid ~ .valid-feedback,
.dark-mode .was-validated :valid ~ .valid-tooltip,
.dark-mode .is-invalid ~ .invalid-feedback,
.dark-mode .is-invalid ~ .invalid-tooltip,
.dark-mode .was-validated :invalid ~ .invalid-feedback,
.dark-mode .was-validated :invalid ~ .invalid-tooltip {
  display: block;
}
.dark-mode .form-check-input {
  background-color: #23242c;
  border-color: #3c3e4c;
}
.dark-mode .form-check-input:focus {
  border-color: #45619d;
}
.dark-mode .form-check-input:checked {
  background-color: #45619d;
  border-color: #45619d;
}
.dark-mode .form-block .form-check-label {
  border-color: #3c3e4c;
}
.dark-mode .form-block .form-check-label:hover {
  border-color: #3f4250;
}
.dark-mode .form-block .form-check-label::before {
  background-color: #45619d;
}
.dark-mode .form-block .form-check-input:checked ~ .form-check-label {
  border-color: #45619d;
}
.dark-mode .form-block .form-check-input:focus ~ .form-check-label {
  border-color: #45619d;
  box-shadow: 0 0 0 0.25rem rgba(69, 97, 157, 0.25);
}
.dark-mode .form-block .form-check-input:disabled:not([checked]) + .form-check-label:hover,
.dark-mode .form-block .form-check-input[readonly]:not([checked]) + .form-check-label:hover {
  border-color: #202128;
}
.dark-mode .breadcrumb-item + .breadcrumb-item::before {
  color: rgba(255, 255, 255, 0.15);
}
.dark-mode .breadcrumb.breadcrumb-alt .breadcrumb-item + .breadcrumb-item::before {
  color: rgba(255, 255, 255, 0.15);
}
.dark-mode .breadcrumb-item.active {
  color: #fff;
}
.dark-mode .alert-primary {
  --bs-alert-color: #fff;
  --bs-alert-bg: #45619d;
  --bs-alert-border-color: #45619d;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-primary .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-secondary {
  --bs-alert-color: #fff;
  --bs-alert-bg: #373946;
  --bs-alert-border-color: #373946;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-secondary .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-success {
  --bs-alert-color: #fff;
  --bs-alert-bg: #537530;
  --bs-alert-border-color: #537530;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-success .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-info {
  --bs-alert-color: #fff;
  --bs-alert-bg: #2d6ca7;
  --bs-alert-border-color: #2d6ca7;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-info .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-warning {
  --bs-alert-color: #fff;
  --bs-alert-bg: #ad7711;
  --bs-alert-border-color: #ad7711;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-warning .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-danger {
  --bs-alert-color: #fff;
  --bs-alert-bg: #a83b14;
  --bs-alert-border-color: #a83b14;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-danger .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-dark {
  --bs-alert-color: #fff;
  --bs-alert-bg: #1c1d24;
  --bs-alert-border-color: #1c1d24;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-dark .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .alert-light {
  --bs-alert-color: #fff;
  --bs-alert-bg: #454757;
  --bs-alert-border-color: #454757;
  --bs-alert-link-color: #cccccc;
}
.dark-mode .alert-light .alert-link {
  color: var(--bs-alert-link-color);
}
.dark-mode .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}
.dark-mode .progress {
  background-color: #202128;
}
.dark-mode .list-activity > li:not(:last-child) {
  border-bottom-color: #202128;
}
.dark-mode .modal-header {
  border-bottom-color: #373946;
}
.dark-mode .modal-content {
  background: #2a2b35;
}
.dark-mode .modal-footer {
  border-top-color: #373946;
}
.dark-mode .toast {
  background-color: #2a2b35;
}
.dark-mode .toast-header {
  color: #c6cadc;
  background-color: #373946;
}
.dark-mode .border {
  border-color: #373946 !important;
}
.dark-mode .border-top {
  border-top-color: #373946 !important;
}
.dark-mode .border-end {
  border-right-color: #373946 !important;
}
.dark-mode .border-bottom {
  border-bottom-color: #373946 !important;
}
.dark-mode .border-start {
  border-left-color: #373946 !important;
}
.dark-mode .border-primary {
  border-color: #45619d !important;
}
.dark-mode .border-white {
  border-color: #fff !important;
}
.dark-mode .border-white-op {
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.dark-mode .border-black-op {
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.dark-mode .block {
  background-color: #2a2b35;
  box-shadow: 0 1px 2px rgba(33, 34, 41, 0.5), 0 1px 2px rgba(33, 34, 41, 0.5);
}
.dark-mode .block.block-bordered {
  border: 1px solid #373946;
  box-shadow: none;
}
.dark-mode .block .block-header-default {
  background-color: #373946 !important;
}
.dark-mode .block .block-title small {
  color: #9aa1c1;
}
.dark-mode .block.block-mode-loading::before {
  background-color: rgba(55, 57, 70, 0.85);
}
.dark-mode .block.block-mode-loading::after {
  color: #fff;
}
.dark-mode .block.block-transparent {
  background-color: transparent;
  box-shadow: none;
}
.dark-mode .block.block-mode-fullscreen.block-transparent {
  background-color: #2a2b35;
}
.dark-mode .block .block,
.dark-mode .content-side .block {
  box-shadow: none;
}
.dark-mode a.block {
  color: #c6cadc;
}
.dark-mode a.block.block-link-pop:hover {
  box-shadow: 0 0.5rem 2.5rem #18191e;
}
.dark-mode a.block.block-link-pop:active {
  box-shadow: 0 0.375rem 0.55rem #272932;
}
.dark-mode a.block.block-link-shadow:hover {
  box-shadow: 0 0 1.5rem #18191e;
}
.dark-mode a.block.block-link-shadow:active {
  box-shadow: 0 0 0.75rem #1e2027;
}
.dark-mode .btn-block-option {
  color: #9aa1c1;
}
.block-header-default .dark-mode .btn-block-option {
  color: #8a93b7;
}
.dark-mode .btn-block-option:hover, .dark-mode .btn-block-option:focus {
  color: #7a84ad;
}
.dark-mode .btn-block-option:active {
  color: #9aa1c1;
}
.dark-mode a.btn-block-option:focus,
.dark-mode .active > a.btn-block-option,
.dark-mode .show > button.btn-block-option {
  color: #7a84ad;
}
.dark-mode .block.block-themed .btn-block-option,
.dark-mode .block.block-themed .btn-block-option:hover,
.dark-mode .block.block-themed .btn-block-option:focus,
.dark-mode .block.block-themed .btn-block-option:active,
.dark-mode .block.block-themed a.btn-block-option:focus,
.dark-mode .block.block-themed .active > a.btn-block-option,
.dark-mode .block.block-themed .show > button.btn-block-option {
  color: #fff;
}
.dark-mode .timeline::before {
  background-color: #373946;
}
.dark-mode .timeline-event-icon {
  border-color: #373946;
  box-shadow: 0 0.375rem 1.5rem #2a2b35;
}
.dark-mode .timeline-event-icon::before {
  border-left-color: #373946;
}
@media (min-width: 1200px) {
  .dark-mode .timeline-centered .timeline-event-time {
    background-color: #373946;
  }
  .dark-mode .timeline-centered .timeline-event-icon::before {
    border-right-color: #373946;
  }
  .dark-mode .timeline-centered.timeline-alt .timeline-event:nth-child(even) .timeline-event-icon::before,
  .dark-mode .timeline-centered .timeline-event.timeline-event-alt .timeline-event-icon::before {
    border-left-color: #373946;
  }
}
.dark-mode .img-thumb {
  background-color: #1c1d24;
}
.dark-mode .swal2-popup {
  background-color: #2a2b35;
}
.dark-mode .swal2-html-container {
  color: #c6cadc;
}
.dark-mode .jvectormap-container {
  background-color: #2a2b35 !important;
}
.dark-mode .ck.ck-editor {
  color: #343a40;
}
.dark-mode .dropzone {
  background-color: #30323d;
  border-color: #373946;
}
.dark-mode .dropzone .dz-message {
  color: #c6cadc;
}
.dark-mode .dropzone:hover {
  background-color: #373946;
  border-color: #45619d;
}
.dark-mode .dropzone:hover .dz-message {
  color: #45619d;
}
.dark-mode .dropzone .dz-preview.dz-image-preview {
  background-color: transparent;
}
.dark-mode .fc.fc-theme-standard a {
  color: #b6bcd2 !important;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary {
  color: #b6bcd2;
  background-color: #202128;
  border-color: #202128;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled):hover {
  color: #c6cadc;
  background-color: #30323d;
  border-color: #30323d;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active, .dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled):active {
  color: #c6cadc;
  background-color: #30323d;
  border-color: #30323d;
}
.dark-mode .fc.fc-theme-standard .fc-button-primary:focus, .dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled).fc-button-active:focus, .dark-mode .fc.fc-theme-standard .fc-button-primary:not(:disabled):active:focus {
  box-shadow: 0 0 0 0.2rem rgba(69, 97, 157, 0.4);
}
.dark-mode .fc.fc-theme-standard .fc-list, .dark-mode .fc.fc-theme-standard .fc-scrollgrid,
.dark-mode .fc.fc-theme-standard th,
.dark-mode .fc.fc-theme-standard td {
  border-color: #1c1d24;
}
.dark-mode .fc.fc-theme-standard .fc-list-day-cushion,
.dark-mode .fc.fc-theme-standard .fc-col-header-cell {
  background-color: #30323d;
}
.dark-mode .fc.fc-theme-standard .fc-list-event:hover td {
  background-color: #24252e;
}
.dark-mode .irs,
.dark-mode .irs--round .irs-grid-text {
  color: #c6cadc;
}
.dark-mode .irs.irs--round .irs-min,
.dark-mode .irs.irs--round .irs-max,
.dark-mode .irs.irs--round .irs-line,
.dark-mode .irs.irs--round .irs-grid-pol,
.dark-mode .irs.irs--round .irs-handle {
  color: #c6cadc;
  background: #23242c;
}
.dark-mode .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #7b7f96;
}
.dark-mode .select2-container--default .select2-selection--single,
.dark-mode .select2-container--default .select2-selection--multiple {
  background-color: #202128;
  border-color: #3c3e4c;
}
.dark-mode .select2-container--default.select2-container--focus .select2-selection--multiple, .dark-mode .select2-container--default.select2-container--focus .select2-selection--single, .dark-mode .select2-container--default.select2-container--open .select2-selection--multiple, .dark-mode .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #45619d;
}
.dark-mode .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #c6cadc;
}
.dark-mode .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #3c3e4c;
}
.dark-mode .select2-container--default .select2-dropdown .select2-search__field:focus {
  border-color: #45619d;
}
.dark-mode .select2-container--default .select2-dropdown {
  background-color: #202128;
  border-color: #3c3e4c;
}
.dark-mode .select2-container--default .select2-search--dropdown .select2-search__field {
  color: #c6cadc;
  background-color: #202128;
  border-color: #3c3e4c;
}
.dark-mode .select2-container--default .select2-results__option[aria-selected=true] {
  color: #fff;
  background-color: #45619d;
}
.dark-mode .select2-container--default .select2-search__field::-moz-placeholder {
  color: #7b7f96;
}
.dark-mode .select2-container--default .select2-search__field::placeholder {
  color: #7b7f96;
}
.dark-mode .is-valid + .select2-container--default .select2-selection--single,
.dark-mode .is-valid + .select2-container--default .select2-selection--multiple,
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #89b956;
}
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-valid + .select2-container--default.select2-container--focus .select2-selection--multiple,
.dark-mode .is-valid + .select2-container--default.select2-container--open .select2-selection--single,
.dark-mode .is-valid + .select2-container--default.select2-container--open .select2-selection--multiple {
  box-shadow: 0 0 0 0.25rem rgba(137, 185, 86, 0.25);
}
.dark-mode .is-invalid + .select2-container--default .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default .select2-selection--multiple,
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #e97044;
}
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default.select2-container--focus .select2-selection--multiple,
.dark-mode .is-invalid + .select2-container--default.select2-container--open .select2-selection--single,
.dark-mode .is-invalid + .select2-container--default.select2-container--open .select2-selection--multiple {
  box-shadow: 0 0 0 0.25rem rgba(233, 112, 68, 0.25);
}
.dark-mode .datepicker .datepicker-switch:hover,
.dark-mode .datepicker .next:hover,
.dark-mode .datepicker .prev:hover,
.dark-mode .datepicker tfoot tr th:hover,
.dark-mode .datepicker table tr td.day:hover,
.dark-mode .datepicker table tr td.focused {
  background-color: #202128;
}
.dark-mode .datepicker table tr td.selected,
.dark-mode .datepicker table tr td.selected.highlighted {
  color: #fff;
  background-color: #202128;
  border-color: #202128;
}
.dark-mode .datepicker table tr td.range {
  color: #c6cadc;
  background-color: #202128;
  border-color: #202128;
}
.dark-mode .datepicker table tr td.active:hover,
.dark-mode .datepicker table tr td.active:hover:hover,
.dark-mode .datepicker table tr td.active.disabled:hover,
.dark-mode .datepicker table tr td.active.disabled:hover:hover,
.dark-mode .datepicker table tr td.active:focus,
.dark-mode .datepicker table tr td.active:hover:focus,
.dark-mode .datepicker table tr td.active.disabled:focus,
.dark-mode .datepicker table tr td.active.disabled:hover:focus,
.dark-mode .datepicker table tr td.active:active,
.dark-mode .datepicker table tr td.active:hover:active,
.dark-mode .datepicker table tr td.active.disabled:active,
.dark-mode .datepicker table tr td.active.disabled:hover:active,
.dark-mode .datepicker table tr td.active.active,
.dark-mode .datepicker table tr td.active:hover.active,
.dark-mode .datepicker table tr td.active.disabled.active,
.dark-mode .datepicker table tr td.active.disabled:hover.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active:hover,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active.disabled,
.dark-mode .open .dropdown-toggle.datepicker table tr td.active.disabled:hover,
.dark-mode .datepicker table tr td span.active:hover,
.dark-mode .datepicker table tr td span.active:hover:hover,
.dark-mode .datepicker table tr td span.active.disabled:hover,
.dark-mode .datepicker table tr td span.active.disabled:hover:hover,
.dark-mode .datepicker table tr td span.active:focus,
.dark-mode .datepicker table tr td span.active:hover:focus,
.dark-mode .datepicker table tr td span.active.disabled:focus,
.dark-mode .datepicker table tr td span.active.disabled:hover:focus,
.dark-mode .datepicker table tr td span.active:active,
.dark-mode .datepicker table tr td span.active:hover:active,
.dark-mode .datepicker table tr td span.active.disabled:active,
.dark-mode .datepicker table tr td span.active.disabled:hover:active,
.dark-mode .datepicker table tr td span.active.active,
.dark-mode .datepicker table tr td span.active:hover.active,
.dark-mode .datepicker table tr td span.active.disabled.active,
.dark-mode .datepicker table tr td span.active.disabled:hover.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active:hover,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active.disabled,
.dark-mode .open .dropdown-toggle.datepicker table tr td span.active.disabled:hover {
  background-color: #45619d;
  border-color: #45619d;
}
.dark-mode .flatpickr-input.form-control:disabled,
.dark-mode .flatpickr-input.form-control[readonly],
.dark-mode .input.form-control:disabled,
.dark-mode .input.form-control[readonly] {
  color: #c6cadc;
  background-color: #202128;
  border-color: #3c3e4c;
}
.dark-mode .flatpickr-day.selected,
.dark-mode .flatpickr-day.startRange,
.dark-mode .flatpickr-day.endRange,
.dark-mode .flatpickr-day.selected.inRange,
.dark-mode .flatpickr-day.startRange.inRange,
.dark-mode .flatpickr-day.endRange.inRange,
.dark-mode .flatpickr-day.selected:focus,
.dark-mode .flatpickr-day.startRange:focus,
.dark-mode .flatpickr-day.endRange:focus,
.dark-mode .flatpickr-day.selected:hover,
.dark-mode .flatpickr-day.startRange:hover,
.dark-mode .flatpickr-day.endRange:hover,
.dark-mode .flatpickr-day.selected.prevMonthDay,
.dark-mode .flatpickr-day.startRange.prevMonthDay,
.dark-mode .flatpickr-day.endRange.prevMonthDay,
.dark-mode .flatpickr-day.selected.nextMonthDay,
.dark-mode .flatpickr-day.startRange.nextMonthDay,
.dark-mode .flatpickr-day.endRange.nextMonthDay {
  border-color: #45619d;
  background: #45619d;
}
.dark-mode .flatpickr-months .flatpickr-prev-month:hover svg,
.dark-mode .flatpickr-months .flatpickr-next-month:hover svg {
  fill: #45619d;
}
.dark-mode .dd-handle {
  color: #c6cadc;
  background: #30323d;
  border-color: #202128;
}
.dark-mode .dd-handle:hover {
  color: #a6adc8;
}
.dark-mode .dd-empty,
.dark-mode .dd-placeholder {
  border-color: #b6c3df;
  background: #2a2b35;
}
.dark-mode table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
  box-shadow: inset 0 0 0 9999px #262830;
}