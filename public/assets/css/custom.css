html body {
  font-family: "Be Vietnam Pro", sans-serif !important;
}
input::placeholder, textarea::placeholder {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.item-main-shadow {
  border-color: #2b7c51;
  box-shadow: 0 10px 40px rgba(14, 166, 59, .2)!important;
}

.top-category--item {
  border-color: #2b7c51;
  box-shadow: 0 10px 40px rgba(14, 166, 59, .2)!important;
  cursor: pointer;
}

.mbbank .top-category--item {
  border-color: #0004cd;
  box-shadow: 0 10px 40px rgba(9, 44, 171, 0.2)!important;
  cursor: pointer;
  border-top: 5px solid #0004cd;
}
.vcb .top-category--item {
  border-top: 5px solid #2b7c51;
}

.mbbank .border-success {
  border-color: #0004cd !important;
}
.vcb .border-success {
  border-color: #2b7c51 !important;
}

.mbbank .bg-success {
  background-color: #0004cd !important;
}
.vcb .bg-success {
  background-color: #2b7c51 !important;
}

/* HTML: <div class="loader"></div> */
.loader {
  width: 50px;
  aspect-ratio: 1.154;
  position: relative;
  background: conic-gradient(from 120deg at 50% 64%,#0000, #308a5a 1deg 120deg,#0000 121deg);
  animation: l27-0 1.5s infinite cubic-bezier(0.3,1,0,1);
  z-index: 99999;
}
.loader:before,
.loader:after {
  content:'';
  position: absolute;
  inset:0;
  background:inherit;
  transform-origin: 50% 66%;
  animation: l27-1 1.5s infinite;
}
.loader:after {
  --s:-1;
}
@keyframes l27-0 {
   0%,30%      {transform: rotate(0)}
   70%         {transform: rotate(120deg)}
   70.01%,100% {transform: rotate(360deg)}
}
@keyframes l27-1 {
   0%      {transform: rotate(calc(var(--s,1)*120deg)) translate(0)}
   30%,70% {transform: rotate(calc(var(--s,1)*120deg)) translate(calc(var(--s,1)*-5px),10px)}
   100%    {transform: rotate(calc(var(--s,1)*120deg)) translate(0)}
}

.progress-limit-package {
  background-image: radial-gradient(300.55% 959.41% at 293.35% -237.5%, #00502F 0%, #0A6F47 67.24%, #629F0D 98.3%);
}

.progress-limit-package.full {
  background-image: radial-gradient(300.55% 959.41% at 293.35% -237.5%, #79111f 0%, #79111f 67.24%, #b81a2f 98.3%);
}

.fancy-card {
  background: radial-gradient(104.28% 319.06% at 97.08% 2.78%, #308a5a 0%, #308a5a 18.53%, rgba(98, 159, 13, .75) 85.34%);
  border-radius: .5rem;
  color: #fff;
  padding: 5px 0px 0px 0px;
}
.bg-header-primary {
  background: radial-gradient(104.28% 319.06% at 97.08% 2.78%, #308a5a 0%, #308a5a 18.53%, rgba(98, 159, 13, .75) 85.34%);
}

.fancy-card .fancy-footer {
  background-color: #0000001a;
  border-bottom-right-radius: .5rem;
  border-bottom-left-radius: .5rem;
  padding: 5px;
}
.fancy-card .fancy-footer a {
  color: #fff;
}

.btn-level-secondary {
  border-width: 1px;
  border-style: solid;
  background-image: linear-gradient(290deg, #EAF6FF 9.78%, #F3FFE9 109.56%);
  color: rgba(7, 76, 49)
}
.btn-level-secondary:hover {
  color: rgba(7, 76, 49)
}

.bg-footer-vcb{
  /* background-position: bottom center; */
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-image: url('/assets/media/backgrounds/wave.svg');
}